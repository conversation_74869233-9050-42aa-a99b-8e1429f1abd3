#Include 'Protheus.ch'


User Function MTIPCBUT()
Local aButtons := {} 
// Botoes a adicionar
aadd(aButtons,{'BUDGETY',{|| U_MarDesK()},'Consulta Aprovacao','Marcar/Desm. Todos'})

Return (aButtons )

User Function MarDesK()

Local nx	:=	0

For nx := 1 to Len(aArrayF4)

Iif(aArrayF4[nx,1] == oNo, aArrayF4[nx,1] := oOk, aArrayF4[nx,1] := oNo)

Next

//oQual:SetArray(aArrayF4)
//oQual:bLDblClick := { || aArrayF4[oQual:nAt,1] := iif(aArrayF4[oQual:nAt,1] == oNo, oOk, oNo) }

Return