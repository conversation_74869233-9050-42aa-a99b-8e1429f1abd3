#Include "rwmake.ch"

/*
Este PE trabalha em conjunto com o PE F460SE1
*/

User Function F460TOK
Local aAlias	:= GetArea()
Local nPosEmit := aScan(aHeader,{|x| AllTrim(x[2])=="E1_EMITCHQ"})
Local nPosHist := aScan(aHeader,{|x| AllTrim(x[2])=="E1_HIST"})
Local nCont

For nCont := 1 to Len (aCols)
	If ! (aCols[nCont,nUsado2+1])
		If Empty(aCols[nCont,nPosEmit])
		   MsgStop("Falta preencher o emitente do Cheque")
			Return .F.
		EndIf
		If Empty(aCols[nCont,nPosHist])
		   MsgStop("Falta preencher o historico do Cheque")
			Return .F.
		EndIf
	EndIf
Next nCont

Return .T.