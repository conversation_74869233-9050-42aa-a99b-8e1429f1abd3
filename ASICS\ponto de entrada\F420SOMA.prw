#Include "totvs.ch"

/*/{Protheus.doc} myFunction
(long_description)
@type    Function
<AUTHOR>
@since   27/03/2019
@version version
@param   param, param_type, param_description
@return  return, return_type, return_description
/*/
User Function F420SOMA()
    Local aAreaAtu := GetArea()
    Local nRet := 0

    IF SE2->E2_MOEDA == 1
        nRet:= SE2->E2_SALDO+SE2->E2_ACRESC-SE2->E2_DECRESC
    ELSE
        nRet:= SE2->E2_VLCRUZ+SE2->E2_ACRESC-SE2->E2_DECRESC     
    ENDIF   

    RestArea(aAreaAtu)
Return nRet
