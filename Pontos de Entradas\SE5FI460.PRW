#Include "rwmake.ch"

// PE PARA ATUALIZACAO COMPLEMENTAR DO SE5 NA LIQUIDACAO
/*
user este pe para alterar o lancamento padrao
*/

User Function SE5FI460()

RecLock("SE5", .F.)
SE5->E5_AGE     := SE1->E1_AGE
SE5->E5_AGELOJA := SE1->E1_AGELOJA
SE5->E5_NOMAGE  := SE1->E1_NOMAGE
If SE5->(FieldPos("E5_NUMLIQ")) > 0
	SE5->E5_NUMLIQ := SE1->E1_NUMLIQ
Endif
If SE5->(FieldPos("E5_NUMBCO")) > 0
	SE5->E5_NUMBCO := SE1->E1_NUMBCO
Endif
If SE5->(FieldPos("E5_NUMSC5")) > 0
	SE5->E5_NUMSC5 := SE1->E1_NUMSC5
Endif
SE5->(MsUnlock())

Return