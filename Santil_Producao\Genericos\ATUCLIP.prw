#include "Tbiconn.ch"
#include "topconn.ch"
#include "rwmake.ch"


User Function ATUCLIP()
 
 Local cRet 		:= ""
 Local cCliente 	:= ""
 Private _numloja

 cConsulta := " SELECT *
 cConsulta += " FROM " + RetSqlName("SA3") + " SA3"
 cConsulta += " WHERE D_E_L_E_T_ = ''  AND A3_TIPO IN  (' ', 'I') AND A3_XLJPAD = '' "
 
 TcQuery cConsulta New Alias '_cQRY'

 cConsulta1 := " SELECT MAX(A3_XLJPAD) NUM
 cConsulta1 += " FROM " + RetSqlName("SA3") + " SA3"
 cConsulta1 += " WHERE D_E_L_E_T_ = ' ' "  //and A3_TIPO = 'I' "
  
 TcQuery cConsulta1 New Alias '_cQ1'

IF	!_cQ1->(Eof())
	_num:=_cQ1->NUM
ENDIF

 While !_cQRY->(Eof())
 
 _num:= Soma1(_num)
 
 DBSELECTAREA("SA3")
 DBSETORDER(1)
 IF DBSEEK(XFILIAL("SA1")+_cQRY->A3_COD)
 	IF EMPTY(SA3->A3_XLJPAD)
 		SA3->(RECLOCK("SA3",.F.))
 		SA3->A3_XLJPAD := ALLTRIM(_num)
    	SA3->(msunlock())
    	_GERASA1(_num)
     ENDIF
 ENDIF
 
 _cQRY->(dbSkip())
 EndDo

 
 
 _cQRY->(dbCloseArea())
 _cQ1->(dbCloseArea())
  
  msgalert("Cliente padrao ja gerado conforme loja do vendedor")
 
 Return   
 
 
 
 static function _GERASA1(_numloja)
 
 DBSELECTAREA("SA1")
 DBSETORDER(1)
 SA1->(RECLOCK("SA1",.T.))
 
 SA1->A1_COD:= "000001"
 SA1->A1_LOJA:=_numloja 
 SA1->A1_NOME:= "CLIENTE PADRAO"
 SA1->A1_NREDUZ:= "CLIENTE PADRAO"
 SA1->A1_EST:= "SP"
 SA1->A1_END:= "RUA"
 SA1->A1_TIPO:= "F" 
 
 SA1->(msunlock())
 
 return
 
 
 