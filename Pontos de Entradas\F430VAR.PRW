#INCLUDE "rwmake.ch" 

** <PERSON><PERSON>o por: <PERSON> - <PERSON>j<PERSON>@gmail.com - Em: 26/03/2006
User Function F430VAR

Local _aValores	:= ParamIxb[01]
Local _nTamParc   := TAMSX3("E2_PARCELA")[1]
Local _nTamTipo   := TAMSX3("E2_TIPO")[1]
/*
aValores := ( { cNumTit, dBaixa, cTipo, cNsNum, nDespes, nDescont,;
nAbatim, nValPgto, nJuros, nMulta, cForne, cOcorr,cCGC, nCM,cRejeicao,xBuffer,cAutentica })
*/ 
If Empty(_aValores[3])
	ParamIxb[01][03] := _aValores[3] := cTipo := "NF"
Endif
If Empty(_aValores[11])
	ParamIxb[01][11] := _aValores[11] := ""
Endif
SE2->( DbSetOrder(1) )
If SE2->( DbSeek( "01" + _aValores[1] + Space(_nTamParc-1) + _aValores[3] + Space(_nTamTipo-2) + _aValores[11] ) )
   cFilAnt := SE2->E2_FILIAL
ElseIf SE2->( DbSeek( "02" + _aValores[1] + Space(_nTamParc-1) + _aValores[3] + Space(_nTamTipo-2) + _aValores[11] ) )
   cFilAnt := SE2->E2_FILIAL
Endif

// 07/11/06 - Passar o valor da despesa para o CNAB
if SE2->E2_ACRESC > 0
   ParamIxb[01][05] := SE2->E2_ACRESC
   ParamIxb[01][08] := ParamIxb[01][08]-SE2->E2_ACRESC
endif
   
Return