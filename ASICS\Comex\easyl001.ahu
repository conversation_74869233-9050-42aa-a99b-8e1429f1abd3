<EASYLINK>
<SERVICE>
<ID>001</ID>
<DATA_SELECTION>
	<FIN_NUM>EECGetFinN()</FIN_NUM>
	<FIN_SEND>
		<FIN_IT>
			<FIN_ELE1>'E1_NUM'</FIN_ELE1>
			<E1_NUM>#TAG FIN_NUM#</E1_NUM>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_PREFIXO'</FIN_ELE1>
			<E1_PREFIXO>'EEC'</E1_PREFIXO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_PARCELA'</FIN_ELE1>
			<E1_PARCELA>' '</E1_PARCELA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_TIPO'</FIN_ELE1>
			<E1_TIPO>If(FindFunction("TETpTitEEQ"),TETpTitEEQ("EEQ"),'RA')</E1_TIPO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_CLIENTE'</FIN_ELE1>
			<E1_CLIENTE>EEQ->EEQ_IMPORT</E1_CLIENTE>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_LOJA'</FIN_ELE1>
			<E1_LOJA>EEQ->EEQ_IMLOJA</E1_LOJA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_EMISSAO'</FIN_ELE1>
			<E1_EMISSAO>dDataBase</E1_EMISSAO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_VENCTO'</FIN_ELE1>
			<E1_VENCTO>IIf (Empty(EEQ->EEQ_VCT),EEQ->EEQ_PGT,EEQ->EEQ_VCT)</E1_VENCTO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_VENCREA'</FIN_ELE1>
			<E1_VENCREA>DataValida(IIf (Empty(EEQ->EEQ_VCT),EEQ->EEQ_PGT,EEQ->EEQ_VCT), .T.)</E1_VENCREA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_VENCORI'</FIN_ELE1>
			<E1_VENCORI>EEQ->EEQ_VCT</E1_VENCORI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_VALOR'</FIN_ELE1>
			<E1_VALOR>EEQ->EEQ_VL</E1_VALOR>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_EMIS1'</FIN_ELE1>
			<E1_EMIS1>dDataBase</E1_EMIS1>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_MOEDA'</FIN_ELE1>
			<E1_MOEDA>Posicione("SYF", 1, xFilial("SYF")+EEQ->EEQ_MOEDA, "YF_MOEFAT")</E1_MOEDA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_TXMOEDA'</FIN_ELE1>
			<E1_TXMOEDA>EEQ->EEQ_TX</E1_TXMOEDA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_VLCRUZ'</FIN_ELE1>
			<E1_VLCRUZ>EEQ->EEQ_EQVL</E1_VLCRUZ>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_PORTADO'</FIN_ELE1>
			<E1_BCOCLI>IF(FindFunction("EasyIntegAdiant"),EasyIntegAdiant('E1_PORTADO'),EEQ->EEQ_BANC)</E1_BCOCLI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_AGEDEP'</FIN_ELE1>
			<E1_BCOCLI>IF(FindFunction("EasyIntegAdiant"),EasyIntegAdiant('E1_AGEDEP'),EEQ->EEQ_AGEN)</E1_BCOCLI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_CONTA'</FIN_ELE1>
			<E1_BCOCLI>IF(FindFunction("EasyIntegAdiant"),EasyIntegAdiant('E1_CONTA'),EEQ->EEQ_NCON)</E1_BCOCLI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'CBCOAUTO'</FIN_ELE1>
			<E1_BCOCLI>IF(FindFunction("EasyIntegAdiant"),EasyIntegAdiant('E1_PORTADO'),EEQ->EEQ_BANC)</E1_BCOCLI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'CAGEAUTO'</FIN_ELE1>
			<E1_BCOCLI>IF(FindFunction("EasyIntegAdiant"),EasyIntegAdiant('E1_AGEDEP'),EEQ->EEQ_AGEN)</E1_BCOCLI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'CCTAAUTO'</FIN_ELE1>
			<E1_BCOCLI>IF(FindFunction("EasyIntegAdiant"),EasyIntegAdiant('E1_CONTA'),EEQ->EEQ_NCON)</E1_BCOCLI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_NATUREZ'</FIN_ELE1>
			<E1_NATUREZ>If(!Empty(Posicione("SA1", 1, xFilial("SA1")+EEQ->(EEQ_IMPORT+EEQ_IMLOJA), "A1_NATUREZ")), Posicione("SA1", 1, xFilial("SA1")+EEQ->(EEQ_IMPORT+EEQ_IMLOJA), "A1_NATUREZ"), GetMv("MV_AVG0178",, "AVG"))</E1_NATUREZ>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_HIST'</FIN_ELE1>
			<E1_HIST>IIF(AllTrim(EEQ->EEQ_FASE) == "E", 'Emb.: ' + EEC->EEC_PREEMB, IIF(AllTrim(EEQ->EEQ_FASE) == "P", 'Ped.: ' + EE7->EE7_PEDIDO, 'Adto. Cliente'))</E1_HIST>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_ORIGEM'</FIN_ELE1>
			<E1_ORIGEM>'sigaeec'</E1_ORIGEM>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<!-- NCF - 28/07/2015 - Inclusao das tags para controle de Acrescimos e Decrescimos na inclusao do titulo do SIGAFIN --> 
		<!--
		<FIN_IT>
			<FIN_ELE1>'E1_ACRESC'</FIN_ELE1>
			<E1_ACRESC>EEQ->EEQ_ACRESC</E1_ACRESC>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_DECRESC'</FIN_ELE1>
			<E1_DECRESC>EEQ->EEQ_DECRES </E1_DECRESC>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
        -->
		<FIN_IT>
			<FIN_ELE1>'E1_MDMULT'</FIN_ELE1>
			<E1_MDMULT>If( AVFLAGS("ACR_DEC_DES_MUL_JUROS_CAMBIO_EXP"), EEQ->EEQ_ACRESC , 0)</E1_MDMULT>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E1_MDDESC'</FIN_ELE1>
			<E1_MDDESC>If( AVFLAGS("ACR_DEC_DES_MUL_JUROS_CAMBIO_EXP"), EEQ->EEQ_DECRES, 0)</E1_MDDESC>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		</FIN_SEND>
</DATA_SELECTION>
<DATA_SEND>
	<SEND>EECINFIN(#TAG FIN_SEND#, 'SE1', 'INCLUIR',,,#TAG FIN_NUM#)</SEND>
</DATA_SEND>
<DATA_RECEIVE>
	<SRV_STATUS>.T.</SRV_STATUS>
</DATA_RECEIVE>
</SERVICE>
</EASYLINK>
