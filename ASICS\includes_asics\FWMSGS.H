//----------------------------------------------------------------------------//
// New messages defined for FiveWin

#define FM_CLICK       WM_USER+1024

#define FM_SCROLLUP    WM_USER+1025
#define FM_SCROLLDOWN  WM_USER+1026
#define FM_SCROLLPGUP  WM_USER+1027
#define FM_SCROLLPGDN  WM_USER+1028

#define FM_CHANGE      WM_USER+1029
#define FM_COLOR       WM_USER+1030
#define FM_MEASURE     WM_USER+1031
#define FM_DRAW        WM_USER+1032
#define FM_LOSTFOCUS   WM_USER+1033
#define FM_THUMBPOS    WM_USER+1034

#define FM_CLOSEAREA   WM_USER+1035

#define FM_VBXEVENT    WM_USER+1036
#define FM_HELPF1      WM_USER+1037

#define FM_THUMBTRACK  WM_USER+1038

#define FM_DROPOVER    WM_USER+1039
#define FM_CHANGEFOCUS WM_USER+1040

#define WM_ASYNCSELECT WM_USER+1041

//----------------------------------------------------------------------------//
