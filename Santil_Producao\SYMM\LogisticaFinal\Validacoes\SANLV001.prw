#Include 'Protheus.ch'
#INCLUDE "TOPCONN.CH"
//s
User Function SANLV001(cPedido,cId)
	Local cQuery 		:= ""
	Local cAlias 		:= GetNextAlias()
	Local _nRec			:= 0
	Local lRet			:= .T.

	cQuery	 :=	 " SELECT F2_CHVNFE FROM "+RetSqlName("SC9")+" SC9
	cQuery	 +=	 " LEFT JOIN "+RetSqlName("SF2")+" SF2 ON SF2.D_E_L_E_T_<>'*' AND SF2.F2_DOC = SC9.C9_NFISCAL AND SF2.F2_SERIE = SC9.C9_SERIENF
	cQuery	 +=	 " WHERE SC9.D_E_L_E_T_<>'*' AND SC9.C9_FILIAL = '"+xFilial("SC9")+"' AND SC9.C9_PEDIDO = '"+cPedido+"' AND SC9.C9_XID = '"+cId+"'

	TCQUERY cQuery NEW ALIAS (cAlias)

	Count to _nRec //quantidade registros
	(cAlias)->(DbGotop())

	While (cAlias)->(!EOF())

		If Empty((cAlias)->F2_CHVNFE)

			lRet := .F.
			exit

		EndIf

		(cAlias)->(DbSkip())

	EndDo
	(cAlias)->(dbCloseArea())

Return(lRet)

