#ifdef SPANISH
	#define STR0001"Numero de la OT"
	#define STR0002"Fecha de emision"
	#define STR0003"Cliente"
	#define STR0004"Buscar"
	#define STR0005"Visualizar"
	#define STR0006"Incluir"
	#define STR0007"Modificar"
	#define STR0008"Borrar"
	#define STR0009"Imprimir"
	#define	STR0010"Leyenda"
	#define	STR0011"Aprobar"
	#define	STR0012"Justificar"
	#define	STR0013"Transf.Aut."
	#define	STR0014"Aprob.Aut."
	#define	STR0015"Orden de servicio - Transferencia"
	#define	STR0016"Orden de servicio vinculada a la factura de entrada."
	#define	STR0017"Por favor, borre la factura"
	#define	STR0018"Orden de servicio finalizada."
	#define	STR0019"ANALITICO"
	#define	STR0020"Movimiento de producto [F4]"
	#define	STR0021"Impresion de la transferencia [F5]"
	#define	STR0022"CLIPS"
	#define	STR0023"Carga tiendas"
	#define	STR0024"Encabezado de la orden de servicio"
	#define	STR0025"Items de la orden de servicio"
	#define	STR0026"Numero"
	#define	STR0027"Fecha de emision"
	#define	STR0028"Observaciones"
	#define	STR0029"Comprador"
	#define	STR0030"Aprobador"
	#define	STR0031"Fecha de aprobacion"
	#define	STR0032"TOTALES POR SUCURSAL"
	#define	STR0033"SUCURSALES"
	#define	STR0034"Producto:"
	#define	STR0035"Precio de venta:"
	#define	STR0036"Ultimo precio de compra:"
	#define	STR0037"DISTRIBUCION"
	#define	STR0038"DISTRIBUCION ENTRE SUCURSALES"
	#define	STR0039"SALDOS"
	#define	STR0040"Grilla"
	#define	STR0041"GRILLAS/PACKS"
	#define	STR0042"Por favor espere, borrando la orden de servicio"
	#define	STR0043"Inicio de la grabacion de la OS:"
	#define	STR0044"Por favor espere, actualizando la orden de servicio"
	#define	STR0045"Inicio de la grabacion de la reserva de la OS:"
	#define	STR0046"Final de la grabacion de la reserva de la OS:"
	#define	STR0047"Final de la grabacion de la OS:"
	#define	STR0048"Inclusion"
	#define	STR0049"Modificacion"
	#define	STR0050"Borrado"
	#define	STR0051"Complete los campos del encabezado."
	#define	STR0052"es necesario completar la sucursal de origen."
	#define	STR0053"es necesario completar la sucursal de destino."
	#define	STR0054"La Sucursal de destino debe ser diferente de la Sucursal de origen."
	#define	STR0055"Informe las cantidades de la grilla."
	#define	STR0056"El saldo por tranferir de la tienda"
	#define	STR0057" es mayor que el saldo disponible."
	#define	STR0058"Complete el precio de costo del producto."
	#define	STR0059"Complete la referencia del producto."
	#define	STR0060"Complete la grilla del producto."
	#define	STR0061"Complete el color del producto."
	#define	STR0062"Confirma"
	#define	STR0063" ¿De la orden de servicio?"
	#define	STR0064"Orden de servicio"
	#define	STR0065"Producto/SKU no registrado."
	#define	STR0066"Saldo indisponible"
	#define	STR0067"Este SKU no existe ["
	#define	STR0068"Sucursal de origen"
	#define	STR0069"Stock"
	#define	STR0070"Sucursal de destino"
	#define	STR0071"Cantidad"
	#define	STR0072"Valor total"
	#define	STR0073"Grilla."
	#define	STR0074"Ref."
	#define	STR0075"Referencia"
	#define	STR0076"Color."
	#define	STR0077"Descripcion"
	#define	STR0078"GRILLA"
	#define	STR0079"Total"
	#define	STR0080"Prec. Compra"
	#define	STR0081"Suc. Origen"
	#define	STR0082"¡Este color ya se informo!"
	#define	STR0083"Informe la grilla."
	#define	STR0084"Informe la referencia del producto."
	#define	STR0085"Informe los colores."
	#define	STR0086"Es necesario informar el precio de costo."
	#define	STR0087"Informe las cantidades de la grilla."
	#define	STR0088"Nombre"
	#define	STR0089"Total R$"
	#define	STR0090"Pendiente"
	#define	STR0091"Esperando aprobacion"
	#define	STR0092"Anulado"
	#define	STR0093"OS de Fact. de entrada"
	#define	STR0094"Leyenda"
	#define	STR0095"Por favor espere, generando la reserva de los productos"
	#define	STR0096"¿Desea aprobar esta orden de servicio?"
	#define	STR0097"Inicio de la aprobacion de la OS:"
	#define	STR0098"Por favor espere, generando la aprobacion"
	#define	STR0099"Final de la aprobacion de la OS:"
	#define	STR0100"&Confirmar"
	#define	STR0101"Consulta estandar"
	#define	STR0102"Referencia"
	#define	STR0103"Color"
	#define	STR0104"Es necesario informar el codigo del producto."
	#define	STR0105"Por favor espere, cargando saldos de las tiendas"
	#define	STR0106"Refer."
	#define	STR0107"Desc."
	#define	STR0108"Cant."
	#define	STR0109"YA SE GRABO"
	#define	STR0110"Anulado por:"
	#define	STR0111"Solo es posible justificar una orden de servicio finalizada."
	#define	STR0112"¿Desea justificar la orden de servicio?"
	#define	STR0113"Justificacion de la orden"
	#define	STR0114"Digite el motivo"
	#define	STR0115"Por favor espere, generando la justificacion"
	#define	STR0116"No fue posible anular la transferencia, ya existen facturas generadas."
	#define	STR0117"Tienda origen"
	#define	STR0118"Tienda destino"
	#define	STR0119"Es necesario informar la tienda origen."
	#define	STR0120"Es necesario informar la tienda destino."
	#define	STR0121"La tienda de origen debe ser diferente de la tienda destino."
	#define	STR0122"Existen ordenes de servicio pendientes:"
#else
	#ifdef ENGLISH
		#define STR0001"OT Number"
		#define STR0002"Issue Date"
		#define STR0003"Customer"
		#define STR0004"Search"
		#define STR0005"View"
		#define STR0006"Add"
		#define STR0007"Edit"
		#define STR0008"Delete"
		#define STR0009"Print"
		#define STR0010"Caption"
		#define STR0011"Approve"
		#define STR0012"Justify"
		#define STR0013"Auto Transfer"
		#define STR0014"Auto Approval"
		#define STR0015"Service Order - Transfer"
		#define STR0016"Service order linked to inflow invoice."
		#define STR0017"Please, delete the invoice."
		#define STR0018"Service Order concluded!"
		#define STR0019"DETAILED"
		#define STR0020"Product Operation [F4]"
		#define STR0021"Transfer Printing [F5]"
		#define STR0022"CLIPS"
		#define STR0023"Load Unit"
		#define STR0024"Service Order Header"
		#define STR0025"Service Order Items"
		#define STR0026"Number"
		#define STR0027"Issue Date"
		#define STR0028"Notes"
		#define STR0029"Buyer"
		#define STR0030"Approver"
		#define STR0031"Approval Date"
		#define STR0032"TOTAL PER BRANCH"
		#define STR0033"BRANCHES"
		#define STR0034"Product:"
		#define STR0035"Sales Price:"
		#define STR0036"Purchase Last Price:"
		#define STR0037"DISTRIBUTION"
		#define STR0038"DISTRIBUTION BETWEEN BRANCHES"
		#define STR0039"BALANCES"
		#define STR0040"GRID"
		#define STR0041"GRID/PACKS"
		#define STR0042"Please wait, deleting Service Order."
		#define STR0043"Beginning of Service Order saving:"
		#define STR0044"Please wair, updating Service Order."
		#define STR0045"Beginning of SO Saving Reservation: "
		#define STR0046"End of SO Saving Reservation: "
		#define STR0047"End of SO Saving: "
		#define STR0048"Addition"
		#define STR0049"Edition"
		#define STR0050"Deletion"
		#define STR0051"Fill in the header fields."
		#define STR0052"mandatory to fill in the Source Branch."
		#define STR0053"mandatory to fill in the Target Branch."
		#define STR0054"Target branch must be different from the source branch."
		#define STR0055"Enter the Grid quantity."
		#define STR0056"Balance to transfer from unit"
		#define STR0057"is greater than the available balance."
		#define STR0058"Fill in the product cost price."
		#define STR0059"Fill in the product reference."
		#define STR0060"Fill in the product grid."
		#define STR0061"Fill in the product colour."
		#define STR0062"Confirm"
		#define STR0063"the Service Order?"
		#define STR0064"Service Order"
		#define STR0065"Product/SKU not registered!"
		#define STR0066"Balance not available."
		#define STR0067"This SKU does not exist ["
		#define STR0068"Source Branch"
		#define STR0069"Stock"
		#define STR0070"Target Branch"
		#define STR0071"Quantity"
		#define STR0072"Aggregate Value"
		#define STR0073"Grid"
		#define STR0074"Ref."
		#define STR0075"Reference"
		#define STR0076"Colour"
		#define STR0077"Description"
		#define STR0078"GRID"
		#define STR0079"Total"
		#define STR0080"Purchase Prc."
		#define STR0081"Source Branch"
		#define STR0082"Colour already informed!"
		#define STR0083"Enter the Grid."
		#define STR0084"Enter the Product Reference."
		#define STR0085"Enter the Colour."
		#define STR0086"mandatory to enter the cost price."
		#define STR0087"Enter the Grid quantity."
		#define STR0088"Name"
		#define STR0089"Total R$"
		#define STR0090"Pending"
		#define STR0091"Waiting for Approval"
		#define STR0092"Cancelled"
		#define STR0093"Inflow Invoice Service Order"
		#define STR0094"Caption"
		#define STR0095"Please wait, generating product reservation."
		#define STR0096"Do you want to approve thsi service order?"
		#define STR0097"Beginning of Service Order saving:"
		#define STR0098"Please wait, generating approval."
		#define STR0099"End of Service Order approval:"
		#define STR0100"&Confirm"
		#define STR0101"Standard Query"
		#define STR0102"Reference"
		#define STR0103"Colour"
		#define STR0104"mandatory to enter the product code."
		#define STR0105"Please wait, loading unit balances."
		#define STR0106"Ref."
		#define STR0107"Desc."
		#define STR0108"Qtty."
		#define STR0109"ALREADYSAVED"
		#define STR0110"Cancelled by:"
		#define STR0111"You can only justify a cancelled service order."
		#define STR0112"Do you want to justify the service order?"
		#define STR0113"Order Justification"
		#define STR0114"Enter the reason."
		#define STR0115"Please wait, generating justification."
		#define STR0116"Unable to cancel transfer, as there are invoices already generated."
		#define STR0117"Source Unit"
		#define STR0118"Target Unit"
		#define STR0119"mandatory to enter the source unit."
		#define STR0120"mandatory to enter the target unit."
		#define STR0121"Souce unit must be different from target unit."
		#define STR0122"There are pendeing Service Orders:"
	#else
		#define STR0001"Numero da OT"
		#define STR0002"Data Emissao"
		#define STR0003"Cliente"
		#define STR0004"Pesquisar"
		#define STR0005"Visualizar"
		#define STR0006"Incluir"
		#define STR0007"Alterar"
		#define STR0008"Excluir"
		#define STR0009"Imprimir"
		#define STR0010"Legenda"
		#define STR0011"Aprovar"
		#define STR0012"Justificar"
		#define STR0013"Transf.Aut."
		#define STR0014"Aprov.Aut."
		#define STR0015"Ordem de Servico - Transferencia"
		#define STR0016"Ordem de servico vinculada a nota fiscal de entrada."
		#define STR0017"Por favor, exclua a nota fiscal"
		#define STR0018"Ordem de Servico Encerrada!"
		#define STR0019"ANALITICO"
		#define STR0020"Movimentacao de Produto [F4]"
		#define STR0021"Impressao da Transferencia [F5]"
		#define STR0022"CLIPS"
		#define STR0023"Carrega Lojas"
		#define STR0024"Cabecalho da Ordem de Servico"
		#define STR0025"Itens da Ordem de Servico"
		#define STR0026"Numero"
		#define STR0027"Data de Emissao"
		#define STR0028"Observacoes"
		#define STR0029"Comprador"
		#define STR0030"Aprovador"
		#define STR0031"Data Aprovacao"
		#define STR0032"TOTAIS POR FILIAL"
		#define STR0033"FILIAIS"
		#define STR0034"Produto:"
		#define STR0035"Preco de Venda:"
		#define STR0036"Ultimo Preco de Compra:"
		#define STR0037"DISTRIBUICAO"
		#define STR0038"DISTRIBUIcaO ENTRE FILIAIS"
		#define STR0039"SALDOS"
		#define STR0040"GRADE"
		#define STR0041"GRADES/PACKS"
		#define STR0042"Por favor aguarde, excluindo Ordem de Servico"
		#define STR0043"Inicio da gravacao da OS:"
		#define STR0044"Por favor aguarde, atualizando Ordem de Servico"
		#define STR0045"Inicio da Gravacao da Reserva da OS:"
		#define STR0046"Fim da Gravacao da Reserva da OS:"
		#define STR0047"Fim da Gravacao da OS:"
		#define STR0048"Inclusao"
		#define STR0049"Alteracao"
		#define STR0050"Exclusao"
		#define STR0051"Preencha os campos do Cabecalho."
		#define STR0052"e necessario preencher a Filial de Origem."
		#define STR0053"e necessario preencher a Filial de Destino."
		#define STR0054"A Filial de Destino deve ser diferente da Filial de Origem."
		#define STR0055"Informe as quantidades da Grade."
		#define STR0056"O saldo a tranferir da loja"
		#define STR0057" e maior que o saldo disponivel."
		#define STR0058"Preencha o preco de custo do produto."
		#define STR0059"Preencha a referencia do produto."
		#define STR0060"Preencha a grade do produto."
		#define STR0061"Preencha a cor do produto."
		#define STR0062"Confirma"
		#define STR0063" da Ordem de Servico ?"
		#define STR0064"Ordem de Servico"
		#define STR0065"Produto/SKU nao cadastrado!"
		#define STR0066"Saldo Indisponivel"
		#define STR0067"Este SKU nao existe ["
		#define STR0068"Filial de Origem"
		#define STR0069"Estoque"
		#define STR0070"Filial de Destino"
		#define STR0071"Quantidade"
		#define STR0072"Valor Total"
		#define STR0073"Grade."
		#define STR0074"Ref."
		#define STR0075"Referencia"
		#define STR0076"Cor."
		#define STR0077"Descricao"
		#define STR0078"GRADE"
		#define STR0079"Total"
		#define STR0080"Prc Compra"
		#define STR0081"Fil Origem"
		#define STR0082"Esta cor ja foi informada!"
		#define STR0083"Informe a Grade."
		#define STR0084"Informe a Referencia do Produto."
		#define STR0085"Informe as Cores."
		#define STR0086"e necessario informar o preco de custo."
		#define STR0087"Informe as quantidades da Grade."
		#define STR0088"Nome"
		#define STR0089"Total R$"
		#define STR0090"Pendente"
		#define STR0091"Aguardando Aprovacao"
		#define STR0092"Cancelado"
		#define STR0093"OS de NFE"
		#define STR0094"Legenda"
		#define STR0095"Por favor aguarde, gerando a reserva dos produtos"
		#define STR0096"Deseja aprovar essa ordem de servico ?"
		#define STR0097"Inicio da Aprovacao da OS:"
		#define STR0098"Por favor aguarde, gerando a aprovacao"
		#define STR0099"Fim da Aprovacao da OS:"
		#define STR0100"&Confirmar"
		#define STR0101"Consulta Padrao"
		#define STR0102"Referencia"
		#define STR0103"Cor"
		#define STR0104"e necessario informar o codigo do produto."
		#define STR0105"Por favor aguarde, carregando saldos das lojas"
		#define STR0106"Refer."
		#define STR0107"Desc."
		#define STR0108"Qtd."
		#define STR0109"JAGRAVADO"
		#define STR0110"Cancelado por:"
		#define STR0111"Somente e possivel justificar uma ordem de servico encerrada."
		#define STR0112"Deseja justificar a ordem de servico ?"
		#define STR0113"Justificativa da Ordem"
		#define STR0114"Digite o Motivo"
		#define STR0115"Por favor aguarde, gerando a justificacao"
		#define STR0116"Nao foi possivel cancelar a transferencia, ja existem notas fiscais geradas."
		#define STR0117"Loja Origem"
		#define STR0118"Loja Destino"
		#define STR0119"e necessario informar a loja origem."
		#define STR0120"e necessario informar a loja destino."
		#define STR0121"A loja de origem deve ser diferente da loja destino."
		#define STR0122"Existem Ordem de servicos pendentes:"
	#endif
#endif
