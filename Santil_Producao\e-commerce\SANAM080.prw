#INCLUDE 'PROTHEUS.CH'
#Include 'TopConn.ch'


/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Program  SANAM080         �Owner  �<PERSON>� Date �  25/06/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �Method  que para importa��o de arquivo de concilia��o		  ���
���           Apenas traremos os Pedidos com Status NOVO no JET COMMERCE  ���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/

User Function SANAM080()

Private aLogFull := {}

Processa( {|| ProcesConcCielo() },"Aguarde , Selecionando Rotina de Conciliação Cielo" )

Return aLogFull


Static Function ProcesConcCielo()

Local nTamFile   := 0
Local nTamLin    := 680
Local nBtLidos   := 0
Local cBuffer   := ""
Local cFileOpen := ""
Local cTitulo1 := "Selecione o arquivo de Retorno Cielo (*.CSV)"
Local cExtens       := "Arquivo CSV | *.csv"
Local nTam
Local nItem       := 0
Local aVector := {}
Local aOrders := {}
Local nContLine := 0
Local lBaixa := .F.
Local oWSexportOrderResult := NIL
Local cUserWs  := ""
Local cSenhaWs := ""
Local aBuffer  := {}
Local aPedidos := {}
Local oWSreturnTransactionReceiptCieloResult := Nil
Local n

If nAmbiente = 1 //Produ��o
	cUserWs  := GetMV("ES_USERJET")
	cSenhaWs := GetMV("ES_PSWDJET")
ElseIf nAmbiente = 2 //Homologa��o
	cUserWs  := GetMV("ES_HUSERJE")
	cSenhaWs := GetMV("ES_HPSWDJE")
EndIf


cFileOpen := cGetFile(cExtens,cTitulo1,,"C:\Users\<USER>\Desktop\TOTVS\CLIENTES\SANTIL\TXT",.T.)



If !File(cFileOpen)
     MsgAlert("Arquivo não localizado")
     Return
Endif


FT_FUSE(cFileOpen)                //ABRIR
FT_FGOTOP()                       //PONTO NO TOPO
ProcRegua(FT_FLASTREC())   		  //QTOS REGISTROS LER

For n=1 to 100

	While !FT_FEOF()

		 nContLine ++
	     ProcRegua(nContLine)

	     If nContLine >= 4 //passa pelo cabe�alho

		     IncProc()
		     cBuffer := FT_FREADLN()

     	     aBuffer := StrTokArr(cBuffer,";")

     	     cTIDarq 	:= ALLTRIM(SUBSTR(aBuffer[5],1,20))
     	     cNSU	 	:= ALLTRIM(aBuffer[6])
     	     cAutoriz	:= ALLTRIM(SUBSTR(aBuffer[7],1,6))
     	     cDataVenda := ALLTRIM(SUBSTR(aBuffer[1],1,10))

     	     nValorTotalArq  :=  REPLACE(aBuffer[8],',','.')
     	     nValorTotalArq  := (VAL(nValorTotalArq)) //Sempre diminui 1 centavo

			 //cTidTabela   := BuscaTIDSl4("3",cTIDarq)
			 cDataVenda   := SUBSTR(aBuffer[1],7,LEN(aBuffer[2])) + SUBSTR(aBuffer[1],4,2) + SUBSTR(aBuffer[1],1,2)
     	     cDataVenc	  := SUBSTR(aBuffer[2],7,LEN(aBuffer[2])) + SUBSTR(aBuffer[2],4,2) + SUBSTR(aBuffer[2],1,2)

     	     nValorParcelaArq:=  REPLACE(aBuffer[9],',','.')
     	     //nValorParcelaArqMenor:= (VAL(nValorParcelaArq) - 0.05) //Sempre diminui 5 centavo
     	     //nValorParcelaArqMaior:= (VAL(nValorParcelaArq) + 0.05) //Sempre soma 5 centavo

     	     //Busca no ws os dados necess�rios do pedido para inclus�o de titulo SE1
			 oWSexportOrderResult := WSWSJET():New()
			 oWSexportOrderResult:EXPORTORDER(cUserWs,cSenhaWs,"TID",cTIDarq,3,"E")
			 aOrders:= oWSexportOrderResult:OWSEXPORTORDERRESULT:OWSORDER

			If Len(aOrders) >0

			 	nIdOrder   := aOrders[1]:NIDORDER
			 	//cIdCliente := BuscaCliente("1",aOrders[1]:OWSCLIENT:NIDCLIENT)
				//cLjCliente := BuscaCliente("2",aOrders[1]:OWSCLIENT:NIDCLIENT)
				nValorTotal    := CVALTOCHAR(aOrders[1]:NTOTAL)
				nValorParcela  := CVALTOCHAR(aOrders[1]:OWSORDERPAYMENT:NVALUEPARCEL)


			    If nValorTotalArq = nValorTotal

					//Verifica se pedido tem status NOVO
                    If BuscaStatusPed(nIdOrder) = .T.

					    //Guarda na tabela PB5 o ID pedido Jet + TID + NSU para concilia��o posterior
					    IncTidNsu(nIdOrder, cTIDarq, cNSU, cAutoriz, cDataVenda)

	                    //cria o titulo se necess�rio
						//IncluiTitRec(cIdCliente, cLjCliente, nValorParcela, cDataVenc)

	                    //Altera o Status para Aprovado
						AltXera(cVALTOCHAR(nIdOrder))

						//Else

						//cNota      := BuscaTIDSl4("1",cTIDarq)
			        	//cSerie     := BuscaTIDSl4("2",cTIDarq)

						//baixa o titulo se necess�rio
						//BaixaTitRec(cIdCliente, cLjCliente, nValorParcela, cDataVenc , cNota, cSerie)

					Else
						lBaixa := .T.
					EndIf

				Else
					U_SANAM091("1","Conciliação Cielo","Pedido "+cValToChar(nIdOrder)+" do TID "+cTIDarq+" que consta no arquivo CIELO não tem valores totais iguais ao pedido do site, Verifique")
					lBaixa := .T.
				Endif

	   		Else
		   		U_SANAM091("1","Conciliação Cielo","Pedido do TID "+cTIDarq+" que consta no arquivo CIELO não foi encontrados na JET via webservice, Verifique")
		   		lBaixa := .T.
	   		EndIF

	       If !lBaixa
	     	 //ADICIONA LOG NA TELA
	     	 AADD(aLogFull,{"Conciliação Cielo", nValorParcela, cDataVenc })
		   EndIf

	     EndIf


	    FT_FSKIP()
	endDo

Next n1

FT_FUSE()
MsgInfo("Leitura Finalizada")
Return Nil



Static Function BaixaTitRec(cIdClient, cLjClient, nValorParcela, cDataVenc , cNota, cSerie)

Local aArray 		:= {}
Local _cTitl 		:= GetSx8Num("SE1","E1_NUM")
Local cPrefixo  	:= "JET"
Local lRet			:= .F.
Local aBaixa 		:= {}
Local cHist			:= "Baixa para compra via e-commerce com cartão de crédito Cielo"
Local nDifDataVenc := 0
Private lMsHelpAuto := .T. // Variavel de controle interno do ExecAuto
Private lMsErroAuto := .F.  // Variavel que informa a ocorr�ncia de erros no ExecAuto

//Indica inclus�o
lMsHelpAuto := .T.
lMsErroAuto := .F.

//Busca
DBSELECTAREA("SE1")
DbSetOrder(29)
If SE1->(DbSeek(xFilial("SE1")+cNota+cSerie))

	WHILE !SE1->(EOF()) .AND. SE1->E1_NUMNOTA == cNota .AND. SE1->E1_SERIE == cSerie

		nDifDataVenc := (CTOD(SUBSTRING(cDataVenc,7,2) +"/"+ SUBSTRING(cDataVenc,5,2) +"/"+ SUBSTRING(cDataVenc,1,4)) - SE1->E1_VENCTO)

		IF (nDifDataVenc >= 0 .and. nDifDataVenc <5) .AND. EMPTY(SE1->E1_BAIXA)

              aBaixa := {}
             	  Aadd(aBaixa, {"E1_PREFIXO"   , SE1->E1_PREFIXO    , Nil})
                  Aadd(aBaixa, {"E1_NUM"       , SE1->E1_NUM        , Nil})
                  Aadd(aBaixa, {"E1_PARCELA"   , SE1->E1_PARCELA    , Nil})
                  Aadd(aBaixa, {"E1_TIPO"      , SE1->E1_TIPO       , Nil})
                  Aadd(aBaixa, {"E1_CLIENTE"   , SE1->E1_CLIENTE    , Nil})
                  Aadd(aBaixa, {"E1_LOJA"      , SE1->E1_LOJA       , Nil})
                  Aadd(aBaixa, {"AUTJUROS"     , 0  	            , Nil})
                  Aadd(aBaixa, {"AUTMULTA"     , 0	                , Nil})
                  Aadd(aBaixa, {"AUTVALREC"    , SE1->E1_VALOR      , Nil})
                  Aadd(aBaixa, {"AUTMOTBX"     , "FAT"              , Nil})
                  Aadd(aBaixa, {"AUTDTBAIXA"   , dDataBase	        , Nil})
                  Aadd(aBaixa, {"AUTHIST"      , cHist              , Nil})

              MSEXECAUTO({|x,y| Fina070(x,y)}, aBaixa, 3)

              If lMsErroAuto
				 //Cadastra ocorrencia de erro
				 DisarmTransaction()
				 RollBackSx8()
				 lRet := .F.
			 	 U_SANAM091("2","Conciliação Bradesco: E1_NUM = "+ ALLTRIM(SE1->E1_NUM) ,cMsg)
		 	 Else
			 	 U_SANAM101("2", ALLTRIM(SE1->E1_NUM), "1")
		 	 EndIf

		EndIf

	SE1->( dbSkip() )
	EndDo

Else
	U_SANAM091("1","Conciliação Bradesco","Titulo SE1 com nota "+cNota+" e série "+cSerie+" não foram encontrados na base. Verifique se a venda SL1 foi criada corretamente")
EndIf

Return lRet



Static Function BuscaFormaPgto(cForma)

Local cRet := ""

If cForma = SUBSTR("Boleto Bancário - Bradesco",1,10)
	cRet := "BOL_BRA"
ElseIf cForma = SUBSTR("Pagamento Fácil - Bradesco",1,10)
	cRet := "TR"
ElseIf cForma = SUBSTR("American Express",1,10)
	cRet := "CC"
ElseIf cForma = SUBSTR("Mastercard Crédito",1,10)
	cRet := "CC"
ElseIf cForma = SUBSTR("Visa Crédito",1,10)
	cRet := "CC"
ElseIf cForma = SUBSTR("Visa Debito",1,10)
	cRet := "CD"
ElseIf cForma = SUBSTR("Itaú Shopline",1,10)
	cRet := "BOL_ITAU"
EndIf

Return cRet


/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Program  AlteraStatusJet  �Owner  �Felipe Santos� Date �  28/05/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �Method  AUTOMATIC that import orders      				  ���
�� 					  of E-commerce JETCOMMERCE 						  ���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
Static Function AlteraStatusJet(nIdOrder)
Local oWSalterOrderStateCompleteResult := NIL
Local cUserWs  := ""
Local cSenhaWs := ""

If nAmbiente = 1 //Produ��o
	cUserWs  := GetMV("ES_USERJET")
	cSenhaWs := GetMV("ES_PSWDJET")
ElseIf nAmbiente = 2 //Homologa��o
	cUserWs  := GetMV("ES_HUSERJE")
	cSenhaWs := GetMV("ES_HPSWDJE")
EndIf


IF nIdOrder > 0

	//Envia o novo status do pedido para o JET COMMERCE via WS
	//INSTANCIANDO A WEBSERVICE WSWJET (JETCOMMERCE)
	oWSalterOrderStateCompleteResult := WSWSJET():New()

	oWSalterOrderStateCompleteResult:alterOrderStateComplete(cUserWs,cSenhaWs,nIdOrder,"5719","Alteração via Protheus - Novo p/ Aprovado"  )

ENDIF

Return




/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Program  IncTidNsu	      Owner  �Felipe Santos� Date �  28/05/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �BUSCA TID 												  ���
�� 					  sl1, sl2 e sl4             						  ���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
Static Function IncTidNsu(nOrder, cTID, cNsu, cAutoriz, cDataVenda)
Local lRet      := .T.

DBSELECTAREA("PB5")
DBSETORDER(1)

cDataVenda := STOD(cDataVenda)

RecLock("PB5",.T.)
PB5->PB5_FILIAL	 := XFILIAL('PB5')
PB5->PB5_IDJET 	 := cValToChar(nOrder)
PB5->PB5_TIDJET	 := Alltrim(cTID)
PB5->PB5_NSUJET	 := STRZERO(VAL(cNsu),6)
PB5->PB5_DOCJET	 := STRZERO(VAL(cNsu),9)
PB5->PB5_AUTORIZ := Alltrim(cAutoriz)

PB5->PB5_DATATE   := cDataVenda
PB5->PB5_HORATE   := "00:00"
PB5->PB5_TIPCAR   := "Magnetico"
PB5->PB5_VENDTE   := "S"
PB5->PB5_FORMAI   := ""
PB5->PB5_PARCTE   := ""

PB5->(MsUnLock())


Return lRet


/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Program  AltXera             �Owner  �Felipe Santos� Date �  28/05/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �Method  MANUAL that export status order				  ���
�� 					  of E-commerce JETCOMMERCE 						  ���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
Static Function AltXera(nOrderJet, cStatusAt)

Local oWSalterOrderStateResult := Nil
Local cUserWs  := ""
Local cSenhaWs := ""
Local aOcorr := {}

cUserWs  := GetMV("ES_USERJET")
cSenhaWs := GetMV("ES_PSWDJET")


//Envia o novo status do pedido para o JET COMMERCE via WS
//INSTANCIANDO A WEBSERVICE WSWJET (JETCOMMERCE)
oWSalterOrderStateCompleteResult := WSWSJET():New()

IF !oWSalterOrderStateCompleteResult:alterOrderStateComplete(cUserWs,cSenhaWs,VAL(nOrderJet),5719,"Alteração via Protheus Após conciliação"  )
	aadd(aOcorr, {AllTrim("Id Order Manual: "+nOrderJet +" Erro:"+ GetWSCError()), "" ,  GetWSCError()})
	GetWSCError()

ELSE
	U_SANAM091("1","Conciliação Cielo","Pedido "+cValToChar(nOrderJet)+" foi alterado para Aprovado")
ENDIF


Return







/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Program  BuscaStatusPedido Owner  �Felipe Santos� Date �  28/05/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �Metodo que far� a venda assistida, criando or�amento		  ���
�� 					  sl1, sl2 e sl4             						  ���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
Static Function  BuscaStatusPed(nOrder)

Local oWSexportOrderStateResult := NIL
Local cUserWs  := ""
Local cSenhaWs := ""
Local aOrdersState := {}
Local lStatNovo := .F.

If nAmbiente = 1 //Produ��o
	cUserWs  := GetMV("ES_USERJET")
	cSenhaWs := GetMV("ES_PSWDJET")
ElseIf nAmbiente = 2 //Homologa��o
	cUserWs  := GetMV("ES_HUSERJE")
	cSenhaWs := GetMV("ES_HPSWDJE")
EndIf

//ORDER STATUS
oWSexportOrderStateResult := WSWSJET():New()

oWSexportOrderStateResult:EXPORTORDERSTATE(cUserWs,cSenhaWs,nOrder,Nil)
aOrdersState:= oWSexportOrderStateResult:OWSEXPORTORDERSTATERESULT:OWSORDERSTATE

lStatNovo := iif(ALLTRIM(STR(aOrdersState[1]:NIDORDERSTATE))="5717",.T.,.F.)

If !lStatNovo
	lStatNovo := iif(ALLTRIM(STR(aOrdersState[1]:NIDORDERSTATE))="5718",.T.,.F.)
EndIf

Return lStatNovo


