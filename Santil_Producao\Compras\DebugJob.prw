#INCLUDE 'Protheus.ch'
#INCLUDE 'TOPConn.ch'
#DEFINE DIRXML  "XMLNFE\"
#DEFINE DIRALER "NEW\"
#DEFINE DIRLIDO "OLD\"
#DEFINE DIRERRO "ERR\" 

User Function DebugJob()

Local cADVPLFun := "SchedComCol"
Local cEnvironment := "teste3"
Local lWaitRun := .T.
Local aParam := {}

                                                	
AAdd(aParam,"01")                                
AAdd(aParam,"0001")

//MATA140I(aParam)
u_SchedComCol(aParam)
 //ImpXML_NFs(,.F.)                                                         
//StartJob( cADVPLFun , cEnvironment , lWaitRun , aParam  )

Return 