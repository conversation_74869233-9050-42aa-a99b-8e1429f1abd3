#INCLUDE 'RWMAKE.CH'

User Function M261BCHOI
Local _aMyBtn := {}
Local _cEnv := Alltrim(Upper(GetEnvServer()))

// If "T060801" $ _cEnv
Aadd(_aMyBtn, {"S4WB005N", { ||U_MTRTransf()  }, OemToAnsi( 'Impressao da Transferencia' ) } )
// Endif
// a funcao U_MT241B2 Encontra-se no PE M241BUT
Aadd(_aMyBtn, { "BUDGET" , { ||U_MT241B2() }, OemToAnsi( 'Consulta o Saldo do Produto' ) } )

Return( _aMyBtn )

User Function MTRTransf

Local nPosEstorno := aScan(aHeader,{|x|UPPER(Alltrim(x[2])) == "D3_ESTORNO"})
Local nPosEstor,nPosCODOri,nPosDOri,nPosUMOri,nPosLOCOri,nPosLcZOri,nPosCODDes,nPosDDes,nPosUMDes
Local nPosLOCDes,nPosLcZDes,nPosNSer,nPosLoTCTL,nPosNLOTE,nPosDTVAL,nPosPotenc,nPosQUANT,nPosQTSEG,nPosNumSeq,nPosLotDes
Local nPosObs	  := aScan(aHeader,{|x|UPPER(Alltrim(x[2])) == "D3_OBS"})
Local nPosSolict := aScan(aHeader,{|x|UPPER(Alltrim(x[2])) == "D3_SOLICIT"})
Local cLogTipo   := "I"

If nPosEstorno == 1
	nPosEstor 	:= 1						//'Estornado'
	nPosCODOri	:= 2 						//Codigo do Produto Origem
	nPosDOri		:= 3						//Descricao do Produto Origem
	nPosUMOri	:= 4						//Unidade de Medida Origem
	nPosLOCOri	:= 5						//Armazem Origem
	nPosLcZOri	:= 6						//Localizacao Origem
	nPosCODDes	:= Iif(!lPyme,7,6)	//Codigo do Produto Destino
	nPosDDes		:= Iif(!lPyme,8,7)	//Descricao do Produto Destino
	nPosUMDes	:= Iif(!lPyme,9,8)	//Unidade de Medida Destino
	nPosLOCDes	:= Iif(!lPyme,10,9)	//Armazem Destino
	nPosLcZDes	:= 11						//Localizacao Destino
	nPosNSer		:= 12						//Numero de Serie
	nPosLoTCTL	:= 13						//Lote de Controle
	nPosNLOTE	:= 14						//Numero do Lote
	nPosDTVAL	:= 15						//Data Valida
	nPosPotenc	:= 16						//Potencia
	nPosQUANT	:= Iif(!lPyme,17,10)	//Quantidade
	nPosQTSEG	:= Iif(!lPyme,18,11)	//Quantidade na 2a. Unidade de Medida
	nPosNumSeq	:= Iif(!lPyme,19,12)	//'Sequencia'
	nPosLotDes	:= 20 					// Lote Destino
Else
	nPosCODOri := 1 						//Codigo do Produto Origem
	nPosDOri	  := 2						//Descricao do Produto Origem
	nPosUMOri  := 3						//Unidade de Medida Origem
	nPosLOCOri := 4						//Armazem Origem
	nPosLcZOri := 5						//Localizacao Origem
	nPosCODDes := Iif(!lPyme,6,5) 	//Codigo do Produto Destino
	nPosDDes	  := Iif(!lPyme,7,6)		//Descricao do Produto Destino
	nPosUMDes  := Iif(!lPyme,8,7)		//Unidade de Medida Destino
	nPosLOCDes := Iif(!lPyme,9,8)		//Armazem Destino
	nPosLcZDes := 10						//Localizacao Destino
	nPosNSer	  := 11						//Numero de Serie
	nPosLoTCTL := 12						//Lote de Controle
	nPosNLOTE  := 13						//Numero do Lote
	nPosDTVAL  := 14						//Data Valida
	nPosPotenc := 15						//Potencia
	nPosQUANT  := Iif(!lPyme,16, 9)	//Quantidade
	nPosQTSEG  := Iif(!lPyme,17,10)	//Quantidade na 2a. Unidade de Medida
	nPosEstor  := Iif(!lPyme,18,11)	//'Estornado'
	nPosNumSeq := Iif(!lPyme,19,12)	//'Sequencia'
	nPosLotDes := 20 						// Lote Destino
Endif

Private aCabNLogOri  := {}
Private aCabNLogDes  := {}
Private aNLogOri  := {}
Private aNLogDes  := {}
Private aNLgSld 	:= {}
Private aLogPrint := {}
/*
AAdd( aNLogOri , Array( 08 ) )
aNLogOri[ 1,01 ] := Padr("Codigo",15)
aNLogOri[ 1,02 ] := Padr("Descricao",40)
aNLogOri[ 1,03 ] := Padr("UM",2)
aNLogOri[ 1,04 ] := "Local"
aNLogOri[ 1,05 ] := Padr("Quant",12)
aNLogOri[ 1,06 ] := Padr("Localiz.",10)
aNLogOri[ 1,07 ] := Padr("Numero Serie",20)
aNLogOri[ 1,08 ] := Padr("Lote",20)

AAdd( aNLogDes , Array( 07 ) )
aNLogDes[ 1,01 ] := "Codigo"
aNLogDes[ 1,02 ] := "Descricao"
aNLogDes[ 1,03 ] := "UM"
aNLogDes[ 1,04 ] := "Local"
aNLogDes[ 1,05 ] := "Localiz."
aNLogDes[ 1,06 ] := "Solicitante"
aNLogDes[ 1,07 ] := "Motivo - Observacao"
*/
For nx := 1 to Len(aCols)
	
	aAdd(aNLogOri, { ;
	aCols[nx,nPosCODOri],;
	aCols[nx,nPosDOri]  ,;
	aCols[nx,nPosUMOri] ,;
	aCols[nx,nPosLOCOri],;
	Transform(aCols[nx,nPosQuant],"@E 999,999.99") ,;
	aCols[nx,nPosNSer]  } )
	
	aAdd(aNLogDes, { ;
	aCols[nx,nPosCODDes],;
	aCols[nx,nPosDDes]  ,;
	aCols[nx,nPosUMDes] ,;
	aCols[nx,nPosLOCDes],;
	aCols[nx,nPosObs],;
	aCols[nx,nPosSolict] } )
	If nPosEstorno > 0
		If aCols[nx,nPosEstorno] == 'S'
			cLogTipo    := "E"
		Endif
	Endif
	
Next nLin

If 'VISUAL' $ Upper(Procname(5))
	u_261RelLogMov(cLogTipo, aNLogOri, aNLogDes )
Endif
If 'INCLUI' $ Upper(Procname(5))
	Alert("Opcao nao disponivel")
Endif
If 'ESTORN' $ Upper(Procname(5))
	u_261RelLogMov(cLogTipo, aNLogOri, aNLogDes )
Endif

Return NIL

User Function 261RelLogMov(cTp,aOLogSld,aDLogSld)

Local cTitulo  := "Relacao dos Produtos Movimentados"
Local cDesc1   := "Este relatorio lista os produtos lancados nas transferencias MODII   "
Local cDesc2   := "dos quais os saldos estao zerados e que geraram movimentos.          "
Local cDesc3   := " "
Private cString  := "SD3"
Private wnrel    := "LOGMOV"
Private Tamanho  := "M"//"P"
Private nTipo    := 15
Private limite   := 132

PRIVATE cPerg   :="      "
PRIVATE aReturn := { "Zebrado", 1,"Administracao", 1, 2, 1, "",1 }
PRIVATE nLastKey:=0

wnRel := SetPrint(cString,wnrel,"",@cTitulo,cDesc1,cDesc2,cDesc3,.F.,,.F.,Tamanho,,.F.)

If nLastKey = 27
	Return
Endif

SetDefault(aReturn,cString)

If nLastKey = 27
	Return
Endif

RptStatus({|lEnd| xRLogImpr(@lEnd,wnRel,Tamanho,cTitulo,cTp,aOLogSld,aDLogSld)},cTitulo)

Return

Static Function xRLogImpr(lEnd,wnRel,Tamanho,Titulo,cTp, aOLogSld, aDLogSld)
Local   n 		:= 0
Private Li     := 80
Private M_Pag  := 1

cCab01 := OemToAnsi("            CODIGO      DESCRICAO                        UM  ALM      QUANT NUMERO DE SERIE      MOTIVO")
cCab02 := ""
If cTP == 'I'
	Titulo := 'Trnsferencia Interna de Estoque'
ElseIf cTp == 'E'
	Titulo := "Estorno de Transferencia de Estoque"
Endif
Cabec( Titulo,cCab01,cCab02,wnRel,Tamanho,nTipo)
@ Prow()+1,00000001 PSay OemToAnsi("Documento: ") + cDocumento
@ Prow()  ,Pcol()+3 PSay OemToAnsi("Emissao: ") + dtoc(dA261Data) + " as " + Time()
@ Prow()+1,00000000 Psay __PrtThinLine()

nTamDesc := 30
For n := 1 to Len(aOLogSld)
	
	If (LastKey() == 286)
		n := Len(aOLogSld)
	EndIf
	If n == 1
		@ Prow()+1,00000001 PSay "Origem  -> "+Left(aOLogSld[1,1],11)
		@ Prow()  ,Pcol()+1 PSay Substr(aOLogSld[1,2],1,32)
		@ Prow()  ,Pcol()+1 PSay aOLogSld[1,3]
		@ Prow()  ,Pcol()+3 PSay aOLogSld[1,4]
		@ Prow()  ,Pcol()+1 PSay aOLogSld[1,5]
		@ Prow()  ,Pcol()+1 PSay aOLogSld[1,6]
		@ Prow()  ,Pcol()+1 PSay aDLogSld[1,5]
		@ Prow()+1,00000001 PSay "Destino -> "+Left(aDLogSld[1,1],11)
		@ Prow()  ,Pcol()+1 PSay Substr(aDLogSld[1,2],1,32)
		@ Prow()  ,Pcol()+1 PSay aDLogSld[1,3]
		@ Prow()  ,Pcol()+3 PSay aDLogSld[1,4]
		@ Prow()  ,Pcol()+1 PSay "Solicitante -> "+aDLogSld[1,6]
		If Len(aOLogSld) > 1
			@ Prow()+1,00000000 PSay replicate("-",80)
		Endif
	Else
		@ Prow()+1,00000001 PSay "Origem  -> "+Left(aOLogSld[n,1],11)
		@ Prow()  ,Pcol()+1 PSay Substr(aOLogSld[n,2],1,32)
		@ Prow()  ,Pcol()+1 PSay aOLogSld[1,3]
		@ Prow()  ,Pcol()+3 PSay aOLogSld[1,4]
		@ Prow()  ,Pcol()+1 PSay aOLogSld[1,5]
		@ Prow()  ,Pcol()+1 PSay aOLogSld[1,6]
		@ Prow()  ,Pcol()+1 PSay aDLogSld[1,5]
		@ Prow()+1,00000001 PSay "Destino -> "+Left(aDLogSld[n,1],11)
		@ Prow()  ,Pcol()+1 PSay Substr(aDLogSld[n,2],1,32)
		@ Prow()  ,Pcol()+1 PSay aDLogSld[1,3]
		@ Prow()  ,Pcol()+3 PSay aDLogSld[1,4]
		@ Prow()  ,Pcol()+1 PSay "Solicitante -> "+aDLogSld[1,6]
	Endif
	If (Prow() > 60)
		Cabec( Titulo,cCab01,cCab02,wnRel,Tamanho,nTipo)
		@ Prow()+1,00000001 PSay OemToAnsi("Documento: ") + cDocumento
		@ Prow()  ,Pcol()+3 PSay OemToAnsi("Emissao: ") + dtoc(dA261Data) //+ " as " + Time()
		@ Prow()+1,00000000 Psay __PrtThinLine()
	EndIf
	
Next n

//@ Prow()+1,00000000 PSay replicate("-",80)
//@ Prow()+1,00000000 PSay ""
/*
For n := 1 to Len(aDLogSld)

If (LastKey() == 286)
n := Len(aDLogSld)
EndIf
If n == 1
@ Prow()+1,00000001 PSay "Destino -> "+Left(aDLogSld[1,1],11)
@ Prow()  ,Pcol()+1 PSay Substr(aDLogSld[1,2],1,32)
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,3]
@ Prow()  ,Pcol()+3 PSay aDLogSld[1,4]
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,5]
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,6]
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,7]
If Len(aDLogSld) > 1
@ Prow()+1,00000000 PSay replicate("-",80)
Endif
Else
@ Prow()+1,00000001 PSay "Destino -> "+Left(aDLogSld[n,1],11)
@ Prow()  ,Pcol()+1 PSay Substr(aDLogSld[n,2],1,32)
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,3]
@ Prow()  ,Pcol()+3 PSay aDLogSld[1,4]
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,5]
@ Prow()  ,Pcol()+1 PSay aDLogSld[1,6]
@ Prow()  ,Pcol()+7 PSay aDLogSld[1,7]
Endif
If (Prow() > 60)
Cabec( Titulo,cCab01,cCab02,wnRel,Tamanho,nTipo)
@ Prow()+1,00000001 PSay OemToAnsi("Documento: ") + cDocumento
@ Prow()  ,Pcol()+3 PSay OemToAnsi("Emissao: ") + dtoc(dA261Data) //+ " as " + Time()
@ Prow()+1,00000000 Psay __PrtThinLine()
EndIf

Next n
*/
@ Prow()+1,00000000 Psay __PrtThinLine()
@ Prow()+3,00000001 PSay OemToAnsi("____________________         _______________________")
@ Prow()+1,00000001 PSay OemToAnsi("    Solicitante                    Autorizacao      ")

If (LastKey() == 286)
	@ PRow()+1,00 PSay OemToAnsi("CANCELADO PELO OPERADOR.")
Else
	Roda( Len(aDLogSld), OemToAnsi("Registro(s) processado(s)"), Tamanho )
EndIf

SET DEVICE TO SCREEN
If aReturn[5] == 1
	Set Printer TO
	dbCommitAll()
	ourspool(wnrel)
Endif

MS_FLUSH()

Return( nil )
