<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">
	<%=oSelf:osheetPr:GetTag()%>
	<dimension ref="<%=oSelf:NumToString(oSelf:aDimension[2][2])+cValToChar(oSelf:aDimension[2][1])+":"+oSelf:NumToString(oSelf:aDimension[1][2])+cValToChar(oSelf:aDimension[1][1])%>"/>
	<%=oSelf:osheetViews:GetTag()%>
	<sheetFormatPr defaultRowHeight="15" x14ac:dyDescent="0.25"/>
	<%=oSelf:oCols:GetTag()%>
<%oSelf:GravaFile(__aCookies,__Response)
	__Response:=""
	FWRITE(__aCookies,"<sheetData>")
	nTamArquivo := Fseek(oSelf:nFileTmpRow,0,2)
	nBytesFalta := nTamArquivo
	Fseek(oSelf:nFileTmpRow,0)
	cBuffer	:= Space(2048)
	While nBytesFalta > 0
		nBytesLer 	:= Min(nBytesFalta, 2048 )
		nBytesLidos := FREAD(oSelf:nFileTmpRow, @cBuffer, nBytesLer )
		nBytesSalvo := FWRITE(__aCookies, cBuffer,nBytesLer)
		nBytesFalta -= nBytesLer
	EndDo
	FCLOSE(oSelf:nFileTmpRow)
	FWRITE(__aCookies,"</sheetData>")
	%>
	<%=If(ValType(oSelf:oAutoFilter)=="O",oSelf:oAutoFilter:GetTag(),"")%>
	<%=If(!Empty(oSelf:oMergeCells:GetValor()),oSelf:oMergeCells:GetTag(),"")%>
	<% If !Empty(oSelf:aConditionalFormatting)
		For nCont:=1 to Len(oSelf:aConditionalFormatting)%>
			<%=oSelf:aConditionalFormatting[nCont]:GetTag()%>
	<%Next
	EndIf%><pageMargins left="0.511811024" right="0.511811024" top="0.78740157499999996" bottom="0.78740157499999996" header="0.31496062000000002" footer="0.31496062000000002"/>
	<%=if(!Empty(oSelf:atable),oSelf:otableParts:GetTag(),"")%>
	<%=if(!Empty(oSelf:adrawing),oSelf:odrawing:GetTag(),"")%>
</worksheet>
<%oSelf:GravaFile(__aCookies,__Response)
__Response:=""%>