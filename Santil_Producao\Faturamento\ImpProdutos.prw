#Include 'rwmake.ch'
#Include 'TbiConn.ch'

User Function ImpProdutos()
Local _n	:= 0

Private _aStruct
Private _nList	:= 0

PREPARE ENVIRONMENT EMPRESA '01' FILIAL '0001' TABLES 'SB1','DA1'

_aStruct := {}
AAdd(_aStruct,{"LINHA"		,"C",2500,0})

_cArq := CriaTrab(NIL,.F.)
DbCreate(_cArq,_aStruct)
DbUseArea(.T.,,_cArq,"TRB",.F.,.F.)

_aArqs 	:= Directory("*.csv")
_aFiles := {}
For _n := 1 to Len(_aArqs)
	AADD(_aFiles,_aArqs[_n][1])
Next

@ 0,0 TO 280,450 DIALOG _oDlg1 TITLE "Arquivos Disponiveis"
@ 10,10 SAY "Arquivos "
@ 20,10 LISTBOX _nList ITEMS _aFiles SIZE 200,100
@ 125,150 BmpButton Type 1 Action (_lOk:=.T.,Close(_oDlg1))
@ 125,180 BmpButton Type 2 Action (_lOk:=.F.,Close(_oDlg1))

ACTIVATE DIALOG _oDlg1 CENTER

If !_lOK

	DbSelectArea("TRB")
	DbCloseArea()
	Return .T.

Endif

APPEND FROM (_aFiles[_nList]) SDF

oProcess := MsNewProcess():New({|lEnd| GeraProd()},"Linha","Produtos...",.T.)
oProcess:Activate()

_cArqTxt := Rtrim(_aFiles[_nList])
_cArqRen := Substr(_cArqTxt,1,Len(Rtrim(_cArqTxt))-3)+"_OK"
frename(_cArqTxt,_cArqRen)

DbSelectArea("TRB")
DbCloseArea()

Return .T.


Static Function GeraProd()
Local _nTab 		:= 0
Local _aCampos		:= {}
Local _aConteudo    := {}
Local _cItem 		:= "0009"
Local _n			:= 0

DbSelectArea("TRB")
oProcess:SetRegua1(RecCount())
oProcess:SetRegua2(RecCount())
DbGotop()

Do While !Eof()

	If Recno() = 1

		_nFim	:= Len(Linha)

		_cLinha := Linha

		Do While .T.

			AADD(_aCampos,Substr(_cLinha,1,At(";",_cLinha)-1))

			_cLinha := Substr(_cLinha,At(";",_cLinha)+1,_nFim)


			If At(";",_cLinha) = 0

				Exit

			Endif

		Enddo

	Else

		_nFim	:= Len(Linha)

		_cLinha := Linha

		Do While .T.

			AADD(_aConteudo,Substr(_cLinha,1,At(";",_cLinha)-1))

			_cLinha := Substr(_cLinha,At(";",_cLinha)+1,_nFim)

			If At(";",_cLinha) = 0

				Exit

			Endif

		Enddo


	Endif

	If Len(_aConteudo) > 0

		DbSelectArea("SB1")
		DbSetOrder(1)
		DbGotop()

		If !DbSeek(xFilial("SB1")+_aConteudo[2])
			RecLock("SB1",.T.)
		Else
			RecLock("SB1",.F.)
		Endif

		For _n:=1 to Len(_aConteudo)

			_cCampo := "SB1->"+_aCampos[_n]

			If FieldPos(_aCampos[_n]) > 0

				IF ValType(&_cCampo) = "C"
					Replace &_cCampo With _aConteudo[_n]
				ElseIF ValType(&_cCampo) = "N"
					If At(",",_aConteudo[_n]) > 0
						Replace &_cCampo With Val(Stuff(_aConteudo[_n],At(",",_aConteudo[_n]),01,"."))
					Else
						Replace &_cCampo With Val(_aConteudo[_n])
					Endif
				ElseIF ValType(&_cCampo) = "D"
					Replace &_cCampo With Ctod(_aConteudo[_n])
				Endif

			Endif

		Next
		SB1->B1_LOCPAD 	:= "01"

		MsUnlock()

		DbSelectArea("SM0")
		DbGotop()

		Do While !Eof()

			DbSelectArea("SB9")
			DbSetOrder(1)
			Dbgotop()

			If DbSeek(Rtrim(SM0->M0_CODFIL)+SB1->B1_COD+SB1->B1_LOCPAD)
				RecLock("SB9",.F.)
			Else
				RecLock("SB9",.T.)
			Endif

			SB9->B9_FILIAL 	:= SM0->M0_CODFIL
			SB9->B9_COD		:= SB1->B1_COD
			SB9->B9_LOCAL	:= "01"
			SB9->B9_QINI	:= 1000

			Msunlock()

			DbSelectArea("SM0")
			DbSkip()

		Enddo

		_aConteudo :={}

/*
		DbSelectArea("DA1")
		DbSetOrder(2)
		DbGotop()

		_cItem := StrZero(Val(_cItem)+1,4)

		If DbSeek(xFilial("DA1")+SB1->B1_COD+"001")
			RecLock("DA1",.F.)
		Else
			RecLock("DA1",.T.)
		Endif

		DA1->DA1_FILIAL 	:= xFilial("DA1")
		DA1->DA1_ITEM		:= _cItem
		DA1->DA1_CODPRO		:= SB1->B1_COD
		DA1->DA1_PRCVEN		:= SB1->B1_PRV1
		DA1->DA1_ATIVO		:= "1"
		DA1->DA1_TPOPER		:= "4"
		DA1->DA1_QTDLOT		:= 999999.99
		DA1->DA1_INDLOT		:= "000000000999999.99"
		DA1->DA1_MOEDA		:= 1
		DA1->DA1_DATVIG		:= Ctod("31/12/14")

		MsUnlock()
*/
	Endif

	DbSelectArea("TRB")
	DbSkip()

Enddo

Return .T.
