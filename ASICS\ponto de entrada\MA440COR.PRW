#Include "Protheus.ch"

/*
#==================================================================================#
| +------------------------------------------------------------------------------+ |
| ! FUNCAO.........:	MA410COR                                                 ! |
| ! DESCRICAO......:    LEGENDA DOS REGISTROS DE EXCECAO DE PRECOS               ! |
| ! AUTOR..........:	BRUNNO ABRIGO                            	                ! |
| ! DATA...........:	14/05/2015                                               ! |
| ! PARAMETROS.....:	NIL                                                      ! |
| ! RETORNO........:	NIL                                                      ! |
| +------------------------------------------------------------------------------+ |
#==================================================================================#
*/

User Function MA440COR
	Local aAuxCor   := PARAMIXB
	Local aRegraAux := 	" .And. SC5->C5_XBLQ <> 'B' .And. SC5->C5_XBLQ <> 'R' "
	
	For i:=1 to Len(aAuxCor)
		aAuxCor[i][1] += aRegraAux
	Next i                     
	
	Aadd(aAuxCor,	{'SC5->C5_XBLQ == "R"' , 'BR_CANCEL'	})
	Aadd(aAuxCor,	{'SC5->C5_XBLQ == "B"' , 'BR_PINK'   })

Return aAuxCor 