#include 'TOTVS.ch'
#include "tbiconn.ch"

/*/{Protheus.doc}  SEFRTAMB
Alteracao de ambiente do PDV Frontloja.

<AUTHOR>
@since 			27/12/2016
@version		1.0
/*/

User Function SEFRTAMB(cCompany, cBranch, cPDV)

Local cEnvironment	:= " "                   
Local cEndProd		:= " "
Local cPortProd		:= " "
Local cEndCntg		:= " "
Local cPortCtg		:= " "
Local cWSProd		:= " "
Local cWSCntg		:= " "
Local cEnvProd		:= " "
Local cEnvCtg		:= " "
Local aPergs		:= {}
Local aRet			:= {}

Default cCompany	:= "01"
Default cBranch		:= "0001"
Default cPDV		:= " "

//-- Para nao consumir licencas
RPCSETTYPE(3)

PREPARE ENVIRONMENT EMPRESA cCompany FILIAL cBranch //MODULO 'SIGAFRT' TABLES 'SLG' MODULO 'FRT'

/*---------------------------------------------------------------+
| Parametros que indicam qual IP e porta dos servidores de		 |
| producao e contingencia										 |
|----------------------------------------------------------------|
| SE_IPPRD -> IP do server de producao							 |
| SE_IPCTG -> IP do server de contingencia						 |
| SE_PTPRD -> Porta do server de producao						 |
| SE_PTCTG -> Porta do server de contingencia					 |
| SE_WSPRD -> Porta do webservice de Producao					 |
| SE_WSCTG -> Porta do webservice de Contingencia				 |
| SE_ENVPR -> Ambiente do RPC de producao						 |
| SE_ENVCG -> Ambiente do RPC de Contingencia					 |
+----------------------------------------------------------------*/
DBSelectArea("SX6")

cEndProd		:= SuperGetMV("SE_IPPRD" ,,"*************"	)
cPortProd		:= SuperGetMV("SE_PTPRD" ,,"10403"			)
cEndCntg	 	:= SuperGetMV("SE_IPCTG" ,,"*************"	)
cPortCtg		:= SuperGetMV("SE_PTCTG" ,,"10403"			)
cWSProd			:= SuperGetMV("SE_WSPRD" ,,"8181"			)
cWSCntg			:= SuperGetMV("SE_WSCTG" ,,"8183"			)
cEnvProd		:= SuperGetMV("SE_ENVPR" ,,"JOB"			)
cEnvCtg			:= SuperGetMV("SE_ENVCG" ,,"RPC"			)

aAdd( aPergs ,{2,"Mudar para ambiente:",1, {"Producao", "Contingencia"}, 50,'.T.',.T.})

If ParamBox(aPergs ,"Parametros ",aRet)
   
	DBSelectArea("SLG")
	DBSetOrder(1)
	If DBSeek(AllTrim(cBranch)+AllTrim(cPDV))
		Reclock("SLG",.F.)                                   
		Do Case
			Case VALTYPE(aRet[01]) == "N"
				Replace SLG->LG_RPCSRV 	With AllTrim(cEndProd	)
				Replace SLG->LG_RPCPORT With AllTrim(cPortProd	)
				Replace SLG->LG_RPCENV	With AllTrim(cEnvProd	)
				Replace SLG->LG_TSCSRV	With AllTrim(cEndProd	)
				Replace SLG->LG_TSCPORT With AllTrim(cPortProd	)
				Replace SLG->LG_WSSRV	With AllTrim(cEndProd	)+":"+AllTrim(cWSProd)
				Replace SLG->LG_WSSRVMT With AllTrim(cEndProd	)+":"+AllTrim(cWSProd)
				
			Case AllTrim(aRet[01]) == "Contingencia" 	 	
				Replace SLG->LG_RPCSRV 	With AllTrim(cEndCntg	)
				Replace SLG->LG_RPCPORT With AllTrim(cPortCtg	)
				Replace SLG->LG_RPCENV	With AllTrim(cEnvCtg	)
				Replace SLG->LG_TSCSRV	With AllTrim(cEndCntg	)
				Replace SLG->LG_TSCPORT With AllTrim(cPortCtg	)
				Replace SLG->LG_WSSRV	With AllTrim(cEndCntg	)+":"+AllTrim(cWSCntg)
				Replace SLG->LG_WSSRVMT With AllTrim(cEndCntg	)+":"+AllTrim(cWSCntg)
				
		EndCase
		
		DBUnlock()
		
		MsgInfo("Ambiente alterado com sucesso!")
		MsgAlert("Favor reiniciar o computador para que as alteracoes entrem em vigor.")
				
	Else
		MsgAlert("Estacao nao encontrada! Entre em contato com o suporte.")
	EndIf
	
	DBCloseArea("SLG")
    
EndIf

DBCloseArea("SX6")

RESET ENVIRONMENT

Return