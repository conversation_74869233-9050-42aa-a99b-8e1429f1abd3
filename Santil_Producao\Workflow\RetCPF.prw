#Include 'Protheus.ch'

User Function rCliCPF() //U_rCliCPF()
	Local aAreaAtu 		:= GetArea()
	Local cCnpj 		:= ""

		DbSelectArea("SUA")
		DbSetOrder(1)
		If DbSeek(SZ3->Z3_FILIAL+SZ3->Z3_ORC)
			DbSelectArea("SA1")
			DbSetOrder(1)

			If DbSeek(xFilial("SA1")+SUA->UA_CLIENTE+SUA->UA_LOJA)
			 	IF SA1->A1_PESSOA == "J"
			 		cCnpj := TransForm(SA1->A1_CGC, "@R 99.999.999/9999-99")
			 	Else
			 		cCnpj := TransForm(SA1->A1_CGC, "@R 999.999.999-99")
			 	EndIf

			EndIf

		EndIf

	RestArea(aAreaAtu)
Return(cCnpj)