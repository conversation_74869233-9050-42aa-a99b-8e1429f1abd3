#ifdef SPANISH
	#define STR0001"De pedido"
	#define STR0002"A pedido"
	#define STR0003"Informe los parametros"
	#define STR0004"Por favor, cree la carpeta/directorio TEMP en el C:\."
	#define STR0005"Por favor espere, imprimiendo pedido de compra..."
	#define STR0006"Por favor espere, generando archivo PDF..."
	#define STR0007"No existe este pedido de compra:"
	#define STR0008"EMPRESA PRUEBA DE TOTVS"
	#define STR0009"PEDIDO DE COMPRA"
	#define	STR0010"Pedido nº:"
	#define	STR0011"Pagina:"
	#define	STR0012"PROVEEDOR:"
	#define	STR0013"VENDEDOR:"
	#define	STR0014"RCPJ:"
	#define	STR0015"FAX:"
	#define	STR0016"TEL:"
	#define	STR0017"IE:"
	#define	STR0018"FECH DE EMISION:"
	#define	STR0019"FECHA DE ENTREGA:"
	#define	STR0020"COMPRADOR:"
	#define	STR0021"DIGITADO POR:"
	#define	STR0022"CALIDAD:"
	#define	STR0023"COND.PAGO:"
	#define	STR0024"Pieza piloto:"
	#define	STR0025"Si"
	#define	STR0026"No"
	#define	STR0027"Descuento financiero:"
	#define	STR0028"Descuento:"
	#define	STR0029"Aumento:"
	#define	STR0030"Cant. Total:"
	#define	STR0031"Total del pedido:"
	#define	STR0032"Costo final:"
	#define	STR0033"Observaciones:"
	#define	STR0034"software de gestion: symm.com.br"
	#define	STR0035"Ref"
	#define	STR0036"Color"
	#define	STR0037"Pack"
	#define	STR0038"Referencia"
	#define	STR0039"Colores"
	#define	STR0040"Cod.Proveed."
	#define	STR0041"Mult"
	#define	STR0042"Total"
	#define	STR0043"Unitario"
	#define	STR0044"Precio venta"
	#define	STR0045"Distribucion"
	#define	STR0046"¿Desea enviar este pedido de compra por e-mail?"
	#define	STR0047"El e-mail del proveedor no esta registrado."
	#define	STR0048"Informe correctamente el directorio del Outlook."
	#define	STR0049"Informe el directorio del Outlook. Ejemplo [C:\Program Files\Microsoft Office\]"
	#define	STR0050"Directorio:"
	#define	STR0051"&Ok"
	#define	STR0052"&Anular"
	#define	STR0053"Producto:"
	#define	STR0054"Cantidad:"
	#define	STR0055"Subtotal R$:"
	#define	STR0056"Val. IPI:"
	#define	STR0057"Total R$:"
	#define	STR0058"Marca:"
#else
	#ifdef ENGLISH
		#define STR0001"From Order"
		#define STR0002"To Order"
		#define STR0003"Enter Parameters"
		#define STR0004"Please create TEMP folder/directory on C:\."
		#define STR0005"Please wait, Printing Purchase Order..."
		#define STR0006"Please wait, Generating PDF file..."
		#define STR0007"Purchase Order Inexistent: "
		#define STR0008"TOTVS COMPANY TEST"
		#define STR0009"PURCHASE ORDER"
		#define STR0010"Order No: "
		#define STR0011"Page: "
		#define STR0012"SUPPLIER:"
		#define STR0013"VENDOR: "
		#define STR0014"CNPJ: "
		#define STR0015"FAX NUMBER: "
		#define STR0016"PHONE: "
		#define STR0017"IE: "
		#define STR0018"ISSUE DATE: "
		#define STR0019"DELIVERY DATE: "
		#define STR0020"BUYER: "
		#define STR0021"TYPED BY: "
		#define STR0022"QUALITY: "
		#define STR0023"PAYMENT CONDITIONS: "
		#define STR0024"Pilot Part: "
		#define STR0025"Yes"
		#define STR0026"No"
		#define STR0027"Financial Discount: "
		#define STR0028"Discount: "
		#define STR0029"Addition: "
		#define STR0030"Qtty. Total: "
		#define STR0031"Total Order: "
		#define STR0032"Final Cost: "
		#define STR0033"Observations:"
		#define STR0034"management software: symm.com.br"
		#define STR0035"Ref."
		#define STR0036"Color"
		#define STR0037"Pack"
		#define STR0038"Reference"
		#define STR0039"Colors"
		#define STR0040"Cod.Supplier"
		#define STR0041"Mult"
		#define STR0042"Total"
		#define STR0043"Unit"
		#define STR0044"Sales Price"
		#define STR0045"Distribution"
		#define STR0046"Send this Purchase Order by Email?"
		#define STR0047"Supplier email not registered."
		#define STR0048"Enter correctly the Outlook Directory."
		#define STR0049"Enter Outlook Directory. Example [C:\Program Files\Microsoft Office\]"
		#define STR0050"Directory:"
		#define STR0051"&Ok"
		#define STR0052"&Cancel"
		#define STR0053"Product: "
		#define STR0054"Quantity: "
		#define STR0055"SubTotal R$: "
		#define STR0056"Value IPI: "
		#define STR0057"Total R$: "
		#define STR0058"Brand: "
	#else
		#define STR0001"Do Pedido"
		#define STR0002"Ate Pedido"
		#define STR0003"Informe os Parametros"
		#define STR0004"Por favor criar a pasta/diret?rio TEMP no C:\."
		#define STR0005"Por favor aguarde, Imprimindo Pedido de Compra..."
		#define STR0006"Por favor aguarde, Gerando arquivo PDF..."
		#define STR0007"Nao Existe este Pedido de Compra:"
		#define STR0008"EMPRESA TESTE DA TOTVS"
		#define STR0009"PEDIDO DE COMPRA"
		#define STR0010"Pedido no:"
		#define STR0011"Pagina:"
		#define STR0012"FORNECEDOR:"
		#define STR0013"VENDEDOR:"
		#define STR0014"CNPJ:"
		#define STR0015"FAX:"
		#define STR0016"FONE:"
		#define STR0017"IE:"
		#define STR0018"DATA EMISSAO:"
		#define STR0019"DATA ENTREGA:"
		#define STR0020"COMPRADOR:"
		#define STR0021"DIGITADO POR:"
		#define STR0022"QUALIDADE:"
		#define STR0023"COND.PAGAMENTO:"
		#define STR0024"Peca Piloto:"
		#define STR0025"Sim"
		#define STR0026"Nao"
		#define STR0027"Desconto Financeiro:"
		#define STR0028"Desconto:"
		#define STR0029"Acrescimo:"
		#define STR0030"Qtd. Total:"
		#define STR0031"Total do Pedido:"
		#define STR0032"Custo Final:"
		#define STR0033"Observacoes:"
		#define STR0034"software de gestao: symm.com.br"
		#define STR0035"Ref"
		#define STR0036"Cor."
		#define STR0037"Pack"
		#define STR0038"Referencia"
		#define STR0039"Cores"
		#define STR0040"Cod.Fornec"
		#define STR0041"Mult"
		#define STR0042"Total"
		#define STR0043"Unitario"
		#define STR0044"Preco Venda"
		#define STR0045"Distribuicao"
		#define STR0046"Deseja enviar este Pedido de Compra por Email ?"
		#define STR0047"O e-mail do fornecedor n?o esta cadastrado."
		#define STR0048"Informe corretamente o Diret?rio do Outlook."
		#define STR0049"Informe o Diret?rio do Outlook. Exemplo [C:\Program Files\Microsoft Office\]"
		#define STR0050"Diret?rio:"
		#define STR0051"&Ok"
		#define STR0052"&Cancelar"
		#define STR0053"Produto:"
		#define STR0054"Quantidade:"
		#define STR0055"SubTotal R$:"
		#define STR0056"Vlr. IPI:"
		#define STR0057"Total R$:"
		#define STR0058"Marca:"
	#endif
#endif
