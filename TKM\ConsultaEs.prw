#Include 'Protheus.ch'

STATIC cCampo_01 := space(len(SA2->A2_DIACONS))

User Function EspecificaC()
	Local aRet := {}
	Local aParamBox := {}
	Local i := 0
	Local cTexto := ""
	Local lAdd := .F.
	
	Private cCadastro := "Dias"

	aAdd(aParamBox,{9,"Selecione dias:",150,7,.T.})
	aAdd(aParamBox,{5,"Segunda",.F.,50,"",.F.})
	aAdd(aParamBox,{5,"Terca",.F.,50,"",.F.})
	aAdd(aParamBox,{5,"Quarta",.F.,50,"",.F.})
	aAdd(aParamBox,{5,"Quinta",.F.,50,"",.F.})
	aAdd(aParamBox,{5,"Sexta",.F.,50,"",.F.})
	aAdd(aParamBox,{5,"Sabado",.F.,50,"",.F.})
	aAdd(aParamBox,{5,"Domingo",.F.,50,"",.F.})

	If ParamBox(aParamBox,"Dias",@aRet)
		For i:=2 To Len(aRet)
			IF aRet[i]
				if lAdd
					cTexto += ";"
				endif
				cTexto += ALLTRIM(aParamBox[i][2])
				lAdd := .T.
			endif
		Next
	Endif
		
	cCampo_01 := LOWER(cTexto)

Return(.T.)

User Function retCons()
return(cCampo_01)
