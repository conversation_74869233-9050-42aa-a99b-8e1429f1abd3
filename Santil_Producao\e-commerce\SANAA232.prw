#include 'rwmake.ch'
#include 'protheus.ch'
#include "fwbrowse.ch"

//------------------------------------------------------------
//------------------------------------------------------------
/*
User Function GetTotal(cNum)
	Local nRet 		:= 0
	Local cQuery 	:= ""
	Local cAliTmp	:= GetNextAlias()


	cQuery := "SELECT SUM(UB_VRUNIT * UB_QUANT) AS UB_TOTAL FROM " + RetSqlName( "SUB" ) + " SUB WITH (NOLOCK)" + CRLF
	cQuery += "INNER JOIN " + RetSqlName( "SUA" ) + " SUA WITH (NOLOCK) ON UA_FILIAL = UB_FILIAL AND UA_NUM = UB_NUM AND SUA.D_E_L_E_T_ <> '*'" + CRLF
	cQuery += "WHERE UB_FILIAL = '" + xFilial( "SUA" ) + "' AND UB_NUM = '" + SUA->UA_NUM + "' AND SUB.D_E_L_E_T_ <> '*'" + CRLF

	If SELECT( cAliTmp ) > 0
		DbSelectArea( cAliTmp )
		( cAliTmp )->( DbCloseArea() )
	EndIf

	dbUseArea(.T., "TOPCONN", TCGenQry(,,cQuery), cAliTmp, .F., .T.)

	DbSelectArea( cAliTmp )
	( cAliTmp )->( DbGoTop() )

	nRet       := ( cAliTmp )->(UB_TOTAL)
	cQuery 	:= ""

	( cAliTmp )->( DbCloseArea() )
Return nRet
*/
//------------------------------------------------------------
//------------------------------------------------------------
/*
User Function GeraSb2(_cFilial,_cProduto,_cLocal)

	Local _aArea	:= GetArea()
	DbSelectArea("SB2")
	DbSetOrder(1)
	DbGoTop()

	If !Empty(_cLocal)

		If !DbSeek(_cFilial+_cProduto+_cLocal)

			SB2->(RecLock("SB2",.T.))
			SB2->B2_FILIAL	:= _cFilial
			SB2->B2_COD		:= _cProduto
			SB2->B2_LOCAL	:= _cLocal
			SB2->(DbCommit())
			SB2->(MsUnlock())

		Endif

	EndIf

	RestArea(_aArea)
Return .T.
*/
//------------------------------------------------------------
//------------------------------------------------------------
/*
User Function TESENTR(cTes)

	Local cTesRet := ""

	DbSelectArea("SF4")
	DbSetOrder(1)

	If DbSeek(xFilial("SF4")+cTes)

		cTesRet := SF4->F4_XTESPED

	EndIf

	If Empty(cTesRet)

		Aviso( "F4_XTESPED" , "TES de venda para tipo de entrega diferente de Retira não disponivel, favor verificar TES: "+cTes+ CHR(13)+CHR(10) +;
			"Será considerada TES do parametro ES_TESTRN "+GetMV("ES_TESTRN"), {"OK"} , 2 )
		cTesRet := GetMV("ES_TESTRN")

	EndIf

Return(cTesRet)
*/