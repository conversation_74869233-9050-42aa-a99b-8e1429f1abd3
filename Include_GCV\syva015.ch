#ifdef SPANISH
	#define STR0001"Buscar"
	#define STR0002"Visualizar"
	#define STR0003"Incluir"
	#define STR0004"Modificar"
	#define STR0005"Borrar"
	#define STR0006"Wizard para archivo de Compradores vs. Categorias"
	#define STR0007"Identificacion del comprador"
	#define STR0008"Verifique los datos del comprador."
	#define STR0009"Grupos de linea"
	#define	STR0010"Seleccione los Grupos de linea para vincularlos al Comprador"
	#define	STR0011"Categoria principal"
	#define	STR0012"Descripcion"
	#define	STR0013"Grupo de linea"
	#define	STR0014"Lineas"
	#define	STR0015"Seleccione las Lineas para vincularlas al comprador"
	#define	STR0016"Linea"
	#define	STR0017"Secciones"
	#define	STR0018"Seleccione las Secciones para vincularlas al comprador"
	#define	STR0019"Seccion"
	#define	STR0020"Especies"
	#define	STR0021"Seleccione las Especies para la vinculacion con el comprador"
	#define	STR0022"Especie"
	#define	STR0023"Categorias Nivel 5"
	#define	STR0024"Seleccione las Categorias de Nivel 5 para la vinculacion con el comprador"
	#define	STR0025"Categoria secundaria"
	#define	STR0026"Resumen de la vinculacion Categorias vs. Comprador"
	#define	STR0027"Verifique los datos para la generacion del comprador"
#else
	#ifdef ENGLISH
		#define STR0001"Search"
		#define STR0002"View"
		#define STR0003"Add"
		#define STR0004"Edit"
		#define STR0005"Delete"
		#define STR0006"Wizard used in Buyers x Categories file"
		#define STR0007"Buyer Identification"
		#define STR0008"Check buyers information."
		#define STR0009"Group of Rows"
		#define STR0010"Choose row groups to perform buyer bind."
		#define STR0011"Main Category"
		#define STR0012"Description"
		#define STR0013"Group of Rows"
		#define STR0014"Rows"
		#define STR0015"Escolha as Linhas para a amarracao com o Comprador"
		#define STR0016"Row"
		#define STR0017"Sections"
		#define STR0018"Choose sections to perfomr buyer bind."
		#define STR0019"Section"
		#define STR0020"Types"
		#define STR0021"Choose types to perfomr buyer bind."
		#define STR0022"Type"
		#define STR0023"Level 5 Category"
		#define STR0024"Choose level 5 categories to perform Buyer bind."
		#define STR0025"Child Category"
		#define STR0026"Summary of Category x Buyer bind."
		#define STR0027"Check data for Buyer generation."
	#else
		#define STR0001"Pesquisar"
		#define STR0002"Visualizar"
		#define STR0003"Incluir"
		#define STR0004"Alterar"
		#define STR0005"Excluir"
		#define STR0006"Wizard para Cadastro de Compradores x Categorias"
		#define STR0007"Identificacao do Comprador"
		#define STR0008"Verifique os dados do Comprador."
		#define STR0009"Grupos de Linha"
		#define STR0010"Escolha os Grupos de Linha para a amarracao com o Comprador"
		#define STR0011"Categoria Pai"
		#define STR0012"Descricao"
		#define STR0013"Grupo de Linha"
		#define STR0014"Linhas"
		#define STR0015"Escolha as Linhas para a amarracao com o Comprador"
		#define STR0016"Linha"
		#define STR0017"Secoes"
		#define STR0018"Escolha as Secoes para a amarracao com o Comprador"
		#define STR0019"Secao"
		#define STR0020"Especies"
		#define STR0021"Escolha as Especies para a amarracao com o Comprador"
		#define STR0022"Especie"
		#define STR0023"Categorias Nivel 5"
		#define STR0024"Escolha as Categorias de Nivel 5 para a amarracao com o Comprador"
		#define STR0025"Categoria Filho"
		#define STR0026"Resumo da Amarracao Categorias x Comprador"
		#define STR0027"Confira os dados para a geracao da Comprador"
	#endif
#endif