#ifdef SPANISH
	#define STR0001"Gestion comercial"
	#define STR0002"Analizar por:"
	#define STR0003"Categorias vs. Tiendas"
	#define STR0004"Tiendas vs. Categorias"
	#define STR0005"Indicadores:"
	#define STR0006"Semana"
	#define STR0007"Mes"
	#define STR0008"Ano"
	#define STR0009"Sin autorizacion"
	#define	STR0010"Resumen de gestion"
	#define	STR0011"Cargando gestion comercial..."
	#define	STR0012"Resumen de gestion"
	#define	STR0013"Montando consulta..."
	#define	STR0014"Ventas semana(C)"
	#define	STR0015"Ventas semana($)"
	#define	STR0016"Ventas mes(C)"
	#define	STR0017"Ventas mes(C)AP % +/-"
	#define	STR0018"Ventas mes($)"
	#define	STR0019"Ventas mes($)AP % +/-"
	#define	STR0020"Stock mes(C)"
	#define	STR0021"Stock mes(C)AP % +/-"
	#define	STR0022"Cobertura mes(C)"
	#define	STR0023"Stock mes($)"
	#define	STR0024"Stock mes($)AP % +/-"
	#define	STR0025"Cobertura mes($)"
	#define	STR0026"Pr.Medio mes"
	#define	STR0027"Pr.Medio mes AP % +/-"
	#define	STR0028"Pr.Medio Stock"
	#define	STR0029"% Ventas mes(C)"
	#define	STR0030"% Ventas mes($)"
	#define	STR0031"% Stock(C)"
	#define	STR0032"% Ventas stock($)"
	#define	STR0033"Giro(Q)"
	#define	STR0034"Giro($)"
	#define	STR0035"% Mkp Stock"
	#define	STR0036"Remarcacion semana($)"
	#define	STR0037"Remarcacion mes($)"
	#define	STR0038"% POV Mes"
	#define	STR0039"% BCP Semana"
	#define	STR0040"% BCP Mes"
	#define	STR0041"Ventas ano(C)"
	#define	STR0042"Ventas ano(C)AP % +/-"
	#define	STR0043"Ventas ano($)"
	#define	STR0044"Ventas ano($)AP % +/-"
	#define	STR0045"Pre.Medio ano"
	#define	STR0046"Pre.Medio Ano AP % +/-"
	#define	STR0047"% Ventas ano(C)"
	#define	STR0048"% Ventas ano($)"
	#define	STR0049"% Devolucion($)"
	#define	STR0050"Remarcacion ano($)"
	#define	STR0051"% POV Ano"
	#define	STR0052"% BCP Ano"
	#define	STR0053"% BCP Ano anterior"
	#define	STR0054"Semana"
	#define	STR0055"De comprador"
	#define	STR0056"A comprador"
	#define	STR0057"¿Selecciona sucursales?"
	#define	STR0058"Ordena por"
	#define	STR0059"Drill"
	#define	STR0060"Indicadores"
	#define	STR0061"Informe los parametros"
	#define	STR0062"Seleccion de sucursales"
	#define	STR0063"Semana:"
	#define	STR0064" a"
#else
	#ifdef ENGLISH
		#define STR0001"Commercial Management"
		#define STR0002"Analyze by:"
		#define STR0003"Categories x Stores"
		#define STR0004"Stores x Categories"
		#define STR0005"Indicators:"
		#define STR0006"Week"
		#define STR0007"Month"
		#define STR0008"Year"
		#define STR0009"No Permission"
		#define STR0010"Management Summary"
		#define STR0011"Loading Commercial Management..."
		#define STR0012"Management Summary"
		#define STR0013"Building Query..."
		#define STR0014"Sales Week(Q)"
		#define STR0015"Sales Week($)"
		#define STR0016"Sales Month(Q)"
		#define STR0017"Sales Month(Q)AP % +/-"
		#define STR0018"Sales Month(%)"
		#define STR0019"Sales Month($)AP % +/-"
		#define STR0020"Month Stock(Q)"
		#define STR0021"Month Stock(Q)AP % +/-"
		#define STR0022"Month Coverage(Q)"
		#define STR0023"Month Stock($)"
		#define STR0024"Month Stock($)AP % +/-"
		#define STR0025"Month Coverage($)"
		#define STR0026"Average.Pc Month"
		#define STR0027"Aver.Pc Month AP % +/-"
		#define STR0028"Average.Pc. Stock"
		#define STR0029"% Sales Month(Q)"
		#define STR0030"% Sales Month(%)"
		#define STR0031"% Stock(Q)"
		#define STR0032"% Sales Stock($)"
		#define STR0033"Turn(Q)"
		#define STR0034"Turn($)"
		#define STR0035"% Mkp Stock"
		#define STR0036"Remark Week($)"
		#define STR0037"Remark Month($)"
		#define STR0038"% POV Month"
		#define STR0039"% BCP Week"
		#define STR0040"% BCP Month"
		#define STR0041"Sales Year(Q)"
		#define STR0042"Sales Year(Q)AP % +/-"
		#define STR0043"Sales Year($)"
		#define STR0044"Sales Year($)AP % +/-"
		#define STR0045"Average.Pc. Year"
		#define STR0046"Aver.Pc Year AP % +/-"
		#define STR0047"% Sales Year(Q)"
		#define STR0048"% Sales Year($)"
		#define STR0049"% Return($)"
		#define STR0050"Remark Year($)"
		#define STR0051"% POV Year"
		#define STR0052"% BCP Year"
		#define STR0053"% BCP Previous Year"
		#define STR0054"Week"
		#define STR0055"From Buyer"
		#define STR0056"To Buyer"
		#define STR0057"Select Branches?"
		#define STR0058"Order by"
		#define STR0059"Drill"
		#define STR0060"Indicators"
		#define STR0061"Enter Parameters"
		#define STR0062"Select Branches"
		#define STR0063"Week: "
		#define STR0064" a "
	#else
		#define STR0001"Gestao Comercial"
		#define STR0002"Analisar por:"
		#define STR0003"Categorias x Lojas"
		#define STR0004"Lojas x Categorias"
		#define STR0005"Indicadores:"
		#define STR0006"Semana"
		#define STR0007"Mes"
		#define STR0008"Ano"
		#define STR0009"Sem Permissao"
		#define STR0010"Resumo Gerencial"
		#define STR0011"Carregando Gestao Comercial..."
		#define STR0012"Resumo Gerencial"
		#define STR0013"Montando Consulta..."
		#define STR0014"Vendas Semana(Q)"
		#define STR0015"Vendas Semana($)"
		#define STR0016"Vendas Mes(Q)"
		#define STR0017"Vendas Mes(Q)AP % +/-"
		#define STR0018"Vendas Mes($)"
		#define STR0019"Vendas Mes($)AP % +/-"
		#define STR0020"Estoque Mes(Q)"
		#define STR0021"Estoque Mes(Q)AP % +/-"
		#define STR0022"Cobertura Mes(Q)"
		#define STR0023"Estoque Mes($)"
		#define STR0024"Estoque Mes($)AP % +/-"
		#define STR0025"Cobertura Mes($)"
		#define STR0026"Pr.Medio Mes"
		#define STR0027"Pr.Medio Mes AP % +/-"
		#define STR0028"Pr.Medio Est."	
		#define STR0029"% Vendas Mes(Q)"
		#define STR0030"% Vendas Mes($)"
		#define STR0031"% Estoque(Q)"
		#define STR0032"% Vendas Estoque($)"
		#define STR0033"Giro(Q)"
		#define STR0034"Giro($)"
		#define STR0035"% Mkp Estoque"
		#define STR0036"Remarcacao Semana($)"
		#define STR0037"Remarcacao Mes($)"
		#define STR0038"% POV Mes"
		#define STR0039"% BCP Semana"
		#define STR0040"% BCP Mes"
		#define STR0041"Vendas Ano(Q)"
		#define STR0042"Vendas Ano(Q)AP % +/-"
		#define STR0043"Vendas Ano($)"
		#define STR0044"Vendas Ano($)AP % +/-"
		#define STR0045"Pr.Medio Ano"
		#define STR0046"Pr.Medio Ano AP % +/-"
		#define STR0047"% Vendas Ano(Q)"
 		#define STR0048"% Vendas Ano($)"
		#define STR0049"% Devolucao($)"
		#define STR0050"Remarcacao Ano($)"
		#define STR0051"% POV Ano"
		#define STR0052"% BCP Ano"
		#define STR0053"% BCP Ano Anterior"
		#define STR0054"Semana"
		#define STR0055"Do Comprador"
		#define STR0056"Ate Comprador"
		#define STR0057"Seleciona Filiais?"
		#define STR0058"Ordena por"
		#define STR0059"Drill"
		#define STR0060"Indicadores"
		#define STR0061"Informe os Parametros"
		#define STR0062"Selecao de Filiais"
		#define STR0063"Semana:"
		#define STR0064" a"
		#define STR0065"Considera CD?"
	#endif
#endif