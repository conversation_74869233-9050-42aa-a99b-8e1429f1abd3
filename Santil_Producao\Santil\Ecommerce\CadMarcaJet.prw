#Include 'Protheus.ch'

/*/{Protheus.doc} CadMarcJet
@description 	Cadastro de Marcas E-commerce
<AUTHOR>
@version		1.0
@since 			30/09/2015
@type 			Function
/*/

USER FUNCTION CadMarcJet()
 
PRIVATE cCadastro  := "Cadastro de Marcas E-commerce"
PRIVATE aRotina     :=  { {"Pesquisar" ,"AxPesqui",0,1} ,; 
                        {"Visualizar","AxVisual",0,2} ,; 
                        {"Incluir"   ,"AxInclui",0,3} ,; 
                        {"Alterar"   ,"AxAltera",0,4} ,; 
                        {"Excluir"   ,"AxDeleta",0,5} } 
   Private cDelFunc := ".T." // Validacao para a exclusao. 
   Private cString := "PB4" 

dbselectarea('PB4')
dbsetorder(1)

 
AxCadastro("PB4", cCadastro, ".T.", ".T." )
 
Return Nil