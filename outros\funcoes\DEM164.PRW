#INCLUDE "RWMAKE.CH"
#INCLUDE "TOPCONN.CH"
                                                                                                                                                                                                                                                                                                                                                                                                 
User function dem164(aPar)

Local oHTML
Local i
Local cEnviapara  
Local aDados

WfPrepEnv(apar[1],apar[2],"U_DEM164")

cEnviapara := '<EMAIL>'//U_ListUser(ALLTRIM(GETMV("MV_JMB039")),14)
oProcess := TWFProcess():New( "Pendencias", "Propostas em elaboracao" )
oProcess:NewTask( "Pendencias Prop.Elaboracao", "\WORKFLOW\HTML\pendpe.html" )
oProcess:cSubject := "Pendencias Prop.Elaboracao"
oHTML := oProcess:oHTML
Orc554a()

//oHtml:valByName('LOGOEMP',"logo"+apar[1]+apar[2])
While TRA->( !EOF() )
	aadd((oHtml:valByName('TB.OC')),TRA->AF1_ORCAME)
  /*	aadd((oHtml:valByName('TB.REVA')),TRA->AF1_REVANT)   
	aadd((oHtml:valByName('TB.DES')),TRA->AF1_DESCRI)
	aadd((oHtml:valByName('TB.VAL')),DTOC(STOD(TRA->AF1_VALID)))   
	aadd((oHtml:valByName('TB.CLI')),TRA->A1_NOME)
	aadd((oHtml:valByName('TB.PRZ')),DTOC(STOD(TRA->AF1_PRZELA)))
	aadd((oHtml:valByName('TB.ORC')),TRA->AF1_NOME)   
	aadd((oHtml:valByName('TB.DTE')),TRA->AF1_ENVIO)   
	aadd((oHtml:valByName('TB.PRV')),DTOC(STOD(TRA->AF1_PREVFE)))
	aadd((oHtml:valByName('TB.UFU')),DTOC(STOD(TRA->AF1_FUP))) */
	TRA->(DbSkip())
Enddo
TRA->(DbCloseArea())
oProcess:cTo := cEnviapara
oProcess:UserSiga := "000000"              
oProcess:Start()            
Return 

Static Function Orc554a()
Local cQuery:=''
Local nRec:=0

cQuery := " SELECT * FROM "+RetSqlName("AF1")+" AS AF1 INNER JOIN "+RetSqlName("SA1")+" AS SA1 ON "
cQuery += " (AF1_CLIENT+AF1_LOJA)=(A1_COD+A1_LOJA) "
cQuery += " WHERE "
cQuery += " AF1.D_E_L_E_T_ ='' AND  SA1.D_E_L_E_T_ ='' ORDER BY AF1_ORCAME "

TCQUERY cQuery NEW ALIAS "TRA" 
//Count to nRec
//TRA->(dbGoTop())

Return