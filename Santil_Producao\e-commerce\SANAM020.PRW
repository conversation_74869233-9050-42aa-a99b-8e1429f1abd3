#include 'protheus.ch'
#include 'topconn.ch'

/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Programa  �Sanwscategory  �Autor  �Felipe <PERSON>� Data �  28/05/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �Rotina que efetua a exporta��o das categorias do JETCOMMERCE���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
User Function SANAM020()

Local oWs := NIL
Local cUserWs  := ""
Local cSenhaWs := ""
Private aLogFull := {}

if !isBlind()
	If nAmbiente = 1 //Produ��o
		cUserWs  := GetMV("ES_USERJET")
		cSenhaWs := GetMV("ES_PSWDJET")
	ElseIf nAmbiente = 2 //Homologa��o
		cUserWs  := GetMV("ES_HUSERJE")
		cSenhaWs := GetMV("ES_HPSWDJE")
	EndIf
else
	cUserWs  := GetMV("ES_USERJET")
	cSenhaWs := GetMV("ES_PSWDJET")
endif

//INSTANCIANDO A WEBSERVICE WSWJET (JETCOMMERCE)
oWSexportCategoryResult := WSWSJET():New()

if isBlind()
	ConectWSCateg(cUserWs, cSenhaWs)
else
	Processa( {|| ConectWSCateg(cUserWs, cSenhaWs) },"Aguarde , Conectando no WS Jet" )
endif

Return aLogFull

//-----------------------------------------------------------
//-----------------------------------------------------------
Static Function ConectWSCateg(cUserWs, cSenhaWs)
Local oWs := NIL
Local aCategoria := {}

//CHAMANDO M�TODO exportCategory ONDE SER�O
//EXPORTADOS PARA A BASE PROTHEUS AS CATEGORIAS
If oWSexportCategoryResult:exportCategory(cUserWs,cSenhaWs,0,"")
	aCategoria:= oWSexportCategoryResult:OWSEXPORTCATEGORYRESULT:OWSCATEGORY
Else
	U_SANAM091("1","Categoria","Não foi possível conexão no WS Categoria, verifique!")
Endif

if Len(aCategoria)>0
	if IsBlind()
		ProcessaCategoria(aCategoria)
	else
	    //ENVIA ARRAY DE CATEGORIAS PARA PROCESSAMENTO E INCLUS�O NA TABELA PA2
		Processa( {|| ProcessaCategoria(aCategoria) },"Aguarde Gerando Integração com Categoria" )
	endif
endif


Return


/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Programa  ProcessaCategoria �Autor  �Felipe Santos� Data �28/05/14     ���
�������������������������������������������������������������������������͹��
���Desc.     �Rotina que efetua a exporta��o das categorias do JETCOMMERCE���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
Static Function ProcessaCategoria(aCategoria)

Local cArea:= ""
Local n

DbSelectArea("PA2")
DbSetOrder(1)

If len(aCategoria) >0

	For n := 1 to len(aCategoria)
	
		if !IsBlind()
			IncProc("Verificando Categoria: "+aCategoria[n]:CNAME)
		else
			conout("Verificando Categoria: "+aCategoria[n]:CNAME)
		endif

		//Caso exista o registro na tabela PA2
		If PA2->(DbSeek(xFilial("PA2") + aCategoria[n]:CIDCATEGORY))
			If PA2->PA2_XSTATU <> "A"  //Se o registro estiver I = Inativo atualiza para A = Ativo
				RecLock("PA2",.F.)
				PA2->PA2_XSTATU	:= "A"
				PA2->PA2_XDATAC := dDataBase
				PA2->(MsUnLock())
				AADD(aLogFull,{"Alteração", PA2->PA2_XIDCAT, PA2->PA2_XNOMEC, "A", dDataBase})
			ElseIf PA2->PA2_XNOMEC <> SUBSTR(aCategoria[n]:CNAME,1,TamSX3("PA2_XNOMEC")[1]) //Se o registro estiver com o nome da categoria diferente ent�o altera para a nova
				RecLock("PA2",.F.)
				PA2->PA2_XNOMEC	:= aCategoria[n]:CNAME
				PA2->PA2_XDATAC := dDataBase
				PA2->(MsUnLock())
				AADD(aLogFull,{"Alteração", PA2->PA2_XIDCAT, PA2->PA2_XNOMEC, "A", dDataBase})
			EndIf
		Else
			//Insere o novo registro
			RecLock("PA2",.T.)
			PA2->PA2_XIDCAT	:= aCategoria[n]:CIDCATEGORY
			PA2->PA2_XNOMEC := aCategoria[n]:CNAME
			PA2->PA2_XSTATU := "A"
			PA2->PA2_XDATAC := dDataBase
			PA2->(MsUnLock())

			AADD(aLogFull,{"Inclusão", aCategoria[n]:CIDCATEGORY, aCategoria[n]:CNAME, "A", dDataBase})
		EndIf

	Next n

EndIf

VerificaAtivos(aCategoria)

//MsgInfo("Opera��o realizada com sucesso")

DbCloseArea("PA2")

Return aLogFull


/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Programa  VerificaAtivos �Autor  �Felipe Santos� Data �28/05/14       ���
�������������������������������������������������������������������������͹��
���Desc.     �Rotina que Inativa as categorias que n�o existem mais       ���
�������������������������������������������������������������������������͹��
���Uso       � SANTIL   												  ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/
Static Function VerificaAtivos(aCategoria)

Local _cQry 	:= ""					//Monta a QUERY
Local _aArea	:= GetArea()			//Salva a Area atual
Local _cAlias	:= GetNextAlias()		//Busca o proximo Alias disponivel
Local _aCat		:= {}					//Array contendo os recnos das tabelas DC3, DC2 e DC8
Local n

DbSelectArea("PA2")
DbSetOrder(1)

_cQry := "SELECT PA2_XIDCAT FROM " + RetSqlName("PA2") + " PA2"
_cQry += " WHERE PA2_FILIAL     = '" + xFilial("PA2") + "'"
_cQry += "   AND PA2_XSTATU     = 'A'"
_cQry += "   AND PA2.D_E_L_E_T_ = ' '"
_cQry += " ORDER BY PA2_XIDCAT"


TcQuery _cQry New Alias (_cAlias)

//Envia para Array para compara��o
If len(aCategoria) >0
	For n := 1 to len(aCategoria)
		AADD(_aCat,Alltrim(aCategoria[n]:CIDCATEGORY))
	Next n
EndIf

While !(_cAlias)->( Eof() )
	If aScan(_aCat, AllTrim((_cAlias)->PA2_XIDCAT) ) == 0

		PA2->(DbSeek(xFilial("PA2") + (_cAlias)->PA2_XIDCAT))
		RecLock("PA2",.F.)
		PA2->PA2_XSTATU	:= "I"
		PA2->PA2_XDATAC := dDataBase
		PA2->(MsUnLock())
		AADD(aLogFull,{"Inclusão", (_cAlias)->PA2_XIDCAT, PA2->PA2_XNOMEC, PA2->PA2_XSTATU, dDataBase})

	EndIf

	(_cAlias)->( dbSkip() )
EndDo

(_cAlias)->( dbCloseArea() )

RestArea(_aArea)

Return