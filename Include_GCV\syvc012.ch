#ifdef SPANISH
	#define STR0001"Parametros"
	#define STR0002"Calendario"
	#define STR0003"Categorias"
	#define STR0004"Historial de metas"
	#define STR0005"Historial de stocks"
	#define STR0006"Resumen de cartera"
	#define STR0007"Resumen de gestion"
	#define STR0008"Open to Buy"
	#define STR0009"Cant. Actual:"
	#define	STR0010"Valor actual:"
	#define	STR0011"Mkp Actual:"
	#define	STR0012"Mkp Deseado:"
	#define	STR0013"Categoria:"
	#define	STR0014"Cartera / Ventas / Remarcacion / Stock / Open To Buy / Giro / BCP"
	#define	STR0015"Atencion"
	#define	STR0016"¿Desea grabar modificaciones?"
	#define	STR0017"Si"
	#define	STR0018"No"
	#define	STR0019"Espere, grabando modificaciones..."
	#define	STR0020"Semana"
	#define	STR0021"Cart.(C)"
	#define	STR0022"Cart.($)"
	#define	STR0023"Cart.(PM)"
	#define	STR0024"Cart.Mkp(%)"
	#define	STR0025"Hist.Venta(C)"
	#define	STR0026"Hist.Venta($)"
	#define	STR0027"Hist.Venta(PM)"
	#define	STR0028"Meta venta(C)"
	#define	STR0029"Meta venta($)"
	#define	STR0030"Meta venta(PM)"
	#define	STR0031"+-% Venta(C)"
	#define	STR0032"+-% Venta($)"
	#define	STR0033"Meta REM($)"
	#define	STR0034"REM/POV(%)"
	#define	STR0035"EFCalc(C)"
	#define	STR0036"EFCalc($)"
	#define	STR0037"MUPSTK %"
	#define	STR0038"COB(C)"
	#define	STR0039"E.Meta(C)"
	#define	STR0040"COB($)"
	#define	STR0041"E.Meta($)"
	#define	STR0042"E.Meta (PM)"
	#define	STR0043"OTB Per.(C)"
	#define	STR0044"OTB Per.($)"
	#define	STR0045"OTB Acum.(C)"
	#define	STR0046"OTB Acum.($)"
	#define	STR0047"Giro Calc."
	#define	STR0048"BCP Calc."
	#define	STR0049"T O T A L"
	#define	STR0050"Atencion"
	#define	STR0051"¿Desea grabar modificaciones?"
	#define	STR0052"Si"
	#define	STR0053"No"
	#define	STR0054"Espere, grabando modificaciones..."
	#define	STR0055"TODAS"
	#define	STR0056"Espere, cargando datos..."
#else
	#ifdef ENGLISH
		#define STR0001"Parameters"
		#define STR0002"Schedule"
		#define STR0003"Categories"
		#define STR0004"Target History"
		#define STR0005"Stock History"
		#define STR0006"Portfolio Summary"
		#define STR0007"Management Summary"
		#define STR0008"Open to Buy"
		#define STR0009"Qtty. Current: "
		#define STR0010"Current Value: "
		#define STR0011"Current Mkp: "
		#define STR0012"Desired Mkp: "
		#define STR0013"Category: "
		#define STR0014"Portfolio / Sales / Remark / Stock / Open To Buy / Turn / BCP"
		#define STR0015"Warning!"
		#define STR0016"Save changes?"
		#define STR0017"Yes"
		#define STR0018"No"
		#define STR0019"Wait, Saving changes..."
		#define STR0020"Week"
		#define STR0021"Port.(Q)"
		#define STR0022"Portfolio($)"
		#define STR0023"Port.(PM)"
		#define STR0024"Port.Mkp(%)"
		#define STR0025"Sales Hist.(Q)"
		#define STR0026"Sales Hist.($)"
		#define STR0027"Sales Hist.(AP)"
		#define STR0028"Target Sales(Q)"
		#define STR0029"Target Sales($)"
		#define STR0030"Target Sales(AP)"
		#define STR0031"+-% Sales(Q)"
		#define STR0032"+-% Sales($)"
		#define STR0033"Target REM($)"
		#define STR0034"REM/POV(%)"
		#define STR0035"EFCalc(Q)"
		#define STR0036"EFCalc($)"
		#define STR0037"MUPSTK %"
		#define STR0038"COB(Q)"
		#define STR0039"E.Target(Q)"
		#define STR0040"COB($)"
		#define STR0041"E.Target($)"
		#define STR0042"E.Target(AP)"
		#define STR0043"OTB Per.(Q)"
		#define STR0044"OTB Per.($)"
		#define STR0045"OTB Acum.(Q)"
		#define STR0046"OTB Acum.($)"
		#define STR0047"Calc. Turn"
		#define STR0048"Calc. BCP"
		#define STR0049"T O T A L"
		#define STR0050"Warning!"
		#define STR0051"Save changes?"
		#define STR0052"Yes"
		#define STR0053"No"
		#define STR0054"Wait, Saving changes..."
		#define STR0055"ALL"
		#define STR0056"Wait, Loading Data..."
	#else
		#define STR0001"Parametros"
		#define STR0002"Calendario"
		#define STR0003"Categorias"
		#define STR0004"Historico de Metas"
		#define STR0005"Historico de Estoques"
		#define STR0006"Resumo de Carteira"
		#define STR0007"Resumo Gerencial"
		#define STR0008"Open to Buy"
		#define STR0009"Qtde. Atual:"
		#define STR0010"Valor Atual:"
		#define STR0011"Mkp Atual:"
		#define STR0012"Mkp Desejado:"
		#define STR0013"Categoria:"
		#define STR0014"Carteira / Vendas / Remarcacao / Estoque / Open To Buy / Giro / BCP"
		#define STR0015"Atencao!"
		#define STR0016"Deseja salvar modificacoes?"
		#define STR0017"Sim"
		#define STR0018"Nao"
		#define STR0019"Aguarde, Salvando alteracoes..."
		#define STR0020"Semana"
		#define STR0021"Cart.(Q)"
		#define STR0022"Cart.($)"
		#define STR0023"Cart.(PM)"
		#define STR0024"Cart.Mkp(%)"
		#define STR0025"Hist.Venda(Q)"
		#define STR0026"Hist.Venda($)"
		#define STR0027"Hist.Venda(PM)"
		#define STR0028"Meta Venda(Q)"
		#define STR0029"Meta Venda($)"
		#define STR0030"Meta Venda(PM)"
		#define STR0031"+-% Venda(Q)"
		#define STR0032"+-% Venda($)"
		#define STR0033"Meta REM($)"
		#define STR0034"REM/POV(%)"
		#define STR0035"EFCalc(Q)"
		#define STR0036"EFCalc($)"
		#define STR0037"MUPSTK %"
		#define STR0038"COB(Q)"
		#define STR0039"E.Meta(Q)"
		#define STR0040"COB($)"
		#define STR0041"E.Meta($)"
		#define STR0042"E.Meta (PM)"
		#define STR0043"OTB Per.(Q)"
		#define STR0044"OTB Per.($)"
		#define STR0045"OTB Acum.(Q)"
		#define STR0046"OTB Acum.($)"
		#define STR0047"Giro Calc."
 		#define STR0048"BCP Calc."
		#define STR0049"T O T A L"
		#define STR0050"Atencao!"
		#define STR0051"Deseja salvar modificacoes?"
		#define STR0052"Sim"
		#define STR0053"Nao"
		#define STR0054"Aguarde, Salvando alteracoes..."
		#define STR0055"TODAS"
		#define STR0056"Aguarde, Carregando Dados..."
	#endif
#endif