#include "rwmake.ch"
/*
Analista 	: <PERSON>
Data     	: 30/09/2006
Rotina 		: Ponto de entrada no fina da rotina de custo de reposicao - mata320
*/
** Alterado por: <PERSON> - amjg<PERSON><EMAIL> - Em: 30/09/2006

User Function M320Fim

cSubject		:= 'Recalculo do Custo de Reposicao: ' + Alltrim(SM0->M0_NOME) + ' - ' + Alltrim(SM0->M0_FILIAL)
cFrom			:= 'Servidor Protheus - Aviso <<EMAIL>>'
cBCC			:= ''
cTo			:= '<EMAIL>'
cCC			:= ''
lHtml       := .f.
cBody			:= "<P>"
cBody			+= "Recalculo efetuado com sucesso <P>"
cBody			+= "Empresa : "+Alltrim(SM0->M0_NOME)+' - '+Alltrim(SM0->M0_FILIAL)+" <P>"
cBody			+= " <P>"
cBody			+= "Setor : Estoque <P>"
cBody			+= " <P>"
cBody			+= ".<P>"

U_MandaEmail(cTo,cCC,cBCC,cSubject,cFrom,cBody,lHtml)

Return