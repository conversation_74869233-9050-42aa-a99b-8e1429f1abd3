#ifdef SPANISH
	#define STR0001 "Geracao de Arquivo Texto"
	#define STR0002 " Este programa ira importar inventario, "
	#define STR0003 " conforme valores do arquivo."
	#define STR0004 "Processando..."
	#define STR0005 "Arquivo Texto..."
	#define STR0006 "Armazem"
	#define STR0007 "Informe o Codigo do Documento..."
	#define STR0008 "Arquivo Nao Encontrado no Local de Origem Indicado!"
	#define STR0009 "O arquivo de nome "
	#define STR0010 " nao pode ser aberto! Verifique os parametros."
	#define STR0011 "Atencao!"
	#define STR0012 "Lendo Arquivo..."
	#define STR0013 "Processando Produto..."
	#define STR0014 "Total de Registros Importados: "
	#define STR0015 "Produtos Nao Importados: "
	#define STR0016 "Incluindo Produto..."
	#define STR0017 "Produto Incluido com sucesso..."
	#define STR0018 "Total de Registros Importados: "
	#define STR0019 "Produtos Nao Importados: "
	#define STR0020 "Selecione o Arquivo"
	#define STR0021 "Arquivos Texto"
	#define STR0022 "Atualizacao concluida."
#else
	#ifdef ENGLISH
		#define STR0001 "Geracao de Arquivo Texto"
		#define STR0002 " Este programa ira importar inventario, "
		#define STR0003 " conforme valores do arquivo."
		#define STR0004 "Processando..."
		#define STR0005 "Arquivo Texto..."
		#define STR0006 "Armazem"
		#define STR0007 "Informe o Codigo do Documento..."
		#define STR0008 "Arquivo Nao Encontrado no Local de Origem Indicado!"
		#define STR0009 "O arquivo de nome "
		#define STR0010 " nao pode ser aberto! Verifique os parametros."
		#define STR0011 "Atencao!"
		#define STR0012 "Lendo Arquivo..."
		#define STR0013 "Processando Produto..."
		#define STR0014 "Total de Registros Importados: "
		#define STR0015 "Produtos Nao Importados: "
		#define STR0016 "Incluindo Produto..."
		#define STR0017 "Produto Incluido com sucesso..."
		#define STR0018 "Total de Registros Importados: "
		#define STR0019 "Produtos Nao Importados: "
		#define STR0020 "Selecione o Arquivo"
		#define STR0021 "Arquivos Texto"
		#define STR0022 "Atualizacao concluida."
	#else
		#define STR0001 "Geracao de Arquivo Texto"
		#define STR0002 " Este programa ira importar inventario, "
		#define STR0003 " conforme valores do arquivo."
		#define STR0004 "Processando..."
		#define STR0005 "Arquivo Texto..."
		#define STR0006 "Armazem"
		#define STR0007 "Informe o Codigo do Documento..."
		#define STR0008 "Arquivo Nao Encontrado no Local de Origem Indicado!"
		#define STR0009 "O arquivo de nome "
		#define STR0010 " nao pode ser aberto! Verifique os parametros."
		#define STR0011 "Atencao!"
		#define STR0012 "Lendo Arquivo..."
		#define STR0013 "Processando Produto..."
		#define STR0014 "Total de Registros Importados: "
		#define STR0015 "Produtos Nao Importados: "
		#define STR0016 "Incluindo Produto..."
		#define STR0017 "Produto Incluido com sucesso..."
		#define STR0018 "Total de Registros Importados: "
		#define STR0019 "Produtos Nao Importados: "
		#define STR0020 "Selecione o Arquivo"
		#define STR0021 "Arquivos Texto"
		#define STR0022 "Atualizacao concluida."
	#endif
#endif