#include 'PROTHEUS.CH'
#include 'TOPCONN.CH'

User Function AFATM017()

Local _cQry := ""

_cQry := " UPDATE 											" + CRLF
_cQry += 		RetSqlName("SA1")
_cQry += " SET												" + CRLF
_cQry += " 	A1_MSBLQL = '1'									" + CRLF
_cQry += " WHERE											" + CRLF
_cQry += " 	A1_CGC IN(	SELECT								" + CRLF
_cQry += " 					A1_CGC							" + CRLF
_cQry += " 				FROM								" + CRLF
_cQry +=  					RetSqlName("SA1")
_cQry += " 				WHERE								" + CRLF
_cQry += " 					D_E_L_E_T_ = ' ' 				" + CRLF
_cQry += " 				AND A1_PESSOA  = 'F'				" + CRLF
_cQry += " 				AND A1_MSBLQL <> '1'				" + CRLF
_cQry += " 				AND A1_CGC    <> ' '				" + CRLF
_cQry += " 				GROUP BY							" + CRLF
_cQry += " 					A1_CGC							" + CRLF
_cQry += " 				HAVING COUNT(*) > 1)				" + CRLF
_cQry += " AND A1_PESSOA  = 'F'								" + CRLF
_cQry += " AND A1_MSBLQL <> '1'								" + CRLF
_cQry += " AND D_E_L_E_T_ = ' '								" + CRLF
_cQry += " AND R_E_C_N_O_ NOT IN(	SELECT 					" + CRLF
_cQry += " 								MAX(R_E_C_N_O_) REC	" + CRLF 
_cQry += " 							FROM					" + CRLF 
_cQry +=  								RetSqlName("SA1")
_cQry += " 							WHERE 					" + CRLF
_cQry += " 								D_E_L_E_T_ = ' ' 	" + CRLF
_cQry += " 							AND A1_PESSOA  = 'F'	" + CRLF
_cQry += " 							AND A1_MSBLQL <> '1'	" + CRLF
_cQry += " 							GROUP BY 				" + CRLF
_cQry += " 								A1_CGC 				" + CRLF
_cQry += " 							HAVING COUNT(*) > 1)	"

If TCSQLEXEC(_cQry) >= 0
	Alert("CLIENTES DUPLICADOS BLOQUEADOS!")
EndIf

_cQry := " UPDATE					" + CRLF
_cQry +=		RetSqlName("SA1")
_cQry += " SET						" + CRLF
_cQry += "		A1_MSBLQL = '1'		" + CRLF
_cQry += " WHERE					" + CRLF
_cQry += " 		D_E_L_E_T_	= ' '	" + CRLF
_cQry += " AND 	A1_MSBLQL	<> '1'	" + CRLF
_cQry += " AND 	A1_PESSOA	= 'F'	" + CRLF
_cQry += " AND 	A1_CGC		= ' '	"

If TCSQLEXEC(_cQry) >= 0
	Alert("CLIENTES EM BRANCO BLOQUEADOS!")
EndIf

Return