<EASYLINK>
	<SERVICE>
		<ID>006</ID>
		<DATA_SELECTION>
			<CMD>If(Type('nValorBaixa') # 'N', nValorBaixa := 0, )</CMD>
			<CMD>If(Type('dDtBaixa') # 'D', dDtBaixa := dDtEmba, )</CMD>
			<FIN_SEND>
				<FIN_IT>
					<FIN_ELE1>'E1_FILIAL'</FIN_ELE1>
					<E1_FILIAL>xFilial('SE1')</E1_FILIAL>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_NUM'</FIN_ELE1>
					<E1_NUM>SE1->E1_NUM</E1_NUM>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_PREFIXO'</FIN_ELE1>
					<E1_PREFIXO>SE1->E1_PREFIXO</E1_PREFIXO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_SERIE'</FIN_ELE1>
					<E1_SERIE>SE1->E1_SERIE</E1_SERIE>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_PARCELA'</FIN_ELE1>
					<E1_PARCELA>SE1->E1_PARCELA</E1_PARCELA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_TIPO'</FIN_ELE1>
					<E1_TIPO>SE1->E1_TIPO</E1_TIPO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_CLIENTE'</FIN_ELE1>
					<E1_CLIENTE>SE1->E1_CLIENTE</E1_CLIENTE>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_LOJA'</FIN_ELE1>
					<E1_LOJA>SE1->E1_LOJA</E1_LOJA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTVALREC'</FIN_ELE1>
					<E1_VALOR>nValorBaixa</E1_VALOR>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTTXMOEDA'</FIN_ELE1>
					<E1_TXMOEDA>If(EEQ->EEQ_TIPO == "A" .Or. IsInCallStack("EECBxFiTit"), BuscaTaxa(EEC->EEC_MOEDA, dDtBaixa,,.F.), EEQ->EEQ_TX)</E1_TXMOEDA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTMOTBX'</FIN_ELE1>
					<AUTMOTBX>EECGetMotBx()</AUTMOTBX>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTDTBAIXA'</FIN_ELE1>
					<AUTDTBAIXA>dDtBaixa</AUTDTBAIXA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTBANCO'</FIN_ELE1>
					<E1_PORTADO>EECGetBanco('AUTBANCO')</E1_PORTADO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTAGENCIA'</FIN_ELE1>
					<E1_AGEDEP>EECGetBanco('AUTAGENCIA')</E1_AGEDEP>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTCONTA'</FIN_ELE1>
					<E1_CONTA>EECGetBanco('AUTCONTA')</E1_CONTA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'AUTHIST'</FIN_ELE1>
					<AUTHIST>'Emb.:' + EEC->EEC_PREEMB</AUTHIST>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_NATUREZ'</FIN_ELE1>
					<E1_NATUREZ>If(!Empty(SE1->E1_NATUREZ), SE1->E1_NATUREZ, GetMv("MV_AVG0178",, "EASY"))</E1_NATUREZ>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
			</FIN_SEND>
		</DATA_SELECTION>
		<DATA_SEND>
			<SEND>EECINFIN(#TAG FIN_SEND#, 'SE1', 'BAIXA',,,,If(SE1->E1_PREFIXO == 'EEC','EEQ', 'SE1'))</SEND>
		</DATA_SEND>
		<DATA_RECEIVE>
			<SRV_STATUS>.T.</SRV_STATUS>
		</DATA_RECEIVE>
	</SERVICE>
</EASYLINK>
