#include 'protheus.ch'
#include 'parmtype.ch'

User function SETMKC01(cCampo, cDestino)
Local cRet			:= " "

Default cCampo		:= " "
Default cDestino	:= " "

Do Case

	Case cCampo = "ZA_PRODUTO" .AND. cDestino = "ZA_DESCPRO"
		cRet	:= IIF(EMPTY(M->ZA_PRODUTO), SPACE(TAMSX3("ZA_DESCPRO")[1]), POSICIONE("SB1",1,XFILIAL("SB1")+M->ZA_PRODUTO,"B1_DESC") )

	Case cCampo = "ZA_PRODUTO" .AND. cDestino = "ZA_PROC"
		cRet	:= IIF(EMPTY(M->ZA_PRODUTO), SPACE(TAMSX3("ZA_PROC")[01]), POSICIONE("SB1",1,XFILIAL("SB1")+M->ZA_PRODUTO, "B1_PROC" ))
		
	Case cCampo = "ZA_PRODUTO" .AND. cDestino = "ZA_LJPROC"
		cRet	:= IIF(EMPTY(M->ZA_PRODUTO), SPACE(TAMSX3("ZA_PROC")[01]), POSICIONE("SB1",1,XFILIAL("SB1")+M->ZA_PRODUTO, "B1_LOJPROC" ))
	
	Case (cCampo = "ZA_PROC" .AND. cDestino = "ZA_DESCFOR") .OR. (cCampo = "ZA_PRODUTO" .AND. cDestino = "ZA_DESCFOR")
		cRet	:= IIF(EMPTY(M->ZA_PROC+M->ZA_LJPROC), SPACE(TAMSX3("ZA_DESCFOR")[01]), POSICIONE("SA2",1,XFILIAL("SA2")+M->ZA_PROC+M->ZA_LJPROC,"A2_NREDUZ") ) 
	
	Case cCampo = "ZA_CLIENTE" .AND. cDestino = "ZA_NOMECLI"
		cRet	:= IIF(EMPTY(M->ZA_CLIENTE+M->ZA_LOJACLI), SPACE(TAMSX3("ZA_NOMECLI")[01]), POSICIONE("SA1",1,XFILIAL("SA1")+M->ZA_CLIENTE+M->ZA_LOJACLI,"A1_NREDUZ") )
	
	OtherWise
		cRet	:= " "
End Case
	
return cRet