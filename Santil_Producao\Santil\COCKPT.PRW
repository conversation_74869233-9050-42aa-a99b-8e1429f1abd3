#INCLUDE "PROTHEUS.ch"
#Include 'rwmake.ch'
#Include 'tbiconn.ch'
#INCLUDE 'TOPCONN.CH'
#INCLUDE "FWBROWSE.CH"
#Include "Colors.ch"
#Include "Font.ch"

USER FUNCTION GERAPC5(_cFilial,_cNum)

	Local _lOk			:= .T.
	Local _cStatus	:= "OK"

	Default _cFilial	:= _cFilial
	Default _cNum		:= _cNum
    
    DBSELECTAREA("SUA")
    DBSETORDER(1)
    DBSEEK(xfilial("SUA")+_cNum)
    

	_cStatus	:= U_GetStatSZ3(_cNum)

	If SUA->(RecLock("SUA",.F.))
		SUA->UA_XWFST	:= _cStatus
		SUA->( MsUnlock() )
	Else
		cMsgErro := "SUA"
	EndIf

	If _cStatus == "OK"
		//PATRICIA FONTANEZI
		DbSelectArea("SUB")
		SUB->( DbSetOrder( 1 ) )
		SUB->( DbGoTop() )

		IF SUB->( DbSeek( xFilial( "SUB" ) + SUA->UA_NUM ) )
			Do WHILE SUB->(! EOF() ) .AND. SUA->UA_NUM == SUB->UB_NUM

				IF !Empty(SUB->UB_NUMPVT)
					//				1			, 2		   ,3  ,     4        , 5 , 6 ,              7               ,8  	, 9  	, 10 , 11
					U_CockpitLog(SUB->UB_XFILRE,SUA->UA_NUM," ",SUB->UB_NUMPVT,"N","3",If(!Empty(SUB->UB_NUMPVT),2,1),"N"	,"N"	,	 , SUB->UB_FILIAL)
					msgalert("Gerou PC5")
				Endif

				IF !Empty(SUB->UB_NUMPVT) .AND. Empty(SUB->UB_NUMSL1)
					//				1			, 2		    	,3  	,     4        	, 5 	, 6 				,              7               	,8  	, 9  								, 10 	, 11
					U_CockpitLog(SUB->UB_XLOCSAI,SUA->UA_NUM	," "	,SUB->UB_NUMPVE	,"N"	,SUB->UB_ENTREGA	,If(!Empty(SUB->UB_NUMPVE),2,1)	,"N"	,If(!Empty(SUB->UB_NUMPVT),"S","N")	,		,SUB->UB_FILIAL	)
			   		msgalert("Gerou PC5")
				Endif

				If !Empty(SUB->UB_NUMSL1) .AND. ALLTRIM(SUB->UB_ENTREGA) == '2' .AND. Empty(SUB->UB_NUMPVE)
					//				1			, 2		    ,3  			,     4        	, 5 	, 6 				,              7            ,8  	, 9  								, 10 	, 11
					U_CockpitLog(SUB->UB_XLOCSAI,SUA->UA_NUM,SUB->UB_NUMSL1	,SUB->UB_NUMPVE	,"N"	,SUB->UB_ENTREGA	,1							,"N"	,If(!Empty(SUB->UB_NUMPVT),"S","N")	,		, SUB->UB_FILIAL)
					msgalert("Gerou PC5")
				Endif

				IF !Empty(SUB->UB_NUMPVE) .AND. !Empty(SUB->UB_NUMSL1)
					//				1			, 2		    ,3  			,     4        	, 5 	, 6 				,              7            	,8  	, 9  								, 10 	, 11
					U_CockpitLog(SUB->UB_XLOCSAI,SUA->UA_NUM," "			,SUB->UB_NUMPVE	,"N"	,SUB->UB_ENTREGA	,If(!Empty(SUB->UB_NUMPVE),2,1)	,"N"	,If(!Empty(SUB->UB_NUMPVT),"S","N")	,		, SUB->UB_FILIAL)
				    msgalert("Gerou PC5")
				Endif

				SUB->(DBSKIP())
			ENDDO
		Endif
	Endif

Return .T.
