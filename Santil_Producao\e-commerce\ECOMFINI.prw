#include "totvs.ch"
#include "protheus.ch"
#include "topconn.ch"
#include "tbiconn.ch"

#define CRLF chr(13) + chr(10)
//---------------------------------------------------------------------------------
/*/{Protheus.doc} ECOMFINI

Rotina para concluir pedidos feitos no site

<AUTHOR> - TOTVS Campinas
@since 16/03/2015
@version 1.0
/*/
//---------------------------------------------------------------------------------
user function ECOMFINI(aParam)
	if isBlind()
		if !empty(aParam[3]) .and. !empty(aParam[4])
			conout(" * * * * * * * * PARAMETROS ENVIADOS * * * * * * * * * * * * *")
			conout("aParam[1]" + str(aParam[1]))
			conout("aParam[2]" + str(aParam[2]))
			conout("aParam[3]" + aParam[3])
			conout("aParam[4]" + aParam[4])
			conout("aParam[5]" + aParam[5])
			conout("aParam[6]" + aParam[6])
			conout("Tamanho do parametro: " + str(len(aParam)))
			conout(" * * * * * * * * * * * * * * * * * * * * *")

			PREPARE ENVIRONMENT EMPRESA aParam[3] FILIAL aParam[4]

			conout("----------------------------------------------------------")
			conout("----Iniciando Job - Finalizacao de Pedido - E-Commerce----")
			conout("Hora		> " + Time())
			conout("Empresa	> " + aParam[3])
			conout("Filial	> " + aParam[4])
			conout("----------------------------------------------------------")

			roda()

			conout("-----------------------------------------------------------")
			conout("----Finalizado Job - Finalizacao de Pedido - E-Commerce----")
			conout(" > " + Time())
			conout("-----------------------------------------------------------")

			RESET ENVIRONMENT
		else
			conOut("-------------------------------------------------------------------------------")
			conOut("----- ERRO: Não foram enviados os parâmetros de Empresa/Filial para o JOB -----")
			conOut("-------------------------------------------------------------------------------")
		endif
	else
		msAguarde({|| roda()}, "Aguarde, por favor...", "Finalizacao de Pedidos - E-Commerce") 
	endif
return

//---------------------------------------------------------------------------------
/*/{Protheus.doc} roda

processa job para concluir pedidos do site

<AUTHOR>
@since 16/03/2015
/*/
//---------------------------------------------------------------------------------
static function roda()
	local aArea		:= getArea()
	local aAreaPA4	:= PA4->(getArea())

	pvSite()
	while !QRYSUA->(EOF())
		conout("Finalizando pedido " + QRYSUA->UA_XIDJETC + "...")
		concluiPV()
		QRYSUA->(DBSkip())
	enddo
	QRYSUA->(DBCloseArea())

	restArea(aArea)
	restArea(aAreaPA4)
return

//---------------------------------------------------------------------------------
/*/{Protheus.doc} pvSite

Verifica se é um pedido de venda feito através do site

<AUTHOR>
@since 27/01/2015
/*/
//---------------------------------------------------------------------------------
static function pvSite()
	local cQuery	:= ""

	cQuery := "SELECT UA_NUM, UA_XIDJETC, F2_FILIAL, F2_CLIENTE, F2_LOJA, F2_DOC, F2_SERIE, F2_DTENTR, SF2.R_E_C_N_O_ F2RECNO"		+ CRLF
	cQuery += " FROM			" + retSQLName("SC5") + " SC5"											+ CRLF
	cQuery += " INNER JOIN	" + retSQLName("SUA") + " SUA"											+ CRLF
	cQuery += " ON	SC5.C5_NUMSUA		=	SUA.UA_NUM"												+ CRLF
	cQuery += " INNER JOIN	" + retSQLName("SF2") + " SF2"											+ CRLF
	cQuery += " ON		SC5.C5_NOTA	=	SF2.F2_DOC"												+ CRLF
	cQuery += " 	AND	SC5.C5_SERIE		=	SF2.F2_SERIE"												+ CRLF
	cQuery += " 	AND	SC5.C5_CLIENTE	=	SF2.F2_CLIENTE"											+ CRLF
	cQuery += " 	AND	SC5.C5_LOJACLI	=	SF2.F2_LOJA"												+ CRLF
	cQuery += " WHERE"																					+ CRLF
	cQuery += " 		SC5.D_E_L_E_T_					<>	'*'"										+ CRLF
	cQuery += " 	AND	SUA.D_E_L_E_T_					<>	'*'"										+ CRLF
	cQuery += " 	AND	SF2.D_E_L_E_T_					<>	'*'"										+ CRLF
	cQuery += " 	AND	SC5.C5_FILIAL						=	'" + xFilial("SC5")	+ "'"				+ CRLF
	cQuery += " 	AND	SUA.UA_FILIAL						=	'" + xFilial("SUA")	+ "'"				+ CRLF
	cQuery += " 	AND	SF2.F2_FILIAL						=	'" + xFilial("SF2")	+ "'"				+ CRLF
	cQuery += " 	AND	RTRIM(LTRIM(SUA.UA_XIDJETC))	<>	''"											+ CRLF
	cQuery += "	AND	SF2.F2_XDESPAC					=	'1'"										+ CRLF
	cQuery += "	AND	RTRIM(LTRIM(SF2.F2_CHVNFE))		<>	''"											+ CRLF
	cQuery += "	AND	RTRIM(LTRIM(SF2.F2_DTENTR))		<>	''"											+ CRLF

	conout("Selecionando pedidos...")

	TcQuery cQuery New Alias "QRYSUA"
return

//---------------------------------------------------------------------------------
/*/{Protheus.doc} concluiPV

Altera status do pedido no site para concluído (cod. 5721)

<AUTHOR>
@since 27/01/2015
/*/
//---------------------------------------------------------------------------------
static function concluiPV()
	local cUpdate						:= ""
	local cUserWs						:= GetMV("ES_USERJET")
	local cSenhaWs					:= GetMV("ES_PSWDJET")
	local oWSalterOrderStateResult	:= nil

	oWSalterOrderStateCompleteResult := WSWSJET():New()

	if !oWSalterOrderStateCompleteResult:alterOrderStateComplete(cUserWs, cSenhaWs, val(QRYSUA->UA_XIDJETC), 5721, "Alteração via automática (PE OM320GRV Protheus)")
		if isBlind()
			conout("Erro ao concluir pedido " + QRYSUA->UA_XIDJETC + " no site!" + CRLF + GetWSCError())
		else
			aviso("Erro", "Erro ao concluir pedido " + QRYSUA->UA_XIDJETC + " no site!" + CRLF + GetWSCError(), {"Ok"})
		endif
	else
		DBSelectArea("PA4")
		PA4->(DBSetOrder(1))
		PA4->(DBGoTop())
		if PA4->(DBSeek(xFilial("PA4") + QRYSUA->UA_NUM))
			RecLock("PA4", .F.)
				PA4->PA4_IDSTAT := "5721"
			PA4->(MSUnLock())

			if isBlind()
				conout("Status do Pedido: " + allTrim(QRYSUA->UA_XIDJETC) + " foi concluído com sucesso no E-COMMERCE!")
			else
				msgAlert("Status do Pedido: " + allTrim(QRYSUA->UA_XIDJETC) + " foi concluído com sucesso no E-COMMERCE!")
			endif
		endif
		PA4->(DBCloseArea())

		cUpdate := " UPDATE " + retSQLName("SF2")						+ CRLF
		cUpdate += " SET F2_XDESPAC = '3'"								+ CRLF
		cUpdate += " WHERE"												+ CRLF
		cUpdate += "	R_E_C_N_O_ = " + cValToChar(QRYSUA->F2RECNO)	+ CRLF

		if TCSQLExec(cUpdate) < 0
			conout("Erro na atualização do BD " + CRLF + TCSQLError())
		endif
	endif
return
