#Include "rwmake.ch"

/*
Este PE trabalha em conjunto com o PE F460SE1
*/
User Function F460VAL()

Local aAlias	:= GetArea()
Local aRetorno	:= PARAMIXB
Local nPosEmit := aScan(aHeader,{|x| AllTrim(x[2])=="E1_EMITCHQ"})
Local nPosHist := aScan(aHeader,{|x| AllTrim(x[2])=="E1_HIST"})

For nXX := 1 to Len( aRetorno )
	RecLock( "SE1" , .F. )
	cCampo		:= aRetorno[ nXX , 1 ]
	cConteudo	:= aRetorno[ nXX , 2 ]
	&(cCampo)	:= cConteudo
   SE1->E1_EMITCHQ := aCols[__laCo,nPosEmit]
   SE1->E1_HIST    := aCols[__laCo,nPosHist]
	SE1->( MsUnLock() )
Next nXX

RestArea( aAlias )
Return