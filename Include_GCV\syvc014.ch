#ifdef SPANISH
	#define STR0001"Seleccion de marcas"
	#define STR0002"Seleccion de proveedores"
	#define STR0003"Seleccion de sucursales"
	#define STR0004"Espere"
	#define STR0005"Efectuando consulta..."
	#define STR0006"Mov.Producto"
	#define STR0007"Imprimir"
	#define STR0008"Ranking de ventas"
	#define STR0009"Periodo seleccionado: De"
	#define	STR0010" a"
	#define	STR0011"Stock"
	#define	STR0012"Cartera"
	#define	STR0013"Venta periodo"
	#define	STR0014"MarkUp"
	#define	STR0015"Venta Per. Anterior"
	#define	STR0016"Cant. Defecto"
	#define	STR0017"Total costo"
	#define	STR0018"Dev. Solicitada"
	#define	STR0019"Dev. Retirada"
	#define	STR0020"Producto"
	#define	STR0021"Marca"
	#define	STR0022"Referencia"
	#define	STR0023"Precio($)"
	#define	STR0024"Costo($)"
	#define	STR0025"Ventas Per.(C)"
	#define	STR0026"Ventas Per.Ant.(C)"
	#define	STR0027"Stock(C)"
	#define	STR0028"Cartera(C)"
	#define	STR0029"Markup"
	#define	STR0030"Cobertura(Sem)"
	#define	STR0031"No fue posible borrar la tabla temporal."
	#define	STR0032"El parametro de fecha esta incorrecto."
	#define	STR0033"El parametro de stock esta en blanco."
	#define	STR0034"Este programa emitira un ranking de productos por"
	#define	STR0035"Precio o cantidad en moneda actual."
	#define	STR0036"Ranking de ventas"
	#define	STR0037"Stock"
	#define	STR0038"Stock valor"
	#define	STR0039"Stock costo"
	#define	STR0040"Stock Markup"
	#define	STR0041"Cartera"
	#define	STR0042"Cartera valor"
	#define	STR0043"Cartera costo"
	#define	STR0044"Cartera Markup"
	#define	STR0045"Venta periodo"
	#define	STR0046"Venta valor"
	#define	STR0047"Venta costo"
	#define	STR0048"Venta Markup"
	#define	STR0049"Venta Per. Anterior"
	#define	STR0050"Venta Ant. Valor"
	#define	STR0051"Venta Ant. Costo"
	#define	STR0052"Venta Ant. Markup"
	#define	STR0053"Defectos"
	#define	STR0054"Cant. Defecto"
	#define	STR0055"Total costo"
	#define	STR0056"Dev. Solicitada"
	#define	STR0057"Total costo"
	#define	STR0058"Dev.Retirada"
	#define	STR0059"Total costo"
	#define STR0060 "Descripcion"
	#define STR0061 "Vendas Per.($)"
	#define STR0062 "Custo Vendas($)"
	#define STR0063 "Vendas Per.Ant.($)"
	#define STR0064 "Custo Vendas Ant.($)"
	#define STR0065 "Est.Custo($)"
	#define STR0066 "Est.Venda($)"
	#define STR0067 "Cart.Custo($)"
	#define STR0068 "Cart.Venda($)"
	#define STR0069 "Estoque Qtd."
	#define STR0070 "Venda Qtd."
	#define STR0071 "Carteira Qtd."
#else
	#ifdef ENGLISH
		#define STR0001"Select Brands"
		#define STR0002"Select Suppliers"
		#define STR0003"Select Branches"
		#define STR0004"Wait"
		#define STR0005"Running inquiry..."
		#define STR0006"Product Mov."
		#define STR0007"Print"
		#define STR0008"Sales Ranking"
		#define STR0009"Selected Period: From "
		#define STR0010" to "
		#define STR0011"Stock"
		#define STR0012"Portfolio"
		#define STR0013"Sales Period"
		#define STR0014"Markup"
		#define STR0015"Sales Per. Previous"
		#define STR0016"Qtty. Defect"
		#define STR0017"Total Cost"
		#define STR0018"Ret. Requested"
		#define STR0019"Ret. Removal"
		#define STR0020"Product"
		#define STR0021"Brand"
		#define STR0022"Reference"
		#define STR0023"Price($)"
		#define STR0024"Cost($)"
		#define STR0025"Sales Per.(Q)"
		#define STR0026"Prev.Sales Per.(Q)"
		#define STR0027"Stock(Q)"
		#define STR0028"Portfolio(Q)"
		#define	STR0029"Markup"
		#define STR0030"Coverage(Week)"
		#define STR0031"It was not possible to delete the temporary table."
		#define STR0032"Date parameter incorrect!"
		#define STR0033"Stock parameter blank!"
		#define STR0034"This program will issue a Ranking of Products by"
		#define STR0035"Price or Quantity in Current Currency."
		#define STR0036"Sales Ranking"
		#define STR0037"Stock"
		#define STR0038"Stock Value"
		#define STR0039"Stock Cost"
		#define STR0040"Markup Stock"
		#define STR0041"Portfolio"
		#define STR0042"Portfolio Value"
		#define STR0043"Portfolio Cost"
		#define STR0044"Markup Portfolio"
		#define STR0045"Sales Period"
		#define STR0046"Sales Value"
		#define STR0047"Sales Cost"
		#define STR0048"Markup Sales"
		#define STR0049"Sales Per. Previous"
		#define STR0050"Prev. Sales Value"
		#define STR0051"Prev. Sales Cost"
		#define STR0052"Prev. Sales Markup"
		#define STR0053"Defects"
		#define STR0054"Qtty. Defect"
		#define STR0055"Total Cost"
		#define STR0056"Ret. Requested"
		#define STR0057"Total Cost"
		#define STR0058"Ret Removal"
		#define STR0059"Total Cost"
		#define STR0060 "Description"
		#define STR0061 "Vendas Per.($)"
		#define STR0062 "Custo Vendas($)"
		#define STR0063 "Vendas Per.Ant.($)"
		#define STR0064 "Custo Vendas Ant.($)"
		#define STR0065 "Est.Custo($)"
		#define STR0066 "Est.Venda($)"
		#define STR0067 "Cart.Custo($)"
		#define STR0068 "Cart.Venda($)"
		#define STR0069 "Estoque Qtd."
		#define STR0070 "Venda Qtd."
		#define STR0071 "Carteira Qtd."
	#else
		#define STR0001"Selecao de Marcas"
		#define STR0002"Selecao de Fornecedores"
		#define STR0003"Selecao de Filiais"
		#define STR0004"Aguarde"
		#define STR0005"Efetuando consulta..."
		#define STR0006"Mov.Produto"
		#define STR0007"Imprimir"
		#define STR0008"Ranking de Vendas"
		#define STR0009"Periodo Selecionado: De "
		#define STR0010" ate "
		#define STR0011"Estoque"
		#define STR0012"Carteira"
		#define STR0013"Venda Periodo"
		#define STR0014"MarkUp"
		#define STR0015"Venda Per. Anterior"
		#define STR0016"Qtde. Defeito"
		#define STR0017"Total Custo"
		#define STR0018"Dev. Requisitada"
		#define STR0019"Dev. Retirada"
		#define STR0020"Produto"
		#define STR0021"Marca"
		#define STR0022"Referencia"
		#define STR0023"Prc.Venda($)"
		#define STR0024"Ult.Prc.Compra($)"
		#define STR0025"Vendas Per.(Q)"
		#define STR0026"Vendas Per.Ant.(Q)"
		#define STR0027"Estoque(Q)"
		#define STR0028"Carteira(Q)"
		#define STR0029"Markup"
		#define STR0030"Cobertura(Sem)"
		#define STR0031"Nao foi possivel a exclusao da tabela temporaria."
		#define STR0032"O parametro de data esta incorreto!"
		#define STR0033"O parametro de estoque esta em branco!"
		#define STR0034"Este programa ira emitir um Ranking de Produtos por"
		#define STR0035"Preco ou Quantidade em Moeda Corrente."
		#define STR0036"Ranking de Vendas"
		#define STR0037"Estoque"
		#define STR0038"Estoque Valor"
		#define STR0039"Estoque Custo"
		#define STR0040"Estoque Markup"
		#define STR0041"Carteira"
		#define STR0042"Carteira Valor"
		#define STR0043"Carteira Custo"
		#define STR0044"Carteira Markup"
		#define STR0045"Venda Periodo"
		#define STR0046"Venda Valor"
		#define STR0047"Venda Custo"
 		#define STR0048"Venda Markup"
		#define STR0049"Venda Per. Anterior"
		#define STR0050"Venda Ant. Valor"
		#define STR0051"Venda Ant. Custo"
		#define STR0052"Venda Ant. Markup"
		#define STR0053"Defeitos"
		#define STR0054"Qtde. Defeito"
		#define STR0055"Total Custo"
		#define STR0056"Dev. Requisitada"
		#define STR0057"Total Custo"
		#define STR0058"Dev.Retirada"
		#define STR0059"Total Custo"
		#define STR0060 "Descricao"
		#define STR0061 "Vendas Per.($)"
		#define STR0062 "Custo Vendas($)"
		#define STR0063 "Vendas Per.Ant.($)"
		#define STR0064 "Custo Vendas Ant.($)"
		#define STR0065 "Est.Custo($)"
		#define STR0066 "Est.Venda($)"
		#define STR0067 "Cart.Custo($)"
		#define STR0068 "Cart.Venda($)"
		#define STR0069 "Estoque Qtd."
		#define STR0070 "Venda Qtd."
		#define STR0071 "Carteira Qtd."
	#endif
#endif