<EASYLINK>
<SERVICE>
<ID>007</ID>
<DATA_SELECTION>
   <FIN_SEQ>If(Type('nParcEst') == 'N', nParcEst, 0)</FIN_SEQ>
   <FIN_SEND>
      <FIN_IT>
         <FIN_ELE1>'E1_FILIAL'</FIN_ELE1>
		 <!-- RRC - 23/10/2013 - Alteração para caso a tabela SE1 seja compartilhada, já que o campo E5_FILORIG estava gravando "01", e assim não encontrava o registro.-->
		<!--<E5_FILIAL>If(!Empty(xFilial('SE1')),SE5->E5_FILORIG,xFilial('SE1'))</E5_FILIAL>-->
		<E5_FILIAL>xFilial('SE1')</E5_FILIAL>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_PREFIXO'</FIN_ELE1>
         <E5_PREFIXO>AvKey(SE5->E5_PREFIXO,"E1_PREFIXO")</E5_PREFIXO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_NUM'</FIN_ELE1>
         <E5_NUMERO>Avkey(SE5->E5_NUMERO,"E1_NUM")</E5_NUMERO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_PARCELA'</FIN_ELE1>
         <E5_PARCELA>AvKey(SE5->E5_PARCELA,"E1_PARCELA")</E5_PARCELA>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_TIPO'</FIN_ELE1>
         <E5_TIPO>AvKey(SE5->E5_TIPO,"E1_TIPO")</E5_TIPO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'AUTHIST'</FIN_ELE1>
         <AUTHIST>If(FindFunction('AF200HisEmb') .And. SE5->E5_PREFIXO = 'EEC',AF200HisEmb(),'Emb.:' + EEC->EEC_PREEMB)</AUTHIST>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
   </FIN_SEND>
</DATA_SELECTION>

<DATA_SEND>
   <SEND>EECINFIN(#TAG FIN_SEND#, 'SE1', 'ESTBAIXA',,#TAG FIN_SEQ#,,If(SE5->E5_PREFIXO == 'EEC','EEQ', 'SE5'))</SEND>
</DATA_SEND>

<DATA_RECEIVE>
   <SRV_STATUS>.T.</SRV_STATUS>
</DATA_RECEIVE>
</SERVICE>
</EASYLINK>
