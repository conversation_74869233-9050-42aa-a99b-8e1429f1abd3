#include "rwmake.ch"

User Function FIN460E1

Local aArea		 := GetArea()

If SE1->E1_PREFIXO + SE1->E1_TIPO <> "LIQNCC"
	RestArea(aArea)
   Return
Endif

aDadosAge:= {}
AAdd( aDadosAge, { "<PERSON><PERSON>", "Nome do Agente" } )
AAdd( aDadosAge, { SE1->E1_AGE+'-'+SE1->E1_AGELOJA, SE1->E1_NOMAGE } )

aDadosNCC:= {}
AAdd( aDadosNCC, { "Prefixo","Numero","<PERSON><PERSON><PERSON>","Tipo","Emissao","Valor da NCC" } )
AAdd( aDadosNCC, { SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, SE1->E1_TIPO, Dtoc(SE1->E1_EMISSAO), Transform(SE1->E1_VALOR,"@E 999,999.99")  } )

aDadosCli:= {}
AAdd( aDados<PERSON>li, { "Cliente-Lj","Nome do Cliente" } )
AAdd( aDadosCli, { SE1->E1_CLIENTE+'-'+SE1->E1_LOJA, SE1->E1_NOMCLI } )

If ! Empty(SE1->E1_AGE)
	aDados  := { aDadosNCC, aDadosCli, aDadosAge }
Else
	aDados  := { aDadosNCC, aDadosCli }
Endif

MenviaMail("S02",aDados,"","","",.T.)

RestArea(aArea)
Return

User Function MMENSS02()
Local cMsg
Local aNewDados := ParamIxb

cMsg := '<html><title></title><body>'
cMsg += '<table borderColor="#0099cc" height="29" cellSpacing="1" width="645" borderColorLight="#0099cc" border=1>'
cMsg += '  <tr><td borderColor="#0099cc" borderColorLight="#0099cc" align="left" width="606"'
cMsg += '    borderColorDark="#0099cc" bgColor="#0099cc" height="1">'
cMsg += '    <p align="center"><FONT face="Courier New" color="#ffffff" size="4">'
cMsg += '    <b>Exclusao da Nota de Credito Cliente (NCC)</b></font></p></td></tr>'
cMsg += '  <tr><td align="left" width="606" height="32"><p align="left"><b>    '

cMsg += MontaTabelaHTML(aDados[1], .T., "105%")
cMsg += MontaTabelaHTML(aDados[2], .T., "105%")
If Len(aDados) >= 3
	cMsg += MontaTabelaHTML(aDados[3], .T., "105%")
Endif
cMsg += '</b></p></td></tr>'
cMsg += '</table><br>'

cMsg += U_ROD_EMAIL()

Return(cMsg)