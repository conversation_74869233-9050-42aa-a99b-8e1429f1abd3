#Include 'Protheus.ch'

User Function FINA910F()

Local aRetorno 
Local aAreaSEE := SEE->(Getarea())
Local aArea    := Getarea()  
Local cCC      := PaDL(AllTrim(PARAMIXB[3]), TamSx3("EE_XCTABCO")[1], "0")  


	dbSelectArea("SEE")
	SEE->( dbOrder<PERSON>ickName("CCSITEF")) 
	If DbSeek(xFilial("SEE")+cCC)	
 
		aRetorno := {SEE->EE_CODIGO,SEE->EE_AGENCIA,SEE->EE_CONTA}				

    EndIf                        
    
    RestArea(aArea)
    RestArea(aAreaSEE)

Return aRetorno