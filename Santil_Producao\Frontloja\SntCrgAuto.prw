#Include "Protheus.ch"

User Function SntCrgAuto(_cEmp,_cFil)

RPCSetType(3)
RPCSetEnv(_cEmp, _cFil, ,,,,{})
Conout('Carga Automatica Santil ---> Iniciado ambiente na empresa ' +_cEmp + ' Filial ' + _cFil)

cEmpCrg := GetMv('MV_LJILLCO') //Empresa
cFilCrg := GetMv('MV_LJILLBR') //Filial
cEnvCrg := GetMv('MV_LJILLEN') //Ambiente
cIpCrg  := GetMv('MV_LJILLIP') //IP
cPrtCrg := GetMv('MV_LJILLPO') //Porta

Conout('********** Inicio do Processo de Download de Cargas ***************')
Conout('Empresa:  ' + cEmpCrg)
Conout('Filial :  ' + cFilCrg)
Conout('Ambiente: ' + cEnvCrg)
Conout('IP:       ' + cIpCrg)
Conout('Porta     ' + cPrtCrg)
Conout('*******************************************************************')

LOJA1157Job ( cIpCrg, cPrtCrg, cEnvCrg, cEmpCrg, cFilCrg, .T., .T., .F., .T., .T. ) 
Conout('Finalizado Processo de Carga no Ambiente')

Return()