<EASYLINK>
<SERVICE>
<ID>012</ID>	
<DATA_SELECTION>
	<FIN_NUM>EECGetFinN("SE2")</FIN_NUM>	
	<FIN_SEND>
		<FIN_IT>
			<FIN_ELE1>'E2_NUM'</FIN_ELE1>
			<E2_NUM>#TAG FIN_NUM#</E2_NUM>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>		
		<FIN_IT>
			<FIN_ELE1>'E2_PREFIXO'</FIN_ELE1>
			<E2_PREFIXO>'EEC'</E2_PREFIXO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_PARCELA'</FIN_ELE1>
			<E2_PARCELA>' '</E2_PARCELA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_TIPO'</FIN_ELE1>
			<E2_TIPO>cTipoTit</E2_TIPO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_NATUREZ'</FIN_ELE1>
			<E2_NATUREZ>(cAliasInt)->EET_NATURE</E2_NATUREZ>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_FORNECE'</FIN_ELE1>
			<E2_FORNECE>cFinForn</E2_FORNECE>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_LOJA'</FIN_ELE1>
			<E2_LOJA>cFinLoja</E2_LOJA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_EMISSAO'</FIN_ELE1>
			<E2_EMISSAO>(cAliasInt)->EET_DESADI</E2_EMISSAO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VENCTO'</FIN_ELE1>
			<E2_VENCTO>(cAliasInt)->EET_DTVENC</E2_VENCTO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VENCREA'</FIN_ELE1>
			<E2_VENCREA>DataValida((cAliasInt)->EET_DTVENC, .T.)</E2_VENCREA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VENCORI'</FIN_ELE1>
			<E2_VENCORI>(cAliasInt)->EET_DTVENC</E2_VENCORI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VALOR'</FIN_ELE1>
			<E2_VALOR>(cAliasInt)->EET_VALORR</E2_VALOR>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>		
		<FIN_IT>
			<FIN_ELE1>'E2_MOEDA'</FIN_ELE1>
			<E2_MOEDA>1</E2_MOEDA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VLCRUZ'</FIN_ELE1>
			<E2_VLCRUZ>(cAliasInt)->EET_VALORR</E2_VLCRUZ>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<IF_01 COND = "cTipoTit == 'PA'">
		<FIN_IT>
			<FIN_ELE1>'AUTBANCO'</FIN_ELE1>
			<E1_PORTADO>cBancoSE5</E1_PORTADO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'AUTAGENCIA'</FIN_ELE1>
			<E1_AGEDEP>cAgenciaSE5</E1_AGEDEP>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'AUTCONTA'</FIN_ELE1>
			<E1_CONTA>cContaSE5</E1_CONTA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		</IF_01>
		<FIN_IT>
			<FIN_ELE1>'E2_HIST'</FIN_ELE1>
			<E2_HIST>'Emb.:' + AllTrim(EEC->EEC_PREEMB) </E2_HIST>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
          		<FIN_ELE1>'E2_ORIGEM'</FIN_ELE1>
          		<E2_ORIGEM>''</E2_ORIGEM>
          		<FIN_ELE3>''</FIN_ELE3>
      		</FIN_IT>
</FIN_SEND>
</DATA_SELECTION>
<DATA_SEND>
	<SEND>EECINFIN(#TAG FIN_SEND#, 'SE2', 'INCLUIR',,,#TAG FIN_NUM#,'EET')</SEND>
</DATA_SEND>
<DATA_RECEIVE>
   <SRV_STATUS>.T.</SRV_STATUS>
</DATA_RECEIVE>
</SERVICE>
</EASYLINK>
