#Include "Protheus.Ch"
#Include "Fileio.ch"
#Include "TopConn.CH"
#Include "TbiConn.CH"

/*
Programa   : ProcMail ()
Objetivo   : Envio de email apos a confirmacao da  alteracao e ao clicar em acoes relacionadas em 'Reenvio de e-mail' 
Retorno    : 
Autor      : <PERSON><PERSON><PERSON>/Hora  : 13/08/2014 - 10:00
*/
User Function Mail_Call()
Local aOrd	   	   	:= SaveOrd({"SUC","SU5","SUQ"})
Local cHtml			:= ""
Local cHtmlPack		:= ""
Local cHtmlProd		:= ""
Local cAssunto		:= "Abertura de FNC - Call Center"
Local cEmailBcc		:= ""
Local lEnvia		:= .F.
Local cStringMail  	:= ""
Local oProcess
Local oHtml
Private lRet		:= .F.
Begin Transaction//Restaura todos os dados caso seja interrompido por um erro

If !Empty(cCopia1) .and. !Empty(cCopia2)
	cStringMail	+= cCopia1+ ";"+cCopia2 
Else
	If !Empty(cCopia1)
   		cStringMail	+= cCopia1 	
	ElseIf !Empty(cCopia2)
		cStringMail	+= cCopia2
	End If
End If


cHtml+= '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">'
cHtml+= '<html xmlns="http://www.w3.org/1999/xhtml">'
cHtml+= '<head>'
cHtml+= '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />'
cHtml+= '<title>Casa Cenario - !TIPO!</title>'
cHtml+= '</head>'
cHtml+= '<body>'
cHtml+= '<table width="100%" border="0" cellspacing="0" cellpadding="0" bordercolor="#333333">'
cHtml+= '  <td align="center" colspan="2"> '
cHtml+= '    <font color="#666666"face="Arial, Helvetica, sans-serif" size="+3"><b>Abertura de FNC</b></font>  ' 
cHtml+= '  </td>'
cHtml+= '  <tr>'
cHtml+= '    <td><font color="#666666"face="Arial, Helvetica, sans-serif" size="5">Informamos que o atendimento sob o protocolo <b>ATENDIMENTO - ITEM/ASSUNTO: '+SUC->UC_CODIGO+'</b>, sera enviado para analise: </font></td>'
cHtml+= '  </tr>'
cHtml+= '</table>'

cHtml+= '<table width="100%" border="1" cellpadding="0" cellspacing="0" bordercolor="#333333">'
cHtml+= '<tr bgcolor="#CCCCCC">'
cHtml+= '<td width="30%" align="center"><b><font color="#666666" face="Arial, Helvetica, sans-serif" size="2">Nome Completo</font></b></td>'
cHtml+= '<td width="15%" align="center"><b><font color="#666666" face="Arial, Helvetica, sans-serif" size="2">Modelo do Produto</font></b></td>'
cHtml+= '<td width="15%" align="center"><b><font color="#666666" face="Arial, Helvetica, sans-serif" size="2">Tamanho do Produto</font></b></td>'
cHtml+= '<td width="30%" align="center"><b><font color="#666666" face="Arial, Helvetica, sans-serif" size="2">Ocorrencia</font></b></td>'
cHtml+= '</tr>'
cHtml+= '<tr>'
cHtml+= '<td width="30%"  align="center">'+SUC->UC_CODCONT+' - '+Posicione("SU5",1,xFilial("SU5")+Avkey(SUC->UC_CODCONT,"U5_CODCONT"),"U5_CONTAT")+'</td>'
cHtml+= '<td width="15%" align="center">'+SUC->UC_XMODELO+'</td>'
cHtml+= '<td width="15%" align="center">'+SUC->UC_XNUMERO+'</td>'
cHtml+= '<td width="30%" align="center">'+Posicione("SUQ",1,xFilial("SUQ")+Avkey(cCodOcorrencia,"UQ_SOLUCAO"),"UQ_DESC")+'</td>'
cHtml+= '</tr>'
cHtml+= '</table>'
cHtml+= '</body>'
cHtml+= '</html>'

Processa({|lEnd| lRet:= U_FUN_EMAIL("",'Abertura de FNC',cHtml,cEmailTo,cStringMail,'')})

If lRet// .T. enviado com sucesso se .F. falha no envio
	RecLock("SUC",.F.)
	SUC->UC_XEMAIL := "2"
	SUC->(MsUnLock())
Else
	RecLock("SUC",.F.)
	SUC->UC_XEMAIL := "1"
	SUC->(MsUnLock())
End If 
 
End Transaction
RestOrd(aOrd)

Return(.T.)