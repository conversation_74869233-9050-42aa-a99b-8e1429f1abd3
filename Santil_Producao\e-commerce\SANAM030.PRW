#include "protheus.ch"
#include "topconn.ch"
#include "totvs.ch"
#include "xmlxfun.ch"
#include "tbiconn.ch"

/*
----------------------------------------------------------------------------------------
Program	SANAM030
Owner	<PERSON>
Date	28/05/14
Desc	Rotine that export products of E-commerce JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
User Function SANAM030()

	Private cUserWs		:= ""
	Private cSenhaWs	:= ""
	Private aLogFull	:= {}
	Private isBlind		:= isBlind()
	Private	cStock		:= ""

	//Default aEmp		:= {}

	If !isBlind
		If nAmbiente = 1 //Produ��o
			cUserWs  := GetMV("ES_USERJET")
			cSenhaWs := GetMV("ES_PSWDJET")
		ElseIf nAmbiente = 2 //Homologa��o
			cUserWs  := GetMV("ES_HUSERJE")
			cSenhaWs := GetMV("ES_HPSWDJE")
		EndIf
	Else

		/*
		If !Empty(aEmp)
		PREPARE ENVIRONMENT EMPRESA aEmp[1] FILIAL aEmp[2]
		EndIf
		*/

		cUserWs  := GetMV("ES_USERJET")
		cSenhaWs := GetMV("ES_PSWDJET")
	EndIf

	if !isBlind

		Processa( {|| BuscaProduto(cUserWs, cSenhaWs) },"Aguarde , Integrando Produtos com o E-commerce" )
		conout("Aguarde , Integrando Produtos com o E-commerce")
		BuscaProduto(cUserWs, cSenhaWs)

	else

		BuscaProduto(cUserWs, cSenhaWs)

		/*
		If !Empty(aEmp)
		RESET ENVIRONMENT
		EndIf
		*/
	endif

Return aLogFull

/*
----------------------------------------------------------------------------------------
Program	BuscaProduto
Owner	Felipe Santos
Date	28/05/14
Desc.	Rotine that process returned array os costumers JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
Static Function BuscaProduto(cUserWs, cSenhaWs)
	Local aProduct		:= {}
	Local _cQry 		:= ""
	Local lDel 			:= .F.
	Local nCountRegs	:= 0
	Local lAlt			:= .F.

	private _cAlias		:= GetNextAlias()
	private nPrecoLog	:= 0
	private oWSJet		:= WSWSJET():New()
	private nContador 	:= 0

	conout(" | | | BuscaProduto | | |")

	processMessages()

	//Verifica se existe saldo SB2 alterado
	BuscaAltSB2()

	//Verifica se pre�o foi alterado
	FindPrecoDA1()

	//Search all products that has alteration
	// even deleted registers too
	_cQry := " SELECT B1_FILIAL, B1_COD, B1_XID, B1_XTIPOPC, B1_DESC,B1_PRV1,B1_PESO, B1_XMULTI, B1_XNMSIT, B1_XCATG, B1_XATIVO, B1_XFRET, B1_XREFFOR, B1_XSISNOV, B1_XFORNEC, B1_XMARC, B1_XALTU, B1_XLARG, B1_XPROFU, B1_XDIAM, B1_XEMB, D_E_L_E_T_ as DEL"
	_cQry += " FROM " + RetSqlName("SB1") + " SB1"
	_cQry += " WHERE"
	_cQry += " 		B1_XDATUPL	<>	''"
	_cQry += " AND	B1_XTIPOPC	=	'1'"
	_cQry += " AND	B1_XMULTI	>=	1" // Apenas ir� integrar produtos com multiplo >= 1 - NAO INTEGRAR COM MULTIPLO ZERADO
	_cQry += " ORDER BY B1_COD"

	TcQuery _cQry New Alias (_cAlias)

	nCountRegs := 0
	Count to nCountRegs

	(_cAlias)->(DBGoTop())

	if !isBlind
		ProcRegua(nCountRegs)
		conout(" | | | Quantidade de Registros " + cValToChar(nCountRegs) + " | BuscaProduto | | | ")
	endif

	While !(_cAlias)->( Eof() )

		nContador 	:= 0
		aProduct	:= {}
		While !(_cAlias)->( Eof() ) .and. nContador <= 50

			processMessages()

			lImpOk 	:= .T.
			lAlt	:= .T.

			If Empty((_cAlias)->B1_PESO)
				U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Peso" , (_cAlias)->B1_COD, , "Campo em branco: Peso", , ,)
				lImpOk := .F.
			EndIf

			If Empty((_cAlias)->B1_XFRET)
				U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Frete" , (_cAlias)->B1_COD, , "Campo em branco: Frete", , ,)
				lImpOk := .F.
			EndIf

			If Empty((_cAlias)->B1_XCATG)
				U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Categoria" , (_cAlias)->B1_COD, , "Campo em branco: Categoria", , ,)
				lImpOk := .F.
			EndIf

			If Empty((_cAlias)->B1_XFORNEC)
				U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Fronecedor JET" , (_cAlias)->B1_COD, , "Campo em branco: Fronecedor JET", , ,)
				lImpOk := .F.
			EndIf

			If Empty((_cAlias)->B1_XMARC)
				U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Marca JET" , (_cAlias)->B1_COD, , "Campo em branco: Marca JET", , ,)
				lImpOk := .F.
			EndIf

			If Empty((_cAlias)->B1_XEMB)
				U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Embalagem" , (_cAlias)->B1_COD, , "Campo em branco: Embalagem", , ,)
				lImpOk := .F.
			EndIf

			If Empty((_cAlias)->B1_XNMSIT)
				//U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Nome Site" , (_cAlias)->B1_COD, , "Campo em branco: Nome Site", , ,)
				//lImpOk := .F.
				lAlt	:= .F.
			EndIf

			If (_cAlias)->B1_XEMB == "2"

				If Empty((_cAlias)->B1_XDIAM)
					//U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Diametro" , (_cAlias)->B1_COD, , "Campo em branco: Diametro", , ,)
					//lImpOk := .F.
					lAlt	:= .F.
				EndIf

				If Empty((_cAlias)->B1_XPROFU)
					//U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Comprimento" , (_cAlias)->B1_COD, , "Campo em branco: Comprimento", , ,)
					//lImpOk := .F.
					lAlt	:= .F.
				EndIf

			ElseIf (_cAlias)->B1_XEMB == "1"

				If Empty((_cAlias)->B1_XALTU)
					//U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: ALTURA" , (_cAlias)->B1_COD, , "Campo em branco: ALTURA", , ,)
					//lImpOk := .F.
					lAlt	:= .F.
				EndIf

				If Empty((_cAlias)->B1_XLARG)
					//U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Largura" , (_cAlias)->B1_COD, , "Campo em branco: Largura", , ,)
					//lImpOk := .F.
					lAlt	:= .F.
				EndIf

				If Empty((_cAlias)->B1_XPROFU)
					//U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Comprimento" , (_cAlias)->B1_COD, , "Campo em branco: Comprimento", , ,)
					//lImpOk := .F.
					lAlt	:= .F.
				EndIf
			EndIf

			If lImpOk
				if !isBlind
					incProc("Produto: "+(_cAlias)->B1_DESC)
				endif

				conout("Produto: "+(_cAlias)->B1_DESC)

				lDel := .F.
				nOption := 1
				If (_cAlias)->DEL = '*'
					lDel := .T.
					nOption:= 2
				Else
					If !EMPTY((_cAlias)->B1_XID)

						//Verifica se j� existe o produto
						If ExistProduct("IDPRODUCT",(_cAlias)->B1_XID)
							nOption:= 2 //Altera��o
							lAlt	:= .T.
						Else
							nOption := 1 //Inclus�o
						Endif
					Else
						nOption := 1
					EndIf
				EndIf

				If lAlt
					//De acordo com a op��o monta o array para enviar ao WS
					aProduct := RetornaArrProd(nOption, lDel, (_cAlias)->B1_XID, (_cAlias)->B1_COD)

					processProduct( aProduct, nOption, (_cAlias)->B1_COD, (_cAlias)->B1_XNMSIT)
				Else

					If Empty((_cAlias)->B1_XNMSIT)
						U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Nome Site" , (_cAlias)->B1_COD, , "Campo em branco: Nome Site", , ,)
						//lImpOk := .F.
						lAlt	:= .F.
					EndIf

					If (_cAlias)->B1_XEMB == "2"

						If Empty((_cAlias)->B1_XDIAM)
							U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Diametro" , (_cAlias)->B1_COD, , "Campo em branco: Diametro", , ,)
							//lImpOk := .F.
							lAlt	:= .F.
						EndIf

						If Empty((_cAlias)->B1_XPROFU)
							U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Comprimento" , (_cAlias)->B1_COD, , "Campo em branco: Comprimento", , ,)
							//lImpOk := .F.
							lAlt	:= .F.
						EndIf

					ElseIf (_cAlias)->B1_XEMB == "1"

						If Empty((_cAlias)->B1_XALTU)
							U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: ALTURA" , (_cAlias)->B1_COD, , "Campo em branco: ALTURA", , ,)
							//lImpOk := .F.
							lAlt	:= .F.
						EndIf

						If Empty((_cAlias)->B1_XLARG)
							U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Largura" , (_cAlias)->B1_COD, , "Campo em branco: Largura", , ,)
							//lImpOk := .F.
							lAlt	:= .F.
						EndIf

						If Empty((_cAlias)->B1_XPROFU)
							U_SANAM091("1","Produto","Produto " + (_cAlias)->B1_COD + " nao incluso. Campo em branco: Comprimento" , (_cAlias)->B1_COD, , "Campo em branco: Comprimento", , ,)
							//lImpOk := .F.
							lAlt	:= .F.
						EndIf
					EndIf

				EndIf

			EndIf

			(_cAlias)->( dbSkip() )
			nContador := nContador + 1
			aProduct  := {}
			commit
		Enddo

	EndDo

	(_cAlias)->( dbCloseArea() )

	conout(" | | | Finalizando - BuscaProduto | | |")
return

/*
----------------------------------------------------------------------------------------
Program	ProcessProduct
Owner	Felipe Santos
Date	28/05/14
Desc.	Rotine that process returned array os costumers JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
static function processProduct(aProducts,nStatus, cCodProd, cNmSite)
	local cIDJet		:= ""
	local nQtyStock	:= 0
	local nPreco		:= 0
	local cUpdSb1		:= ""
	local aArea		:= getArea()
	local aAreaPA2	:= PA2->(getArea())

	local cXMLCubing	:= ""
	local cXMLBrand	:= ""
	local aProdExp	:= {}
	local lImportOk	:= .F.
	Local cMessage	:= ""

	PA2->(DBGoTop())

	conout(" | | | ProcessProduct | | |")

	processMessages()

	cXML		:= GeraXML(cUserWs,cSenhaWs, aProducts, nStatus)
	lImportOk	:= .F.

	if nStatus == 1
		if oWSJet:importProduct(cXML)

			If ValType(oWSJet:OWSIMPORTPRODUCTRESULT:CMESSAGE) == "C"
				cMessage	:= oWSJet:OWSIMPORTPRODUCTRESULT:CMESSAGE
			EndIf

			if oWSJet:exportProduct(cUserWs, cSenhaWs, "NAME", allTrim((_cAlias)->B1_XNMSIT), "E")
				aProdExp	:= {}
				aProdExp	:= oWSJet:oWSexportProductResult:oWSproduct

				if len(aProdExp) > 0
					cIDJet	:= aProduct030:_EXPORTPRODUCTRESPONSE:_EXPORTPRODUCTRESULT:_PRODUCT:_IDPRODUCT:TEXT
				endif
			endif

			if !empty(cIDJet)
				nQtyStock	:= 0
				nQtyStock	:= round(BuscaEstoque((_cAlias)->B1_COD, (_cAlias)->B1_XMULTI), 0)

				U_SANAM091("2","Produto","Inclus�o do produto no e-commerce Cod. Prod. WEB: " + cCodProd, cCodProd, , , , , nQtyStock )
				conout("Inclus�o do produto no e-commerce Cod. Prod. WEB: " + cCodProd)

				//aadd(aLogFull,{"Inclus�o", (_cAlias)->B1_COD, (_cAlias)->B1_XID, (_cAlias)->B1_DESC, (_cAlias)->B1_XNMSIT, Posicione("PA2",1,xFilial("SB1")+(_cAlias)->B1_XCATG,"PA2_XNOMEC"), (_cAlias)->B1_XATIVO})

				lImportOk := .T.
			else
				If Empty(cMessage)
					If ValType(oWSJet:OWSIMPORTPRODUCTRESULT:CMESSAGE) == "C"
						cMessage := oWSJet:OWSIMPORTPRODUCTRESULT:CMESSAGE
					Else
						conout("Produto " + cCodProd + " n�o incluso. - oWSJet:OWSIMPORTPRODUCTRESULT:CMESSAGE retornou NIL")
					EndIf
				EndIf

				U_SANAM091("1","Produto","Produto " + cCodProd + " n�o incluso. " + cMessage, cCodProd, , cMessage, , ,)

				cMessage	:= ""

			endif
		else
			if !empty(GetWSCError(3))
				U_SANAM091("1","Produto","Produto " + cCodProd + " n�o incluso. Erro WS JET " + GetWSCError(3), cCodProd, , GetWSCError(3), , ,)
				conout("Produto " + cCodProd + " n�o incluso. Erro WS JET " + GetWSCError(3))
			else
				U_SANAM091("1","Produto","Produto " + cCodProd + " n�o incluso. Erro WS JET " + GetWSCError(), cCodProd, , GetWSCError(), , ,)
				conout("Produto " + cCodProd + " n�o incluso. Erro WS JET " + GetWSCError())
			endif
		endif
	elseif nStatus == 2
		if oWSJet:importProduct(cXML)
			//aadd(aLogFull, {"Altera��o", (_cAlias)->B1_COD, (_cAlias)->B1_XID, (_cAlias)->B1_DESC, (_cAlias)->B1_XNMSIT, Posicione("PA2",1,xFilial("SB1")+(_cAlias)->B1_XCATG,"PA2_XNOMEC"), (_cAlias)->B1_XATIVO})

			//--------------------------------------------------
			// Andre Lanzieri 18/03/2016
			// Envia estoque do produto para ecommerce.
			//--------------------------------------------------
			If !Empty(cStock)
				oWSJet:stockUpdateSingle(cUserWs, cSenhaWs, (_cAlias)->B1_XID, cStock)
			EndIf

			U_SANAM091("2","Produto","Altera��o do produto no e-commerce Cod. Prod. WEB: " + cCodProd, cCodProd, , , , , nQtyStock )

			lImportOk	:= .T.
			cIDJet		:= (_cAlias)->B1_XID
		else
			if !empty(GetWSCError(3))
				U_SANAM091("1","Produto","Altera��o | Produto " + cCodProd + " n�o alterado. Erro WS JET " + GetWSCError(3), cCodProd, , GetWSCError(3), , ,)
				conout("Altera��o | Produto " + cCodProd + " n�o incluso. Erro WS JET " + GetWSCError(3))
			else
				U_SANAM091("1","Produto","Altera��o | Produto " + cCodProd + " n�o alterado. Erro WS JET " + GetWSCError(), cCodProd, , GetWSCError(), , ,)
				conout("Altera��o | Produto " + cCodProd + " n�o incluso. Erro WS JET " + GetWSCError())
			endif
		endif
	endif

	If lImportOk
		processMessages()

		cUpdSb1 := ""
		cUpdSb1 := "UPDATE " + retSQLName("SB1")

		if nStatus == 1
			cUpdSb1 += " SET B1_XDATUPL = '', B1_XID = '" + cIDJet + "'"
		elseif nStatus == 2
			cUpdSb1 += " SET B1_XDATUPL = ''"
		endif

		cUpdSb1 += " WHERE"
		cUpdSb1 += " 	B1_COD = '" + allTrim((_cAlias)->B1_COD) + "'"

		if tcSQLExec(cUpdSb1) < 0
			U_SANAM091("1", "Produto", "SQL ERROR - Erro ao atualizar campo B1_XDATUPL", (_cAlias)->B1_COD )
			conout("SQL ERROR - Erro ao atualizar campo B1_XDATUPL" + (_cAlias)->B1_COD)
		endif

		If nStatus == 1

			cXMLCubing := ""
			cXMLCubing := GXMLCubing(cUserWs,cSenhaWs, nStatus, cIDJet)

			if oWSJet:importCubingProduct(cXMLCubing)
				U_SANAM091("2","Produto","Embalagem/Dimens�es atualizadas do produto no e-commerce Cod. Prod. WEB: " + cCodProd, cCodProd, , , , , nQtyStock )
			else
				if !empty(GetWSCError(3))
					U_SANAM091("1","Produto","Embalagem/Dimens�es - Erro WS JET " + GetWSCError(3), cCodProd, , GetWSCError(3), , ,)
					conout("Embalagem/Dimensões - Erro WS JET " + GetWSCError(3))
				else
					U_SANAM091("1","Produto","Embalagem/Dimens�es - Erro WS JET " + GetWSCError(), cCodProd, , GetWSCError(), , ,)
					conout("Embalagem/Dimens�es- Erro WS JET " + GetWSCError())
				endif
			endif

			cXMLBrand := ""
			cXMLBrand := GXMLBrand(cIDJet)

			if oWSJet:AttachProductBrand(cXMLBrand)
				U_SANAM091("2","Produto","Marca atualizada do produto no e-commerce Cod. Prod. WEB: " + cCodProd, cCodProd, , , , , nQtyStock )
				conout("Marca atualizada do produto no e-commerce Cod. Prod. WEB: " + cCodProd)
			else
				if !empty(GetWSCError(3))
					U_SANAM091("1","Produto","Marca - Erro WS JET " + GetWSCError(3), cCodProd, , GetWSCError(3), , ,)
					conout("Marca - Erro WS JET " + GetWSCError(3))
				else
					U_SANAM091("1","Produto","Erro WS JET " + GetWSCError(), cCodProd, , GetWSCError(), , ,)
					conout("Marca - Erro WS JET " + GetWSCError())
				endif
			endif
			
		EndIf

	endif

	conout(" | | | Finalizando - ProcessProduct | | |")

	restArea(aAreaPA2)
	restArea(aArea)
return

/*
----------------------------------------------------------------------------------------
Program	ExistProduct
Owner	Felipe Santos
Date	28/05/14
Desc.	Rotine that process returned array os costumers JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
Static Function ExistProduct(cCampo, cIdProduct)
	Local lRet := .F.
	Local aProducts := {}

	conout(" | | | ExistProduct | | |")

	If oWSJet:exportProduct(cUserWs,cSenhaWs,cCampo,Alltrim(cIdProduct),"E")
		aProducts:= oWSJet:oWSexportProductResult:oWSproduct
		If Len(aProducts) > 0
			if LEN(aProducts[1]:CNAME) > 0
				lRet := .T.
			EndIF
		EndIf
	Endif

	conout(" | | | Finalizando - ExistProduct | | |")
Return lRet

/*                                                               '
----------------------------------------------------------------------------------------
Program	RetornaArrProd
Owner	Felipe Santos
Date	28/05/14
Desc.	Rotine that process returned array os costumers JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
Static Function RetornaArrProd(nStatus, lDel, cIdJet, cIdProd )
	local aWsProduct	:= {}
	local aRet			:= {}
	local aProdSel	:= {}

	conout(" | | | RetornaArrProd | | |")

	//Busca a quantidade de estoque do produto
	nEstoque := BuscaEstoque(cIdProd, (_cAlias)->B1_XMULTI)
	nEstoque := ROUND(nEstoque,0)
	nPreco   := BuscaPreco(cIdProd, (_cAlias)->B1_XMULTI, (_cAlias)->B1_PRV1)

	aRet:={{"NIDPRODUCT"		, "0"												},; //DATA DE INICIO DE PROMO��O
	{"CIDEXTERNALPRODUCT"	, ""												},; //DATA DE INICIO DE PROMO��O
	{"CCODE"					, (_cAlias)->B1_XREFFOR							},; //DATA DE INICIO DE PROMO��O
	{"CNAME"					, ALLTRIM((_cAlias)->B1_XNMSIT)					},; //NOME DO PRODUTO ALLTRIM(SB1->B1_XNMSIT)
	{"CDETAILSUMMARY"			, ALLTRIM((_cAlias)->B1_XNMSIT)					},; //INFORMA��ES B�SICAS ALLTRIM(SB1->B1_XNMSIT)
	{"CDETAIL"					, ALLTRIM((_cAlias)->B1_XNMSIT)					},; //CARACTERISTICAS DO PRODUTO ALLTRIM(SB1->B1_XNMSIT)
	{"CPROMOTIONSTORE"		, ""												},; //VALOR PADR�O = N
	{"CPROMOTIONPORTAL"		, ""												},; //VALOR PADR�O = N
	{"CFLAGEXHAUSTED"			, "N"												},; //VALOR PADR�O = N
	{"CFLAGSHIPPING"			, iif((_cAlias)->B1_XFRET == "1", "S", "N")	},; //VALOR PADR�O = S
	{"CSTATUSSUBTYPE"			, "D"												},; //VALOR PADR�O = N
	{"CSTATUS"					, iif((_cAlias)->B1_XATIVO == "1", "A", "I")	},; //VALOR PADR�O = D/A
	{"NIDCATEGORY"			, (_cAlias)->B1_XCATG							},; //ID DA CATEGORIA
	{"CIDEXTERNALCATEGORY"	, ""												},; //ID DA CATEGORIA EXTERNA
	{"NIDPRODUCTSTATUS"		, "2492"											},; //ID DO STATUS DO PRODUTO
	{"NIDCURRENCY"			, "1927"											},; //ID MOEDA
	{"NIDSUPPLIER"			, (_cAlias)->B1_XFORNEC							},; //ID SUPPLIER
	{"NIDEXTERNALSUPPLIER"	, 0													},; //ID SUPPLIER EXTERNO
	{"NSTOCK"					, nEstoque											},; //QTD EM ESTOCK
	{"CBEGINPROMOTIONSTORE"	, ""												},; //DATA DE INICIO DE PROMO��O
	{"CENDPROMOTIONSTORE"	, ""												},; //DATA DE FINAL DE PROMO��O
	{"NPRICE"					, nPreco											},; //PRE�O DO PRODUTO
	{"NPRICEPROMOTION"		, 0													},; //PRE�O EM PROMO��O
	{"NWEIGHT"					, (_cAlias)->B1_PESO								},;  //PESO DO PRODUTO
	{"CIMAGEHOME"				, ""												},; //IMAGEM PRINCIPAL DO PRODUTO
	{"CIMAGEDETAIL"			, ""												},; //IMAGEM DETALHADA DO PRODUTO
	{"NIDSUBTYPE"				, 0													},; //IMAGEM DETALHADA DO PRODUTO
	{"NIDTYPE"					, 0													},; //IMAGEM DETALHADA DO PRODUTO
	{"CINSERTDATE"			, "2015-01-01T00:00:00.000-00:00"				},; //DATA DE INCLUS�O
	{"CLASTUPDATE"			, "2014-06-09T00:00:00.000-00:00"				}} //DATA DE ALTERA��O

	nPrecoLog := nPreco

	aadd(aWsProduct,aRet)

	conout(" | | | Finalizando - RetornaArrProd | | |")
Return aWsProduct

//------------------------------------------------------------------------------------
//------------------------------------------------------------------------------------
static function GeraXML(cUserWs, cSenhaWs, aProducts, nStatus)
	local cScript		:= ""
	local n			:= 0
	local nQtdSaldo	:= 0
	local nPreco		:= 0
	local aRetWs		:= {}
	local aProdExp	:= {}
	local cName		:= ""
	
	//-- Alterado 14/02/17 Gera XML na pasta EDI -> Leonardo Espinosa 
	Local cDir		:= "\edi\cfg\SANAM030\"
	Local cFile		:=	cDir+"SANAM030-"+dToS(DATE())+"-"+LEFT(Time(),2)+SubStr(Time(),4,2)+".XML"
	Local nHndLog	:= 0
	
	FWMakeDir(cDir)
	
	//-- Cria o arquivo HTML em branco
	nHndLog := FCREATE(cFile)
	If nHndLog == -1
		Conout("[SANAM030]Erro ao criar o arquivo "+cFile+" erro:"+STR(FERROR()))
	EndIf

	conout(" | | | GeraXML | | |")

	cStock		:= ""

	nQtdSaldo := 0
	nQtdSaldo := round(buscaEstoque((_cAlias)->B1_COD, (_cAlias)->B1_XMULTI), 0)

	nPreco := 0
	nPreco := BuscaPreco((_cAlias)->B1_COD, (_cAlias)->B1_XMULTI, (_cAlias)->B1_PRV1)

	if len(aProducts) > 0
		if nStatus == 2
			if oWSJet:exportProduct(cUserWs, cSenhaWs, "IDPRODUCT", (_cAlias)->B1_XID, "E")
				aProdExp := {}
				aProdExp := oWSJet:oWSexportProductResult:oWSproduct
				if len(aProdExp) > 0
					if !empty(aProdExp[1]:cName)
						cName := ""
						cName := aProdExp[1]:cName

						cScript += "<importProduct xmlns='WSJET'>"+CRLF
						cScript += "<userName>"+cUserWs+"</userName>"+CRLF
						cScript += "<password>"+cSenhaWs+"</password>"+CRLF
						cScript += "<product>"+CRLF
						cScript += "<Product>"+CRLF
						cScript += "<idProduct>"				+ allTrim((_cAlias)->B1_XID)		+ "</idProduct>"+CRLF
						cScript += "<idExternalProduct>"	+ allTrim((_cAlias)->B1_XSISNOV)	+ "</idExternalProduct>"+CRLF
						cScript += "<code>"					+ allTrim((_cAlias)->B1_XREFFOR)	+ "</code>"+CRLF

						//-- Ajuste para tratar acentos - Robson Oliveira
						cScript += "<name><![CDATA[" + ACTxt2Htm(allTrim(cName)) + "]]></name>"+CRLF

						cScript += "<promotionStore>N</promotionStore>"+CRLF

						if nQtdSaldo == 0
							cScript += "<flagExhausted>S</flagExhausted>"+CRLF
						elseif nQtdSaldo > 0
							cScript += "<flagExhausted>N</flagExhausted>"+CRLF
						endif

						cScript += "<flagShipping>" + iif((_cAlias)->B1_XFRET == "1", "S", "N")		+ "</flagShipping>"+CRLF
						cScript += "<statusSubType>N</statusSubType>"+CRLF
						cScript += "<status>"		+ iif((_cAlias)->B1_XATIVO == "1", "A", "I")		+ "</status>"+CRLF
						cScript += "<idCategory>"	+ allTrim((_cAlias)->B1_XCATG)						+ "</idCategory>"+CRLF
						cScript += "<idSupplier>"	+ allTrim((_cAlias)->B1_XFORNEC)					+ "</idSupplier>"+CRLF
						cScript += "<stock>"			+ cValToChar(nQtdSaldo)								+ "</stock>"+CRLF
						cScript += "<price>"			+ cValToChar(nPreco)									+ "</price>"+CRLF
						cScript += "<weight>"		+ cValToChar((_cAlias)->B1_PESO)					+ "</weight>"+CRLF
						cScript += "</Product>"+CRLF
						cScript += "</product>"+CRLF
						cScript += "<operation>2</operation>"+CRLF
						cScript += "</importProduct>"+CRLF

						cStock	:= cValToChar(nQtdSaldo)
					endif
				endif
			endif
		else
			for n := 1 to len(aProducts)
				cScript += "<importProduct xmlns='WSJET'>"+CRLF
				cScript += "<userName>"+cUserWs+"</userName>"+CRLF
				cScript += "<password>"+cSenhaWs+"</password>"+CRLF
				cScript += "<product>"+CRLF
				cScript += "<Product>"+CRLF
				cScript += "<idProduct>"+aProducts[n][1][2]+"</idProduct>"+CRLF
				cScript += "<idExternalProduct>"	+ allTrim((_cAlias)->B1_XSISNOV)	+ "</idExternalProduct>"+CRLF
				cScript += "<code>"+aProducts[n][3][2]+"</code>"+CRLF
				cScript += "<name>"+aProducts[n][4][2]+"</name>"+CRLF
				cScript += "<detailSummary>"+aProducts[n][5][2]+"</detailSummary>"+CRLF
				cScript += "<detail>"+aProducts[n][5][2]+"</detail>"+CRLF
				cScript += "<promotionStore>N</promotionStore>"+CRLF

				if nQtdSaldo == 0
					cScript += "<flagExhausted>S</flagExhausted>"+CRLF
				elseif nQtdSaldo > 0
					cScript += "<flagExhausted>N</flagExhausted>"+CRLF
				endif

				cScript += "<flagShipping>" + iif((_cAlias)->B1_XFRET == "1", "S", "N") + "</flagShipping>"+CRLF
				cScript += "<statusSubType>N</statusSubType>"+CRLF
				cScript += "<status>"		+ allTrim(aProducts[n][12][2])					+ "</status>"+CRLF
				cScript += "<idCategory>"	+ cValToChar(aProducts[n][13][2])				+ "</idCategory>"+CRLF
				cScript += "<idSupplier>"	+ cValToChar(aProducts[n][17][2])				+ "</idSupplier>"+CRLF
				cScript += "<stock>"			+ cValToChar(nQtdSaldo)							+ "</stock>"+CRLF
				cScript += "<price>"			+ cValToChar(nPreco)								+ "</price>"+CRLF
				cScript += "<weight>"		+ cValToChar((_cAlias)->B1_PESO)				+ "</weight>"+CRLF
				cScript += "</Product>"+CRLF
				cScript += "</product>"+CRLF
				cScript += "<operation>1</operation>"+CRLF
				cScript += "</importProduct>"+CRLF
			next n
		endif
	endif

	conout(" | | | Finalizando - GeraXML | | |")
	
	Conout("[SANAM030] - Gravando arquivo XML.. "+DTOC(DATE())+TIME()+" ") 
	
	//-- Grava o Arquivo
	FWrite(nHndLog, cScript)

	//-- Fecha o arquivo
	FClose(nHndLog)

return cScript

/*
----------------------------------------------------------------------------------------
Program	BuscaPreco
Owner	Felipe Santos
Date	28/05/14
Desc.	Rotine that process returned array os costumers JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
static function BuscaPreco(cProduto, nMult, nB1Prv1)
	local cTablePrice	:= allTrim(getMV("ES_TBLPRIC"))
	local nValProd	:= 0
	local cAlias		:= GetNextAlias()

	conout(" | | | BuscaPreco | | |")

	if cTablePrice <> ""
		_cQry := "SELECT DA1_PRCVEN  FROM " + RetSqlName("DA1") + " DA1"
		_cQry += " WHERE"
		_cQry += "			DA1.DA1_CODTAB	=	'" + cTablePrice	+ "'"
		_cQry += "		AND	DA1.DA1_CODPRO	=	'" + cProduto		+ "'"
		_cQry += "		AND	DA1.DA1_ATIVO		=	'1'"
		_cQry += "		AND	D_E_L_E_T_			<>	'*'"

		TcQuery _cQry New Alias (cAlias)

		while !(cAlias)->( Eof() )
			nValProd := (cAlias)->DA1_PRCVEN
			(cAlias)->( dbSkip() )
		enddo
	endif

	if nValProd == 0
		nValProd := nB1Prv1
	endif

	if nMult > 0
		nValProd := nValProd * nMult
	endif

	(cAlias)->(DBCloseArea())

	conout(" | | | Finalizando - BuscaPreco | | |")
return nValProd

/*
----------------------------------------------------------------------------------------
Program	BuscaEstoque
Owner	Felipe Santos
Date	28/05/14
Desc.	Rotine that process returned array os costumers JETCOMMERCE
Uso		SANTIL
----------------------------------------------------------------------------------------
*/
static function buscaEstoque(cProduto, nMult)
	local nSaldo		:= 0
	local aBranches	:= {}
	local cFilEst  	:= allTrim(getMV("ES_FILEST"))
	local nDivEst  	:= getMV("ES_DIVEST")
	local aArea		:= getArea()
	local aAreaSB2	:= SB2->(getArea())

	DBSelectArea("SB2")
	SB2->(DBSetOrder(1))

	//(_cAlias)->B1_COD
	//(_cAlias)->B1_XMULTI

	aBranches := gBranches()

	nSaldo := 0
	for nI := 1 to len(aBranches)
		for nJ := 1 to len(aBranches[nI, 2])
			SB2->(DBGoTop())
			if SB2->(DBSeek(aBranches[nI, 1] + (_cAlias)->B1_COD + aBranches[nI, 2, nJ]))
				processMessages()
				nSaldo += SaldoSB2(,,dDataBase,,,"SB2")
			endif
		next
	next

	nDivEst := (nDivEst * 0.01)
	nSaldo  := (nSaldo * nDivEst)

	if nMult > 0
		nSaldo := (nSaldo / nMult)
	else
		nSaldo := 0
	endif

	SB2->(DBCloseArea())

	restArea(aAreaSB2)
	restArea(aArea)
return nSaldo

/*
----------------------------------------------------------------------------------------
Retorna filiais e estoques contidos no par�metro
----------------------------------------------------------------------------------------
*/
static function gBranches()
	local cStockSite		:= allTrim(getMv("ES_FILEST")) // Formato: 0008|01,02,03;
	local aBranches		:= {}
	local nPosPipe		:= 0
	local nPosComma		:= 0

	while !empty(cStockSite)
		aAux		:= {}
		nPosPipe	:= 0
		nPosPipe	:= at("|", cStockSite)

		cBranche := subStr(cStockSite, 1, nPosPipe-1)
		aadd(aAux, cBranche)

		cStockSite := subStr(cStockSite, nPosPipe+1, len(cStockSite))

		nCommaDot := 0
		nCommaDot := at(";", cStockSite)

		cStocks := ""
		cStocks := subStr(cStockSite, 1, nCommaDot-1)

		aStocks := {}
		while !empty(cStocks)
			nPosComma := 0
			nPosComma := at(",", cStocks)

			if nPosComma == 0
				aadd(aStocks, subStr(cStocks, 1, len(cStocks)))
				cStocks := ""
			else
				aadd(aStocks, subStr(cStocks, 1, nPosComma-1))
				cStocks := subStr(cStocks, nPosComma+1, len(cStocks))
			endif
		enddo

		aadd(aAux, aStocks)

		aadd(aBranches, aAux)

		cStockSite := subStr(cStockSite, nCommaDot+1, len(cStockSite))
	enddo
return aBranches


//---------------------------------------------------
//---------------------------------------------------
static function BuscaAltSB2()
	local lRet				:= .T.
	local _cQry			:= ""
	local _cAliasQuery	:= GetNextAlias()
	local cCodProd		:= ""
	local nCountSB2		:= 0
	local nCountProc		:= 0

	conout(" | | | BuscaAltSB2 | | |")

	_cQry += " SELECT B2_MSEXP, B2_COD"
	_cQry += " FROM "			+ RetSqlName("SB2") + " SB2"
	_cQry += " INNER JOIN "	+ RetSqlName("SB1") + " SB1"
	_cQry += " ON SB2.B2_COD = SB1.B1_COD"
	_cQry += " WHERE"
	_cQry += "		B2_MSEXP			=	''"
	_cQry += "	AND	SB1.B1_XTIPOPC	=	'1'"
	_cQry += "	AND	SB1.B1_XDATUPL	=	''"
	_cQry += "	AND	SB1.B1_XMULTI		>=	1" // Apenas ir� integrar produtos com multiplo >= 1 - NAO INTEGRAR COM MULTIPLO ZERADO
	_cQry += "	AND	SB2.D_E_L_E_T_	<>	'*'"
	_cQry += "	AND	SB1.D_E_L_E_T_	<>	'*'"

	TcQuery _cQry New Alias (_cAliasQuery)

	Count to nCountSB2
	(_cAliasQuery)->(DBGoTop())

	While !(_cAliasQuery)->( Eof() )
		nCountProc++
		conout("BuscaAltSB2 | Processando registro " + str(nCountProc) + " de " + str(nCountSB2))

		cCodProd := (_cAliasQuery)->B2_COD

		cComando := "UPDATE " + retSQLName("SB1")
		cComando += " SET"
		cComando += "		B1_XDATUPL = '" + dToS(dDataBase) + "'"
		cComando += " WHERE"
		cComando += " 	B1_COD			=	'" + allTrim(cCodProd) + "'"
		cComando += "	AND	B1_XDATUPL		=	''"
		cComando += "	AND	B1_XTIPOPC		=	'1'"
		cComando += "	AND	B1_XMULTI		>=	1" // Apenas ir� integrar produtos com multiplo >= 1 - NAO INTEGRAR COM MULTIPLO ZERADO
		cComando += " AND	D_E_L_E_T_		<>	'*'"

		If (TCSQLExec(cComando) < 0)
			U_SANAM091("1","Estoque","Campo B1_XDATUPL da tabela SB1 n�o foi alterado para DataBase, Favor alterar manualmente", cCodProd)
			conout("Campo B1_XDATUPL da tabela SB1 n�o foi alterado para DataBase, Favor alterar manualmente")
		EndIf

		//ALTERA REGISTRO MSEXP PARA X
		cComando := "UPDATE " + RETSQLNAME("SB2") + " SET B2_MSEXP= 'X' WHERE B2_COD='"+Alltrim(cCodProd)+"'"
		If (TCSQLExec(cComando) < 0)
			U_SANAM091("1","Estoque","Campo MSEXP da tabela SB2 n�o foi alterado para X, Favor alterar manualmente", cCodProd)
			conout("Campo MSEXP da tabela SB2 n�o foi alterado para X, Favor alterar manualmente")
		EndIf


		(_cAliasQuery)->( dbSkip() )
	EndDo

	(_cAliasQuery)->(DBCloseArea())

	conout(" | | | Finalizado - BuscaAltSB2 | | |")
Return lRet

//---------------------------------------------------
//---------------------------------------------------
Static Function FindPrecoDA1()
	local lRet				:= .T.
	local _cQry			:= ""
	local _cAliasQuery	:= GetNextAlias()
	local cCodProd		:= ""
	local nCountRegs		:= 0
	local nCountProc		:= 0
	local cTblPrice		:= allTrim(getMv("ES_TBLPRIC"))

	conout(" | | | FindPrecoDA1 | | |")

	_cQry += " SELECT DA1_MSEXP, DA1_CODPRO, DA1_CODTAB"
	_cQry += " FROM "			+ RetSqlName("DA1") + " DA1"
	_cQry += " INNER JOIN "	+ RetSqlName("SB1") + " SB1"
	_cQry += " ON DA1.DA1_CODPRO = SB1.B1_COD"
	_cQry += " WHERE"
	_cQry += " 	DA1.DA1_MSEXP		=	''"
	_cQry += "	AND	DA1.DA1_CODTAB	=	'" + cTblPrice + "'"
	_cQry += "	AND	SB1.B1_XDATUPL	=	''"
	_cQry += "	AND	SB1.B1_XTIPOPC	=	'1'"
	_cQry += "	AND	SB1.B1_XMULTI		>=	1" // Apenas ir� integrar produtos com multiplo >= 1 - NAO INTEGRAR COM MULTIPLO ZERADO
	_cQry += "	AND	DA1.D_E_L_E_T_	<> '*' "
	_cQry += "	AND	SB1.D_E_L_E_T_	<> '*' "

	TcQuery _cQry New Alias (_cAliasQuery)

	Count to nCountRegs
	(_cAliasQuery)->(DBGoTop())

	While !(_cAliasQuery)->( Eof() )
		nCountProc++
		conout("FindPrecoDA1 | Processando registro " + str(nCountProc) + " de " + str(nCountRegs))

		cCodProd := (_cAliasQuery)->DA1_CODPRO
		cCodTab  := (_cAliasQuery)->DA1_CODTAB

		cComando := "UPDATE " + retSQLName("SB1")
		cComando += " SET"
		cComando += "		B1_XDATUPL = '" + dToS(dDataBase) + "'"
		cComando += " WHERE"
		cComando += " 	B1_COD			=	'" + allTrim(cCodProd) + "'"
		cComando += "	AND	B1_XDATUPL		=	''"
		cComando += "	AND	B1_XTIPOPC		=	'1'"
		cComando += "	AND	B1_XMULTI		>=	1" // Apenas ir� integrar produtos com multiplo >= 1 - NAO INTEGRAR COM MULTIPLO ZERADO
		cComando += " AND	D_E_L_E_T_		<>	'*'"

		If (TCSQLExec(cComando) < 0)
			U_SANAM091("1", "Pre�o", "Campo B1_XDATUPL da tabela SB1 n�o foi alterado para DataBase, Favor alterar manualmente", cCodProd)
			conout("Campo B1_XDATUPL da tabela SB1 n�o foi alterado para DataBase, Favor alterar manualmente")
		EndIf

		//ALTERA REGISTRO MSEXP PARA X
		cComando := "UPDATE " + RETSQLNAME("DA1") + " SET DA1_MSEXP= 'X' WHERE DA1_CODPRO='"+Alltrim(cCodProd)+"' AND DA1_CODTAB = '"+Alltrim(cCodTab)+"'"
		If (TCSQLExec(cComando) < 0)
			U_SANAM091("1","Pre�o","Campo MSEXP da tabela DA1 n�o foi alterado para X, Favor alterar manualmente: Cod. Tabela"+Alltrim(cCodTab),cCodProd  )
			conout("Campo MSEXP da tabela DA1 n�o foi alterado para X, Favor alterar manualmente: Cod. Tabela " + Alltrim(cCodTab) + " Produto " + cCodProd)
		EndIf


		(_cAliasQuery)->( dbSkip() )
	EndDo

	(_cAliasQuery)->(DBCloseArea())

	conout(" | | | Finzalizado - FindPrecoDA1 | | |")
return lRet

//---------------------------------------------------
//---------------------------------------------------
static function GXMLCubing(cUserWs, cSenhaWs, nStatus, cIDJet)
	local cScript := ""
	local n		:= 0
	local cQrySB1 := ""

	conout(" | | | GXMLCubing | | |")

	cScript += "<importCubingProduct xmlns='WSJET'>"
	cScript += "<userName>"+cUserWs+"</userName>"
	cScript += "<password>"+cSenhaWs+"</password>"
	cScript += "<cubingProduct>"
	cScript += "<simpleProduct>"
	cScript += "<idProduct>"				+ allTrim(cIDJet)						+ "</idProduct>"
	cScript += "<idExternalProduct>"	+ allTrim((_cAlias)->B1_XSISNOV)	+ "</idExternalProduct>"
	cScript += "<code>"					+ allTrim((_cAlias)->B1_XREFFOR)	+ "</code>"
	cScript += "</simpleProduct>"

	if nStatus == 1 .OR. nStatus == 2
		cScript += "<packaging>"	+ iif((_cAlias)->B1_XEMB == "1","P","R")	+ "</packaging>"
		//elseif nStatus == 2
		//	if oWSJet:exportCubingProduct(cUserWs, cSenhaWs, val(allTrim((_cAlias)->B1_XID)), "", allTrim((_cAlias)->B1_XREFFOR))
		//		if "PACOTE" $ upper(OWSJET:OWSEXPORTCUBINGPRODUCTRESULT:OWSCUBINGPRODUCT[1]:CPACKAGING)
		//			cScript += "<packaging>P</packaging>"
		//		elseif "ROLO" $ upper(OWSJET:OWSEXPORTCUBINGPRODUCTRESULT:OWSCUBINGPRODUCT[1]:CPACKAGING)
		//			cScript += "<packaging>R</packaging>"
		//		endif
		//	endif
	endif

	cScript += "<height>"	+ cValToChar((_cAlias)->B1_XALTU)	+ "</height>"
	cScript += "<width>"		+ cValToChar((_cAlias)->B1_XLARG)	+ "</width>"
	cScript += "<length>"	+ cValToChar((_cAlias)->B1_XPROFU)	+ "</length>"
	cScript += "<diameter>"	+ cValToChar((_cAlias)->B1_XDIAM)	+ "</diameter>"
	cScript += "</cubingProduct>"
	cScript += "</importCubingProduct>"

	conout(" | | | Finalizando - GXMLCubing | | |")
return cScript

//---------------------------------------------------
//---------------------------------------------------
static function GXMLBrand(cIDJet)
	local cScript := ""

	conout(" | | | GXMLBrand | | |")

	cScript := "<attachProductBrand xmlns='WSJET'>"
	cScript += "<userName>"+cUserWs+"</userName>"
	cScript += "<password>"+cSenhaWs+"</password>"
	cScript += "<productBrand>"
	cScript += "<IdProduct>"				+ allTrim(cIDJet)						+ "</IdProduct>"
	cScript += "<IdBrand>"				+ allTrim((_cAlias)->B1_XMARC)		+ "</IdBrand>"
	cScript += "<IdExternalProduct>"	+ allTrim((_cAlias)->B1_XSISNOV)	+ "</IdExternalProduct>"
	cScript += "<Code>"					+ allTrim((_cAlias)->B1_XREFFOR)	+ "</Code>"
	cScript += "<IdExternalBrand></IdExternalBrand>"
	cScript += "</productBrand>"
	cScript += "</attachProductBrand>"

	conout(" | | | Finalizando - GXMLBrand | | |")

return cScript
