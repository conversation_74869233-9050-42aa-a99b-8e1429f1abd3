#ifdef SPANISH
	#define STR0001 "Inventario por Grade"
	#define STR0002 "Cabecalho"
	#define STR0003 "Grade"
	#define STR0004 "Produto:"
	#define STR0005 "Armazem:"
	#define STR0006 "Doc.:"
	#define STR0007 "Por favor, aguarde..."
	#define STR0008 "Referencia"
	#define STR0009 "Cor"
	#define STR0010 "Descricao"
	#define STR0011 "Nao foram informadas as quantidades."
	#define STR0012 "Por favor, aguarde..."
	#define STR0013 "Por favor, informe o produto."
	#define STR0014 "Por favor, informe o armazem."
	#define STR0015 "Por favor, informe apenas numeros."
#else
	#ifdef ENGLISH
		#define STR0001 "Inventario por Grade"
		#define STR0002 "Cabecalho"
		#define STR0003 "Grade"
		#define STR0004 "Produto:"
		#define STR0005 "Armazem:"
		#define STR0006 "Doc.:"
		#define STR0007 "Por favor, aguarde..."
		#define STR0008 "Referencia"
		#define STR0009 "Cor"
		#define STR0010 "Descricao"
		#define STR0011 "Nao foram informadas as quantidades."
		#define STR0012 "Por favor, aguarde..."
		#define STR0013 "Por favor, informe o produto."
		#define STR0014 "Por favor, informe o armazem."
		#define STR0015 "Por favor, informe apenas numeros."
	#else
		#define STR0001 "Inventario por Grade"
		#define STR0002 "Cabecalho"
		#define STR0003 "Grade"
		#define STR0004 "Produto:"
		#define STR0005 "Armazem:"
		#define STR0006 "Doc.:"
		#define STR0007 "Por favor, aguarde..."
		#define STR0008 "Referencia"
		#define STR0009 "Cor"
		#define STR0010 "Descricao"
		#define STR0011 "Nao foram informadas as quantidades."
		#define STR0012 "Por favor, aguarde..."
		#define STR0013 "Por favor, informe o produto."
		#define STR0014 "Por favor, informe o armazem."
		#define STR0015 "Por favor, informe apenas numeros."
	#endif
#endif