#include "rwmake.ch"
#include "protheus.ch"

User Function SANAA205(cCondPgto, lIntegEcom)
	Local _aArea	:= GetArea()
	Local _aAreaSUA
	Local _lRet	:= .T.
	local cStatusOrc	:= "OK"
	local cStatusFin	:= ""
	local cMvSanti15	:= getMv("MV_SANTI15")

	default lIntegEcom := .F.

	If allTrim(cCondPgto) $ cMvSanti15
		cStatusFin := "OK"
	endif

	DbSelectArea("SUA")
	_aAreaSUA	:= GetArea()

	DbSelectArea("SZ2")
	DbSetOrder(1)
	DbGotop()

	DbSeek(xFilial("SZ2"))

	Do While SZ2->Z2_FILIAL == xFilial("SZ2") .And. SZ2->(!Eof())

		DbSelectArea("SZ3")
		DbSetOrder(1)
		DbGoTop()

		If DbSeek(SUA->UA_FILIAL+SUA->UA_NUM+SZ2->Z2_SEQ)
			RecLock("SZ3",.F.)
		Else
			RecLock("SZ3",.T.)
		Endif

		SZ3->Z3_FILIAL		:= 	SUA->UA_FILIAL
		SZ3->Z3_ORC			:= 	SUA->UA_NUM
		SZ3->Z3_SEQ			:= 	SZ2->Z2_SEQ
		SZ3->Z3_DESCR		:=	SZ2->Z2_DESC
		SZ3->Z3_GRUPO		:= 	SZ2->Z2_GRUPO

		If SZ3->(FieldPos("Z3_UACLIEN")) > 0
			SZ3->Z3_UACLIEN		:=	SUA->UA_CLIENTE
			SZ3->Z3_UALOJA		:=	SUA->UA_LOJA
			SZ3->Z3_SITE		:=	IIF( Empty(SUA->UA_XIDJETC), "N", "S" )
			SZ3->Z3_DTVALID		:=	SUA->UA_DTLIM
		EndIf

		if lIntegEcom
			SZ3->Z3_XSTATMJ		:= "3" 	// 1=Aguardando;2=Processando;3=Processado;4=Falha
		else
			SZ3->Z3_XSTATMJ		:= "1" 	// 1=Aguardando;2=Processando;3=Processado;4=Falha
		endif

		SZ3->Z3_EMISSAO		:= dDataBase

		If SUA->UA_XTPOPER $ GetMv("ES_OPETRS")

			SZ3->Z3_STATUS	:= "OK"

		Else

			_aAreaTrb			:= GetArea()
			If &(SZ2->Z2_FUNC)
				RestArea(_aAreaTrb)
				SZ3->Z3_STATUS	:= cStatusOrc
			Else
				RestArea(_aAreaTrb)
				SZ3->Z3_STATUS	:= SZ2->Z2_STATUS
				_lRet	:= .F.
			Endif

		Endif

		//if SZ2->Z2_SEQ == "17"
		//	SZ3->Z3_STATUS := cStatusFin
		//endif
		SZ3->(MsUnlock())
		//SZ3->(DbCommit())
		SZ2->(DbSkip())
	Enddo

	MsUnlockAll()

	RestArea(_aAreaSUA)
	RestArea(_aArea)

Return _lRet