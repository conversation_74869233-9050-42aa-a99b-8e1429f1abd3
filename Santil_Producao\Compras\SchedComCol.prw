#INCLUDE 'Protheus.ch'
#INCLUDE 'TOPConn.ch'
//#INCLUDE 'COMXCOL.ch'
#INCLUDE "FILEIO.CH"
#DEFINE DIRXML  "XMLNFE\"
#DEFINE DIRALER "NEW\"
#DEFINE DIRLIDO "OLD\"
#DEFINE DIRERRO "ERR\" 
#DEFINE TOTVS_COLAB_ONDEMAND 3100 // TOTVS Colaboracao 

//u_SchedComCol()

User Function SchedComCol(aParam)
Local aFiles := {}
Local nX	 := 0
Local aProc  := {}
Local aErros := {}
Private DIRXML  :="XMLNFE\"
Private DIRALER :="NEW\"
Private DIRLIDO :="OLD\"
Private DIRERRO := "ERR\" 
//-- Loga empresa e filial
//RpcSetType(3)
//RpcSetEnv(aParam[1],aParam[2],,,"COM")

//-- Prepara estrutura de diretorios
If !ExistDir(DIRXML)
	MakeDir(DIRXML)
	MakeDir(DIRXML +DIRALER)
	MakeDir(DIRXML +DIRLIDO)
	MakeDir(DIRXML +DIRERRO)
EndIf

//-- Comunica com TSS para baixa dos documentos disponiveis   
COMXCOLTSS()

//-- Processa importacao dos arquivos baixados
aFiles := Directory("\" +DIRXML +DIRALER +"*.xml")
For nX := 1 To Len(aFiles)
	COMXCOLImp(aFiles[nX,1],.T.,@aProc,@aErros)
Next nX

//-- Dispara M-Messenger para erros (evento 052)
If !Empty(aErros)
	MEnviaMail("052",aErros)
EndIf

//-- Dispara M-Messenger para docs disponiveis (evento 053)
If !Empty(aProc)
	MEnviaMail("053",aProc)
EndIf

//RpcClearEnv()
Return