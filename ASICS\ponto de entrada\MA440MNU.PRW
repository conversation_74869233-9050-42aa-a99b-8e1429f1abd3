#Include "Protheus.ch"

/*
#==================================================================================#
| +------------------------------------------------------------------------------+ |
| ! FUNCAO.........:	MA410MNU                                                 ! |
| ! DESCRICAO......:    CONSULTA STATUS APROVACAO                                ! |
| ! AUTOR..........:	BRUNNO ABRIGO                                            ! |
| ! DATA...........:	14/05/2015                                               ! |
| ! PARAMETROS.....:	NIL                                                      ! |
| ! RETORNO........:	NIL                                                      ! |
| +------------------------------------------------------------------------------+ |
#==================================================================================#
*/

#DEFINE STR00A "Status Aprov."

User Function MA440MNU
	Local cRegraAux := "U_ASCONAPR(SC5->(C5_FILIAL+C5_NUM), 'MATA410')"
	
	Aadd(aRotina,{	OemToAnsi(STR00A) , cRegraAux, 0, 7, 0, Nil 	})
	
Return 