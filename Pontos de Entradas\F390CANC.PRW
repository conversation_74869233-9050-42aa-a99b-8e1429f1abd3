#INCLUDE "RWMAKE.CH"
// PE NA EXCLUSAO DO CHEQUE DA ROTINA DE CHEQ S/ TITULOS
User Function F390CANC
/*
Local cQuery   := ""
Local cAliasTOP:= CriaTrab(Nil, .F.)
Local aArea    := GetArea()
Local aAreaSE2 := SE2->(GetArea())
Local cVar1  	:= cVar2 := cVar3 := ""

cQuery := "SELECT E2_FILIAL, E2_ORDPAGO, SE2.R_E_C_N_O_ SE2RECNO "
cQuery += "FROM " + RetSqlName("SE2")+" SE2 "
cQuery += "WHERE SE2.E2_ORDPAGO = '"+cCheque390+"' AND "
cQuery += "SE2.D_E_L_E_T_='' "
cQuery += "ORDER BY " + SqlOrder( SE2->( IndexKey(1) ) )

cQuery := ChangeQuery(cQuery)
MemoWrit('QRYF390CANC.SQL',cQuery)
MsAguarde({|| dbUseArea(.T.,'TOPCONN', TCGenQry(,,cQuery), cAliasTOP,.F.,.T.)}, "Selecionando Registros ...")

DbSelectArea((cAliasTOP))
(cAliasTOP)->( DbGoTop() )

cVar1 := SE2->E2_IMPCHEQ
cVar2 := SE2->E2_NUMBCO
cVar3 := SE2->E2_BCOPAG
/*
Begin Transaction
Do While ! (cAliasTOP)->(Eof() )
	
	SE2->( MsGoTo( (cAliasTOP)->SE2RECNO ) )
	RecLock( "SE2",.F. )
	SE2->E2_IMPCHEQ	:= Space(Len(SE2->E2_IMPCHEQ))
	SE2->E2_NUMBCO 	:= Space(Len(SE2->E2_NUMBCO))
	SE2->E2_BCOPAG    := Space(Len(SE2->E2_BCOPAG))
	MsUnlock()
	(cAliasTOP)->( DbSkip() )
	
EndDo
End Transaction

(cAliasTOP)->( DbGoTop() )

Do While ! (cAliasTOP)->(Eof() )
	
	SE2->( MsGoTo( (cAliasTOP)->SE2RECNO ) )
	
	If SE2->E2_SALDO = 0

		_aCabec 	:= {}
		Aadd(_aCabec, {"E2_PREFIXO" 	, SE2->E2_PREFIXO		, nil})
		Aadd(_aCabec, {"E2_NUM"		 	, SE2->E2_NUM			, nil})
		Aadd(_aCabec, {"E2_PARCELA" 	, SE2->E2_PARCELA		, nil})
		Aadd(_aCabec, {"E2_TIPO" 		, SE2->E2_TIPO			, nil})
		
		Aadd(_aCabec, {"AUTBANCO" 		, cBanco390				, nil})
		Aadd(_aCabec, {"AUTAGENCIA" 	, cAgencia390			, nil})
		Aadd(_aCabec, {"AUTCONTA" 		, cConta390				, nil})
		
		Aadd(_aCabec, {"AUTMOTBX" 		, "NOR"					, nil})
		Aadd(_aCabec, {"AUTDTBAIXA" 	, dDataBase				, nil})
		Aadd(_aCabec, {"AUTDTCREDITO"	, dDataBase				, nil})
      Aadd(_aCabec, {"AUTHIST"		, 'Cheq.: '+Alltrim(Left(cCheque390,7))+' Bco '+cBanco390+' Cancelado', nil})
		Aadd(_aCabec, {"AUTVLRPG"		, SE2->E2_VALOR     	, nil})
		
		lMsErroAuto := .F.
		Begin Transaction
		MSExecAuto({|x,y| fina080(x,y)},_aCabec,5) // Cancelar Baixa
		IF lMsErroAuto
			DisarmTransaction()
			//restauro para a situacao anterior
			RecLock( "SE2",.F. )
			SE2->E2_IMPCHEQ	:= cVar1
			SE2->E2_NUMBCO 	:= cVar2
			SE2->E2_BCOPAG 	:= cVar3
			MsUnlock()
			Break
		Else
			RecLock( "SE2",.F. )
			SE2->E2_ORDPAGO 	:= Space(06)
			MsUnlock()
		Endif
		End Transaction

		IF lMsErroAuto
			MostraErro()
		Endif

	Endif
	
	(cAliasTOP)->( DbSkip() )
	
EndDo

ApMsgInfo('Cheque Cancelado e Baixas Efetuadas')

dbSelectArea((cAliasTOP))
dbCloseArea()

SE2->(RestArea(aAreaSE2))
RestArea(aArea)
*/
Return (.T.)