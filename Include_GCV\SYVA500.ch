#ifdef SPANISH
	#define STR0001"Cantidad entregada"
	#define STR0002"Datos del pedido"
	#define STR0003"Saldo pendiente"
	#define STR0004"Complete los campos del encabezado de esta Factura de entrada."
	#define STR0005"Complete los campos del encabezado de esta Factura de entrada."
	#define STR0006"Este local/sucursal no tiene autorizacion para recibir este pedido, porque este esta dirigido al CD."
	#define STR0007"Por favor espere..."
	#define STR0008"Sucursal"
	#define STR0009"Producto"
	#define	STR0010"Referencia"
	#define	STR0011"Color"
	#define	STR0012"Descripcion del producto"
	#define	STR0013"Cant.Entregada"
	#define	STR0014"Sal.Pendiente"
	#define	STR0015"Cant.Original"
	#define	STR0016"Prec.Compra"
	#define	STR0017"TES"
	#define	STR0018"Fch. Emision"
	#define	STR0019"Periodo entrega"
	#define	STR0020"Grilla"
	#define	STR0021"Tamanos"
	#define	STR0022"No existe pedido para el proveedor seleccionado o para esta tienda."
	#define	STR0023"ANALITICO"
	#define	STR0024"Anular item [F4]"
	#define	STR0025"Cross Docking [F5]"
	#define	STR0026"Definir TES/CFOP [F6]"
	#define	STR0027"Buscar [F7]"
	#define	STR0028"Entrada del pedido de compra con grilla:"
	#define	STR0029"Proveedor:"
	#define	STR0030"Entrada de productos por referencia/color"
	#define	STR0031"Datos del pedido de compra:"
	#define	STR0032"Totalizadores"
	#define	STR0033"Cant.Entregada"
	#define	STR0034"Sal.Pendiente"
	#define	STR0035"Cant.Original"
	#define	STR0036"Pedido pendiente"
	#define	STR0037"Parcialmente atendido"
	#define	STR0038"Totalmente atendido"
	#define	STR0039"Fch Entrega no valida"
	#define	STR0040"No se verifico ningun item para realizar la entrada."
	#define	STR0041"¿Desea confirmar las modificaciones y actualizar los datos de la factura de entrada?."
	#define	STR0042"Atencion"
	#define	STR0043"Seleccione el pedido de compra"
	#define	STR0044"Nº Pedido de compra:"
	#define	STR0045"RCPJ Proveedor:"
	#define	STR0046"&Anular"
	#define	STR0047"Este pedido no existe."
	#define	STR0048"RCPJ no existe."
	#define	STR0049"DATOS DEL PEDIDO:"
	#define	STR0050"PROVEEDOR:"
	#define	STR0051"CONFIRMA CAMBIO PARA EL PROVEEDOR:"
	#define	STR0052"Error al cambiar el proveedor del pedido."
	#define	STR0053"Val. Unitario R$"
	#define	STR0054"UNICO"
	#define	STR0055"Total"
	#define	STR0056"No existe pedido para esta tienda"
	#define	STR0057"El periodo para entrega es"
	#define	STR0058"MV_01VLDPZ = L - Libera la entrada del PC fuera de plazo."
	#define	STR0059"MV_01VLDPZ = B - Bloquea la entrada de PC fuera de plazo."
	#define	STR0060"Este item ya fue totalmente atendido."
	#define	STR0061"Datos de la grilla"
	#define	STR0062"Valores"
	#define	STR0063"Informe las cantidades del producto"
	#define	STR0064"Informe el valor."
	#define	STR0065"El numero del pedido no se informo."
	#define	STR0066"Este pedido no existe."
	#define	STR0067"Seleccione un item verificado para anular."
	#define	STR0068"¿Desea anular la entrada de este item?"
	#define	STR0069"No se permite Cross Docking de items entregados."
	#define	STR0070"¿Desea realizar Cross Docking?"
	#define	STR0071"No se permite clasificar items entregados."
	#define	STR0072"Definicion del TES/CFOP"
	#define	STR0073"¿Desea copiar este TES a ?"
	#define	STR0074"Este item"
	#define	STR0075"De este item hacia abajo"
	#define	STR0076"Para todos los items"
	#define	STR0077"1=Producto"
	#define	STR0078"2=Referencia"
	#define	STR0079"3=Descripcion"
	#define	STR0080"1=Contiene la expresion"
	#define	STR0081"2=Inicia con la expresion"
	#define	STR0082"Ubicar"
	#define	STR0083"Espere..."
	#define	STR0084"Ubicando producto..."
	#define	STR0085"Salir"
#else
	#ifdef ENGLISH
		#define STR0001"Amount Delivered"
		#define STR0002"Order Data"
		#define STR0003"Pending Balance"
		#define STR0004"Fill out the header fields on this Entry Invoice."
		#define STR0005"Fill out the header fields on this Entry Invoice."
		#define STR0006"This location/branch does not have the permission to receive this order, it was directed to the CD."
		#define STR0007"Please wait..."
		#define STR0008"Branch"
		#define STR0009"Product"
		#define STR0010"Reference"
		#define STR0011"Color"
		#define STR0012"Product Description"
		#define STR0013"Delivered Qtty."
		#define STR0014"Pending Blc."
		#define STR0015"Original Qtty."
		#define STR0016"Purchase Prc."
		#define STR0017"TES"
		#define STR0018"Issue Dt."
		#define STR0019"Delivery Period"
		#define STR0020"Grid"
		#define STR0021"Sizes"
		#define STR0022"There is no order to the selected Supplier, or for this store."
		#define STR0023"ANALYTIC"
		#define STR0024"Cancel Item [F4}"
		#define STR0025"Cross Docking [F5]"
		#define STR0026"Define TES/CFOP [F6]"
		#define STR0027"Search [F7]"
		#define STR0028"Purchase Order Entry with Grid: "
		#define STR0029"Supplier:"
		#define STR0030"Product Entry by Reference/Color"
		#define STR0031"Purchase Order Data: "
		#define STR0032"Totalizers"
		#define STR0033"Delivered Qtty."
		#define STR0034"Pending Blc."
		#define STR0035"Original Qtty."
		#define STR0036"Open Order"
		#define STR0037"Partially Served"
		#define STR0038"Totally Served"
		#define STR0039"Invalid Delivery Dt"
		#define STR0040"No item checked to make the Entry."
		#define STR0041"Confirm changes and Update Entry Invoice Data?."
		#define STR0042"Warning"
		#define STR0043"Select Purchase Order"
		#define STR0044"No. Purchase Order: "
		#define STR0045"CNPJ Supplier: "
		#define STR0046"&Cancel"
		#define STR0047"Order inexistent."
		#define STR0048"CNPJ inexistent."
		#define STR0049"ORDER DATA: "
		#define STR0050"SUPPLIER: "
		#define STR0051"CONFIRM CHANGE TO SUPPLIER: "
		#define STR0052"Error at changing to Order supplier."
		#define STR0053"Value Unit R$"
		#define STR0054"UNIQUE"
		#define STR0055"Total"
		#define STR0056"Order does not exist for this store"
		#define STR0057"The period for Delivery and  "
		#define STR0058"MV_01VLDPZ = L - Release PC entry out of term."
		#define STR0059"MV_01VLDPZ = B - Block PC entry out of term."
		#define STR0060"Item totally served."
		#define STR0061"Grid Data"
		#define STR0062"Values"
		#define STR0063"Enter Product Quantities"
		#define STR0064"Enter Value."
		#define STR0065"Order Number not entered."
		#define STR0066"Order inexistent."
		#define STR0067"Select an item already checked to cancel."
		#define STR0068"Cancel item entry?"
		#define STR0069"No and allow Cross Docking of delivered items."
		#define STR0070"Make Cross Docking?"
		#define STR0071"No and allow classification of delivered items."
		#define STR0072"TES/CFOP definition"
		#define STR0073"Replicate this TES to?"
		#define STR0074"This item"
		#define STR0075"From this item to below"
		#define STR0076"To all items"
		#define STR0077"1=Product"
		#define STR0078"2=Reference"
		#define STR0079"3=Description"
		#define STR0080"1=Contain the Expression"
		#define STR0081"2=Start with the Expression"
		#define STR0082"Locate"
		#define STR0083"Wait..."
		#define STR0084"Locate Product..."
		#define STR0085"Exit"
	#else
		#define STR0001"Quantidade Entregue"
		#define STR0002"Dados do Pedido"
		#define STR0003"Saldo Pendente"
		#define STR0004"Preencha os campos do cabecalho desta NF de Entrada."
		#define STR0005"Preencha os campos do cabecalho desta NF de Entrada."
		#define STR0006"Este local/filial nao tem permissao para receber este pedido, pois o mesmo foi direcionado para o CD."
		#define STR0007"Por favor aguarde..."
		#define STR0008"Filial"
		#define STR0009"Produto"
		#define STR0010"Referencia"
		#define STR0011"Cor"
		#define STR0012"Descri??o do Produto"
		#define STR0013"Qtd.Entregue"
		#define STR0014"Sld.Pendente"
		#define STR0015"Qtd.Original"
		#define STR0016"Prc.Compra"
		#define STR0017"TES"
		#define STR0018"Dt Emissao"
		#define STR0019"Per?odo Entrega"
		#define STR0020"Grade"
		#define STR0021"Tamanhos"
		#define STR0022"Nao existe pedido para o Fornecedor selecionado, ou para esta loja."
		#define STR0023"ANALITICO"
		#define STR0024"Cancelar Item [F4]"
		#define STR0025"Cross Docking [F5]"
		#define STR0026"Definir TES/CFOP [F6]"
		#define STR0027"Pesquisar [F7]"
		#define STR0028"Entrada do Pedido de Compra com Grade:"
		#define STR0029"Fornecedor:"
		#define STR0030"Entrada de Produtos por Referencia/Cor"
		#define STR0031"Dados do Pedido de Compra:"
		#define STR0032"Totalizadores"
		#define STR0033"Qtd.Entregue"
		#define STR0034"Sld.Pendente"
		#define STR0035"Qtd.Original"
		#define STR0036"Pedido em Aberto"
		#define STR0037"Parcialmente Atendido"
		#define STR0038"Totalmente Atendido"
		#define STR0039"Dt Entrega Invalida"
		#define STR0040"Nenhum item foi conferido para realizar a Entrada."
		#define STR0041"Deseja Confirma as alteracoes e Atualizar os dados da Nota Fiscal de Entrada ?."
		#define STR0042"Atencao"
		#define STR0043"Selecione o Pedido de Compra"
		#define STR0044"No. Pedido de Compra:"
		#define STR0045"CNPJ Fornecedor:"
		#define STR0046"&Cancelar"
		#define STR0047"Este pedido nao existe."
		#define STR0048"CNPJ nao existe."
		#define STR0049"DADOS DO PEDIDO:"
		#define STR0050"FORNECEDOR:"
		#define STR0051"CONFIRMA TROCA PARA O FORNECEDOR:"
		#define STR0052"Erro ao trocar o fornecedor do Pedido."
		#define STR0053"Vlr. Unitario R$"
		#define STR0054"UNICO"
		#define STR0055"Total"
		#define STR0056"Pedido nao existe para esta loja"
		#define STR0057"O periodo para Entrega ? de"
		#define STR0058"MV_01VLDPZ = L - Libera entrada do PC fora do prazo."
		#define STR0059"MV_01VLDPZ = B - Bloqueia a entrada de PC fora do prazo."
		#define STR0060"Este item ja foi totalmente atendido."
		#define STR0061"Dados da Grade"
		#define STR0062"Valores"
		#define STR0063"Informe as Quantidades do Produto"
		#define STR0064"Informe o Valor."
		#define STR0065"O Numero do Pedido nao foi informado."
		#define STR0066"Este pedido nao existe."
		#define STR0067"Selecione um item ja conferido para cancelar."
		#define STR0068"Deseja cancelar a entrada deste item?"
		#define STR0069"Nao e permite Cross Docking de itens ja entregues."
		#define STR0070"Deseja realizar Cross Docking?"
		#define STR0071"Nao e permite classificar itens ja entregues."
		#define STR0072"Definicao do TES/CFOP"
		#define STR0073"Deseja replicar este TES para ?"
		#define STR0074"Este Item"
		#define STR0075"Deste Item para Baixo"
		#define STR0076"Para todos os Itens"
		#define STR0077"1=Produto"
		#define STR0078"2=Referencia"
		#define STR0079"3=Descricao"
		#define STR0080"1=Contem a Expressao"
		#define STR0081"2=Inicia com a Expressao"
		#define STR0082"Localizar"
		#define STR0083"Aguarde..."
		#define STR0084"Localizando produto..."
		#define STR0085"Sair"
	#endif
#endif
