#include "rwmake.ch"
/*
Analista 	: <PERSON>
Data     	: 30/09/2006
*/
** Alterado por: <PERSON> - amjg<PERSON><EMAIL> - Em: 10/10/2006

User Function M103DSE2
local aAreaSE2		:= SE2->(GetArea())
Local cEnv			:= Alltrim(Upper(GetEnvServer()))
Local cParcCofins, sFornece, sLoja, nx, cVar
Local nRecSE2		:= SE2->(<PERSON><PERSON><PERSON>())

SE2->( dbSetOrder(1) )
IF SE2->( dbSeek( xFilial("SE2")+SF1->F1_SERIE+SF1->F1_DOC+SE2->E2_PRETPIS+"TX "+GetMv("MV_FORPIS")+"00" ) )
	RecLock( "SE2", .F. )
	SE2->( dbDelete() )
	SE2->( MsUnlock() )
Endif
SE2->(dbGoto(nRecSE2))
IF SE2->( dbSeek( xFilial("SE2")+SF1->F1_SERIE+SF1->F1_DOC+SE2->E2_PRETCOF+"TX "+GetMv("MV_FORCOFI")+"00" ) )
	RecLock( "SE2", .F. )
	SE2->( dbDelete() )
	SE2->( MsUnlock() )
Endif
SE2->(dbGoto(nRecSE2))
IF SE2->( dbSeek( xFilial("SE2")+SF1->F1_SERIE+SF1->F1_DOC+SE2->E2_PRETCSL+"TX "+GetMv("MV_FORCSLL")+"00" ) )
	RecLock( "SE2", .F. )
	SE2->( dbDelete() )
	SE2->( MsUnlock() )
Endif

Restarea(aAreaSE2)

Return
