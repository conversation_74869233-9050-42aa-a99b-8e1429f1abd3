<%
Local nCont
%><?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">
<%=oSelf:onumFmts:GetTag()%>
<%=oSelf:oFonts:GetTag()%>
	<fills count="<%=Len(oSelf:aCorPreenc)%>">
	<% for nCont:=1 to Len(oSelf:aCorPreenc) %>
		<%=oSelf:aCorPreenc[nCont]:GetTag()%>
	<% next %>
	</fills>
	<%= oSelf:oBorders:GetTag() %>
	<cellStyleXfs count="1">
		<xf numFmtId="0" fontId="0" fillId="0" borderId="0"/>
	</cellStyleXfs>
	<%=oSelf:oSyles:GetTag()%>
	<cellStyles count="1">
		<cellStyle name="Normal" xfId="0" builtinId="0"/>
	</cellStyles>
	<%=oSelf:odxfs:GetTag()%>
</styleSheet>