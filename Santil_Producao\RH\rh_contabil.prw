***********
* Debito  *
***********
user function deb_rh_contabil()

	cConta := space(10)

	cConta := FDESC("SRV",SRZ->RZ_PD,"RV_DEBITO")
	if trim(cConta) = ""
	 	msgalert("Conta nao encontrada para a Verba - " + SRZ->RZ_PD)	
	endif  	

return(cConta)    

***********
* Credito *
***********
user function cre_rh_contabil()

	cConta := space(10)

	cConta := FDESC("SRV",SRZ->RZ_PD,"RV_CREDITO")
	if trim(cConta) = ""
	 	msgalert("Conta nao encontrada para a Verba - " + SRZ->RZ_PD)	
	endif  	


return(cConta)  


***************************
* Centro de Custo Debito  *
***************************
user function cc_deb_rh_contabil()

cCC := space(10)
crecebCC := space(10)
	cConta := FDESC("SRV",SRZ->RZ_PD,"RV_DEBITO")
    cRecebCC := fdesc("CT1",cConta,"CT1_CCOBRG")

 IF cRecebCC = "1"
       cCC:= SRZ->RZ_CC
 endif 

return(cCC)    

*****************************
* * Centro de Custo Credito *
*****************************
user function cc_cre_rh_contabil()

cCC := space(10)
crecebCC := space(10)

	cConta := FDESC("SRV",SRZ->RZ_PD,"RV_CREDITO")
    cRecebCC := fdesc("CT1",cConta,"CT1_CCOBRG")

 IF cRecebCC = "1"
       cCC:= SRZ->RZ_CC
 endif 

return(cCC)


//**************************
//* HISTORICO DO LP 
//*************************

USER FUNCTION xHISTPD

    Local aOld := GETAREA()
    Local nSrvOrd := SRV->(IndexOrd())
    Local nSrvRec := SRV->(Recno())
    Local xHist
    
    dbSelectArea( "SRV" )
    dbSetOrder(1)
    dbSeek( xFilial("SRV")+SRZ->RZ_PD )

        xHIST := "*" + srz->rz_pd +" "+ srv->rv_desc +" REF. " + MESEXTENSO(DDATABASE) + "/" + STRZERO(YEAR(DDATABASE),4)
        xHIST := xHIST + "*"

    SRV->(dbSetOrder( nSrvOrd ))
    SRV->(dbGoTo( nSrvRec ))
    RESTAREA( aOld )    

RETURN(xHIST)

