#INCLUDE "rwmake.ch"
/*
Analista 	: <PERSON>
Data     	: 09/02/2006
ExecBlock	: F050MCP
Ponto			: Tratar Array com os campos editaveis
Retorno		: Array
*/
** Criado por: <PERSON> - <PERSON>@gmail.com - Em: 09/02/2006

USER FUNCTION F050MCP

Local _aCpos := PARAMIXB
Local nPos   := 0
If SE2->E2_TIPO == "PR "
   nPos := Ascan( _aCpos, "E2_VALOR" )
	if nPos == 0
		AADD(_aCpos,"E2_VALOR")
	Endif
   nPos := Ascan( _aCpos, "E2_OBRA" )
	if nPos == 0
		AADD(_aCpos,"E2_OBRA")
	Endif
EndIf
Return(_aCpos)