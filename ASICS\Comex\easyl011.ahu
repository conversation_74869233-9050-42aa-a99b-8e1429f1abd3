<EASYLINK>
<SERVICE>
<ID>011</ID>
<DATA_SELECTION>
   <FIN_SEQ>If(Type('nParcEst') == 'N', nParcEst, 0)</FIN_SEQ>
   <FIN_SEND>
      <FIN_IT>
         <FIN_ELE1>'E2_FILIAL'</FIN_ELE1>
         <E5_FILIAL>xFilial('SE2')</E5_FILIAL>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E2_PREFIXO'</FIN_ELE1>
         <E5_PREFIXO>SE5->E5_PREFIXO</E5_PREFIXO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E2_NUM'</FIN_ELE1>
         <E5_NUMERO>SE5->E5_NUMERO</E5_NUMERO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E2_PARCELA'</FIN_ELE1>
         <E5_PARCELA>SE5->E5_PARCELA</E5_PARCELA>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E2_TIPO'</FIN_ELE1>
         <E5_TIPO>SE5->E5_TIPO</E5_TIPO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'AUTHIST'</FIN_ELE1>
         <AUTHIST>If(FindFunction('AF200HisEmb'),AF200HisEmb(),'Emb.:' + EEC->EEC_PREEMB)</AUTHIST>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
   </FIN_SEND>
</DATA_SELECTION>

<DATA_SEND>
   <SEND>EECINFIN(#TAG FIN_SEND#, 'SE2', 'ESTBAIXA',,#TAG FIN_SEQ#)</SEND>
</DATA_SEND>

<DATA_RECEIVE>
   <SRV_STATUS>.T.</SRV_STATUS>
</DATA_RECEIVE>
</SERVICE>
</EASYLINK>
