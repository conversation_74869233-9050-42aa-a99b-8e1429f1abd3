#include 'protheus.ch'

user function cns002()
//nome, valor inic,tam,dec, Picture, Cons. Padr,
local aTst := {;
{'teste1',nil,'M',5000,0,,"SA2",,},;
{'combo',nil,'C',1,0,,,,,{"S=SIM","N=NAO"}},;
{'teste2',nil,'N',9,2,'@Z 999,999,999.99',,,},;
{'teste3','TESTE','C',10,0,,"SA2",,},;
{'teste4',nil,'D',,,"99/99/99",,,},;
{'teste5','TESTE','C',10,0,,"SA2",,},;
{'teste25','TESTE','C',10,0,,"SA2",,}}


u_TForm(550,500,3,aTst)

return

User Function TForm(nTamX,nTamY,nCols,aStruct,cTitulo)
local nEspac := 10
private oScr,oDlg, nx:=1, ny:=1, nTotal, nLin, nCol, aMemo:={}
private a,x,aGrid := aStruct 

lOk:=.F.
a:=@aGrid
nTamGet:= ((nTamY-30)/nCols-(nCols*nEspac))/2
nTotal:= Len(aGrid)
nLin := 18

DEFINE MSDIALOG oDlg FROM 0,0 TO nTamX,nTamY PIXEL TITLE "   "+cTitulo

oScr:= TScrollBox():New(oDlg,14,0,(nTamX-(14*2))/2,nTamY/2,.T.,.T.,.T.)

for  nx:=1 to nTotal
	
	x:=nx
	
	if aGrid[nx][3] == 'N'
		iif(aGrid[nx,2] == nil, aGrid[nx,2] := 0,)
	elseif aGrid[nx][3] == 'D'
		iif(aGrid[nx,2] == nil, aGrid[nx,2] := STOD('        '),)
	elseif aGrid[nx][3] == 'C' .OR. aGrid[nx][3] == 'M'
		if aGrid[nx,2] == nil
			aGrid[nx,2] := Space(aGrid[nx][4]+iif(aGrid[nx][5] <> nil, aGrid[nx][5], 0))
		else
			aGrid[nx,2] += Space(aGrid[nx][4]+iif(aGrid[nx][5] <> nil, aGrid[nx][5], 0)-Len(aGrid[nx,2]))
		endIf
	endIf
	if aGrid[nx][3] == 'M'
		
		
		if ny+1 > nCols
			ny++ 
			lQuebraLinha(nCols)
		endIf 
		
		AADD(aMemo,{ny,1})  
		tMultiget():New(nLin,nTamGet*(ny-1)+(ny*nEspac),&("{|u| if(PCount()>0,aGrid["+cValToChar(nx)+"][2]:=u,aGrid["+cValToChar(nx)+"][2])}" ) ,oScr,(nTamGet*2)+10,39,,,,,,.T.,,.T.,,,,,,,,.F.,.T.)
	elseif len(aGrid[nx])>=10 .and. aGrid[nx][10] != nil
		tComboBox():New(nLin,nTamGet*(ny-1)+(ny*nEspac),&("{|u| if(PCount()>0,aGrid["+cValToChar(nx)+"][2]:=u,aGrid["+cValToChar(nx)+"][2])}" ),aGrid[nx][10],nTamGet,10,oScr,,,,,,.T.,,,,,,,,,"aGrid["+cValToChar(nx)+"][2]")
	else
		TGet():New(nLin,nTamGet*(ny-1)+(ny*nEspac),&("{|u| if(PCount()>0,aGrid["+cValToChar(nx)+"][2]:=u,aGrid["+cValToChar(nx)+"][2])}" ),;
		oScr,nTamGet,10,&("aGrid["+cValToChar(nx)+"][6]"),&("aGrid["+cValToChar(nx)+"][9]"),,,,,,.T.,,,,,,,iif(Len(aGrid[nx])>=11,aGrid[nx,11],.F.),,&("aGrid["+cValToChar(nx)+"][7]"),"aGrid["+cValToChar(nx)+"][2]",,,,)
	endIf
	
	TSay():New(nLin-9,nTamGet*(ny-1)+(ny*nEspac),&("{|| aGrid["+cValToChar(nx)+"][1]}"),oScr,,,,,,.T.,CLR_RED,CLR_WHITE,nTamGet,15)
	
	if aGrid[nx][3] == 'M'
		ny+=2 
	else
		ny++
	endIf
	
	lQuebraLinha(nCols)
	
	for nz:=1 to len(aMemo)
		if ny == aMemo[nz,1] .and. aMemo[nz,2] > 0
			ny+=2
			aMemo[nz,2]--
		endif
	next
	
	lQuebraLinha(nCols)
	
next nx

ACTIVATE MSDIALOG oDlg CENTERED ON INIT (EnchoiceBar(oDlg,{||lOk:=.T.,oDlg:End()},{||lOk:=.F.,oDlg:End()},,))    

return aGrid

Static function lQuebraLinha(nCols) 
local lRet := .F.

	if ny > nCols
		ny:=1
		nLin+=25
		lRet := .T.
	endIf  
	
return lRet
