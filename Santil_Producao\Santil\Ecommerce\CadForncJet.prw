#Include 'Protheus.ch'

/*/{Protheus.doc} CadFornJet
@description 	Cadastro de Fornecedores E-commerce
<AUTHOR>
@version		1.0
@since 			30/09/2015
@type 			Function
/*/

USER FUNCTION CadFornJet()
 
PRIVATE cCadastro  := "Cadastro de Fornecedores E-commerce"
PRIVATE aRotina     :=  { {"Pesquisar" ,"AxPesqui",0,1} ,; 
                        {"Visualizar","AxVisual",0,2} ,; 
                        {"Incluir"   ,"AxInclui",0,3} ,; 
                        {"Alterar"   ,"AxAltera",0,4} ,; 
                        {"Excluir"   ,"AxDeleta",0,5} } 
   Private cDelFunc := ".T." // Validacao para a exclusao. 
   Private cString := "PB3" 

dbselectarea('PB3')
dbsetorder(1)

 
AxCadastro("PB3", cCadastro, ".T.", ".T." )
 
Return Nil