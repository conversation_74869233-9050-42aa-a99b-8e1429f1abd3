<EASYLINK>
<SERVICE>
<ID>014</ID>	
<DATA_SELECTION>
	<FIN_NUM>cTitFin</FIN_NUM>
	<FIN_SEND>
		<FIN_IT>
			<FIN_ELE1>'E2_NUM'</FIN_ELE1>
			<E2_NUM>#TAG FIN_NUM#</E2_NUM>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_PREFIXO'</FIN_ELE1>
			<E2_PREFIXO>'EEC'</E2_PREFIXO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_PARCELA'</FIN_ELE1>
			<E2_PARCELA>cParcFin</E2_PARCELA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_TIPO'</FIN_ELE1>
			<E2_TIPO>cTipTit</E2_TIPO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_NATUREZ'</FIN_ELE1>
			<E2_NATUREZ>cNatFin</E2_NATUREZ>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_FORNECE'</FIN_ELE1>
			<E2_FORNECE>cFinForn</E2_FORNECE>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_LOJA'</FIN_ELE1>
			<E2_LOJA>cFinLoja</E2_LOJA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_EMISSAO'</FIN_ELE1>
			<E2_EMISSAO>dDtEmis</E2_EMISSAO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VENCTO'</FIN_ELE1>
			<E2_VENCTO>dDtVenc</E2_VENCTO>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VENCREA'</FIN_ELE1>
			<E2_VENCREA>DataValida(dDtVenc, .T.)</E2_VENCREA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VENCORI'</FIN_ELE1>
			<E2_VENCORI>dDtVenc</E2_VENCORI>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VALOR'</FIN_ELE1>
			<E2_VALOR>nValorFin</E2_VALOR>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>		
		<FIN_IT>
			<FIN_ELE1>'E2_MOEDA'</FIN_ELE1>
			<E2_MOEDA>nMoeFin</E2_MOEDA>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_VLCRUZ'</FIN_ELE1>
			<E2_VLCRUZ>nValorFin*(BuscaTaxa(cMoeda,dDtEmis))</E2_VLCRUZ>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
		<FIN_IT>
			<FIN_ELE1>'E2_HIST'</FIN_ELE1>
			<E2_HIST>'Emb.:' + AllTrim(M->EEC_PREEMB) </E2_HIST>
			<FIN_ELE3>''</FIN_ELE3>
		</FIN_IT>
           	<FIN_IT>
          	<FIN_ELE1>'E2_ORIGEM'</FIN_ELE1>
          		<E2_ORIGEM>'sigaeec'</E2_ORIGEM>
        		<FIN_ELE3>''</FIN_ELE3>
	      	</FIN_IT>
</FIN_SEND>
</DATA_SELECTION>
<DATA_SEND>
	<SEND>EECINFIN(#TAG FIN_SEND#, 'SE2', 'INCLUIR')</SEND>
</DATA_SEND>
<DATA_RECEIVE>
   <SRV_STATUS>.T.</SRV_STATUS>
</DATA_RECEIVE>
</SERVICE>
</EASYLINK>