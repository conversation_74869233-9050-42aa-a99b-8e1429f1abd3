#Include 'Protheus.ch'

/*/{Protheus.doc} AFATM048
Funcao para efetuar a consulta de uma informacao exata.
<AUTHOR>
@since 07/06/2016
@version 1.0
@param cTab, caracter, <PERSON><PERSON>a a ser consultada
@param aSeek, array, Array com dados a serem consultados na seguinte estrutura {{"CAMPO","SEEK"},{"CAMPO","SEEK"}}
@return lRet, Indica se encontrou o registro pesquisado
/*/
User Function AFATM048(cTab,aSeek)
	Local lRet	:= .F.
	Local cQry	:= ''
	Local nX	:= 0
	Local cAli	:= GetNextAlias()

	// +-------------------------------------------+
	// | Verifica se as informacoes foram passadas |
	// +-------------------------------------------+
	If cTab <> Nil .And. aSeek <> Nil
		cQry := "SELECT COUNT(*) QTD" + CRLF
		cQry += "FROM " + RetSqlName(cTab) + " " + cTab + CRLF
		cQry += "WHERE " + CRLF
		For nX := 1 To Len(aSeek)
			If nX == 1
				cQry += "	" + aSeek[nX][1] + " = '" + aSeek[nX][2] + "'" + CRLF
			Else
				cQry += "	AND " + aSeek[nX][1] + " = '" + aSeek[nX][2] + "'" + CRLF
			EndIf
		Next nX
		cQry += "	AND " + cTab + ".D_E_L_E_T_ = ' '"

		//+------------------------------------+
		//| Fecha a tabela caso esteja aberta. |
		//+------------------------------------+
		If Select(cAli) > 0
			(cAli)->(DbCloseArea())
		EndIf

		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAli,.T.,.T.)

		//+-------------------------------+
		//| Verifica se o produto existe. |
		//+-------------------------------+
		If (cAli)->QTD > 0
			lRet := .T.
		EndIf

		(cAli)->(DbCloseArea())
	EndIf
Return lRet