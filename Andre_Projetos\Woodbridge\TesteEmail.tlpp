#include "protheus.ch"

User Function TesteEmail()

	//Local cEmail := "<EMAIL>;andre_lanz<PERSON>@woodbridgegroup.com;william_ara<PERSON><EMAIL>"
	Local cEmail := "<EMAIL>"
	WFNotifyAdmin(cEmail,"TESTE1","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE2","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE3","testehtml",{})
	/*
	WFNotifyAdmin(cEmail,"TESTE14","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE15","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE16","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE17","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE18","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE19","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE20","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE21","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE22","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE23","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE24","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE25","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE26","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE27","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE28","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE29","testehtml",{})
	WFNotifyAdmin(cEmail,"TESTE30","testehtml",{})
	*/
Return
