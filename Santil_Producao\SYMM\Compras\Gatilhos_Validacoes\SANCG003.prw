#Include 'Protheus.ch'

User Function SANCG003()
	Local aAreaAtu 	:= GetArea()
	Local lRet := .T.
	Local nValor 	:= &(ReadVar())
	Local cCampo 	:= ReadVar()
	local nColuna 	:= GDFieldPos(SUBSTR(cCampo,4,10))
	Local aParamBox := {}
	Local aCombo 	:= {"Somente Este","Todos","Para Baixo","Para Cima"}
	Local i 		:= 0
	Local aRet 		:= {}
	Local nLinha 	:= n
	Local lExibePar	:= .T.
	Private cCadastro := "Atualiza"

	aAdd(aParamBox,{3,cCadastro,1,aCombo,50,"",.F.})

	If ParamBox(aParamBox,cCadastro,@aRet)

		IF aRet[1] == 1 // Somente Este
			GDFieldPut ( SUBSTR(cCampo,4,10), nValor, nLinha)
		ElseIF aRet[1] == 2 // Todos
			FOR	i := 1 to Len(aCols)
				GDFieldPut( SUBSTR(cCampo,4,10), nValor, i)
			next i

		ElseIf aRet[1] == 3 // Todos Abaixo
			FOR	i := nLinha to Len(aCols)
				GDFieldPut ( SUBSTR(cCampo,4,10), nValor, i)
			next i
		ElseIf aRet[1] == 4
			FOR	i := 1 to nLinha // Todos Acima
				GDFieldPut ( SUBSTR(cCampo,4,10), nValor, i)
			next i
		EndIf

	EndIf

RestArea(aAreaAtu)

Return(lRet)
