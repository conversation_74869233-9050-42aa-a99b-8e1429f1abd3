#Include 'Totvs.ch'

//==========================================================================
/*/{Protheus.doc} DL200TRB
@description 	Ponto de Entrada na montagem do Browse de monta carga
				(Altera tamanho do campo de volumes pois esta estourando)
<AUTHOR> D. Paoli filho
@version		1.0
@param			Exp
@return			Nil
@type 			Function
/*/
//==========================================================================
User Function DL200TRB()
	Local aCampos	:= PARAMIXB
	Local nPosCpo	:= 0
	
	nPosCpo := Ascan( aCampos, {|x| x[1] == "PED_VOLUM" } )
	
	If nPosCpo > 0
		aCampos[nPosCpo][3]	:= 17
	EndIf
	
Return aCampos
