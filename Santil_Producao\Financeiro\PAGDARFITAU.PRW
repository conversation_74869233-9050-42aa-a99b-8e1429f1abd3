#include "rwmake.ch"

USER FUNCTION PAGDARFITAU()

cret:=""
cnum:=Posicione("SE2",15,xfilial("SE2")+SEA->EA_NUMBOR,"E2_CODRET")
cnug:=Posicione("SE2",15,xfilial("SE2")+SEA->EA_NUMBOR,"E2_CODREC")
cplaca:=Posicione("SE2",15,xfilial("SE2")+SEA->EA_NUMBOR,"E2_PLACA")

IF SEA->EA_MODELO == "13"

	IF cnum <> " " .OR. cnug <> " " .OR. cplaca <> " "
	
	cret:="91"
	
	ELSE
	
	cret:="13"
	
	ENDIF

ENDIF


IF SEA->EA_MODELO <> "13" 

cret:=SEA->EA_MODELO

ENDIF  

RETURN(cret)        