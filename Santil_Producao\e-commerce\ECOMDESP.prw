#include "rwmake.ch"
#include "protheus.ch"
#include "totvs.ch"
#include "topconn.ch"
#include "tbiconn.ch"

#define CRLF chr(13) + chr(10)
//---------------------------------------------------------------------------------
/*/{Protheus.doc} ECOMDESP

Rotina para atualizar o status dos pedidos para despachados

<AUTHOR>
@since 22/01/2015
@version 1.0

@param aParam, Array, Array contendo Empresa e Filial nas posições 1 e 2 respectivamente
/*/
//---------------------------------------------------------------------------------
user function ECOMDESP(aParam)
	private	aLogDesp	:= {}

	if isBlind()
		if !empty(aParam[3]) .and. !empty(aParam[4])

			conout(" * * * * * * * * PARAMETROS ENVIADOS * * * * * * * * * * * * *")
			conout("aParam[1]" + str(aParam[1]))
			conout("aParam[2]" + str(aParam[2]))
			conout("aParam[3]" + aParam[3])
			conout("aParam[4]" + aParam[4])
			conout("aParam[5]" + aParam[5])
			conout("aParam[6]" + aParam[6])
			conout("Tamanho do parametro: " + str(len(aParam)))
			conout(" * * * * * * * * * * * * * * * * * * * * *")

			PREPARE ENVIRONMENT EMPRESA aParam[3] FILIAL aParam[4]

			conout("--------------------------------------------------------")
			conout("----Iniciando Job - Despacho de Pedidos - E-Commerce----")
			conout("Hora		> " + Time())
			conout("Empresa	> " + aParam[3])
			conout("Filial	> " + aParam[4])
			conout("--------------------------------------------------------")

			roda()

			conout("---------------------------------------------------------")
			conout("----Finalizado Job - Despacho de Pedidos - E-Commerce----")
			conout(" > " + Time())
			conout("---------------------------------------------------------")			
			RESET ENVIRONMENT
		else
			conOut("-------------------------------------------------------------------------------")
			conOut("----- ERRO: Não foram enviados os parâmetros de Empresa/Filial para o JOB -----")
			conOut("-------------------------------------------------------------------------------")
		endif
	else
		msAguarde({|| roda()}, "Aguarde, por favor...", "Despachando Pedidos - E-Commerce") 
	endif
return aLogDesp

//---------------------------------------------------------------------------------
/*/{Protheus.doc} roda

Executa processamento

<AUTHOR>
@since 22/01/2015
/*/
//---------------------------------------------------------------------------------
static function roda()
	local aArea		:= getArea()
	local aAreaPA4	:= PA4->(getArea())

	pvSite()
	while !QRYSUA->(EOF())
		conout("Despachando pedido " + QRYSUA->UA_XIDJETC + "...")
		despachaPV()
		QRYSUA->(DBSkip())
	enddo

	QRYSUA->(DBCloseArea())

	restArea(aArea)
	restArea(aAreaPA4)
return

//---------------------------------------------------------------------------------
/*/{Protheus.doc} pvSite

Verifica se � um pedido de venda feito atrav�s do site

<AUTHOR>
@since 22/01/2015
/*/
//---------------------------------------------------------------------------------
static function pvSite()
	local cQuery	:= ""

	cQuery := "SELECT UA_NUM, UA_XIDJETC, F2_FILIAL, F2_CLIENTE, F2_LOJA, F2_DOC, F2_SERIE"		+ CRLF
	cQuery += " FROM			" + retSQLName("SC5") + " SC5"											+ CRLF
	cQuery += " INNER JOIN	" + retSQLName("SUA") + " SUA"											+ CRLF
	cQuery += " ON	SC5.C5_NUMSUA		=	SUA.UA_NUM"												+ CRLF
	cQuery += " INNER JOIN	" + retSQLName("SF2") + " SF2"											+ CRLF
	cQuery += " ON		SC5.C5_NOTA	=	SF2.F2_DOC"												+ CRLF
	cQuery += " 	AND	SC5.C5_SERIE		=	SF2.F2_SERIE"												+ CRLF
	cQuery += " 	AND	SC5.C5_CLIENTE	=	SF2.F2_CLIENTE"											+ CRLF
	cQuery += " 	AND	SC5.C5_LOJACLI	=	SF2.F2_LOJA"												+ CRLF
	cQuery += " WHERE"																					+ CRLF
	cQuery += " 		SC5.D_E_L_E_T_					<>	'*'"										+ CRLF
	cQuery += " 	AND	SUA.D_E_L_E_T_					<>	'*'"										+ CRLF
	cQuery += " 	AND	SF2.D_E_L_E_T_					<>	'*'"										+ CRLF
	cQuery += " 	AND	SC5.C5_FILIAL						=	'" + xFilial("SC5")	+ "'"				+ CRLF
	cQuery += " 	AND	SUA.UA_FILIAL						=	'" + xFilial("SUA")	+ "'"				+ CRLF
	cQuery += " 	AND	SF2.F2_FILIAL						=	'" + xFilial("SF2")	+ "'"				+ CRLF
	cQuery += " 	AND	RTRIM(LTRIM(SUA.UA_XIDJETC))	<>	''"											+ CRLF
	cQuery += "	AND	SF2.F2_XDESPAC					=	'2'"										+ CRLF
	cQuery += "	AND	RTRIM(LTRIM(SF2.F2_CHVNFE))		<>	''"											+ CRLF

	conout("Selecionando pedidos...")
	TcQuery cQuery New Alias "QRYSUA"
return

//---------------------------------------------------------------------------------
/*/{Protheus.doc} despachaPV

Altera status do pedido no site para despachado (cod. 5720)

<AUTHOR>
@since 22/01/2015
/*/
//---------------------------------------------------------------------------------
static function despachaPV()
	local cUpdate						:= ""
	local cUserWs						:= GetMV("ES_USERJET")
	local cSenhaWs					:= GetMV("ES_PSWDJET")
	local oWSalterOrderStateResult	:= nil

	oWSalterOrderStateCompleteResult := WSWSJET():New()

	if !oWSalterOrderStateCompleteResult:alterOrderStateComplete(cUserWs, cSenhaWs, val(QRYSUA->UA_XIDJETC), 5720, "Alteração via automática (JOB Protheus)")
		conout("Erro ao despachar pedido " + QRYSUA->UA_XIDJETC + " no site!" + CRLF + GetWSCError())

		aadd(aLogDesp, {allTrim(QRYSUA->UA_XIDJETC), "Erro ao despachar pedido"})
	else
		DBSelectArea("PA4")
		PA4->(DBSetOrder(1))
		PA4->(DBGoTop())
		if PA4->(DBSeek(xFilial("PA4") + QRYSUA->UA_NUM))
			RecLock("PA4", .F.)
				PA4->PA4_IDSTAT := "5720"
			PA4->(MSUnLock())
			conout("Status do Pedido: " + allTrim(QRYSUA->UA_XIDJETC) + " foi despachado com sucesso no E-COMMERCE!")

			aadd(aLogDesp, {allTrim(QRYSUA->UA_XIDJETC), "Despachado com sucesso"})

			if !isBlind()
				msgAlert("Status do Pedido: " + allTrim(QRYSUA->UA_XIDJETC) + " foi despachado com sucesso no E-COMMERCE!")
			endif
		endif
		PA4->(DBCloseArea())

		cUpdate := " UPDATE " + retSQLName("SF2")
		cUpdate += " SET F2_XDESPAC = '1'"															+ CRLF
		cUpdate += " WHERE"																			+ CRLF
		cUpdate += " 		D_E_L_E_T_					<>	'*'"								+ CRLF
		cUpdate += " 	AND	F2_FILIAL					=	'" + xFilial("SF2")		+ "'"	+ CRLF
		cUpdate += " 	AND	F2_DOC						=	'" + QRYSUA->F2_DOC		+ "'"	+ CRLF
		cUpdate += " 	AND	F2_SERIE					=	'" + QRYSUA->F2_SERIE	+ "'"	+ CRLF
		cUpdate += " 	AND	F2_CLIENTE					=	'" + QRYSUA->F2_CLIENTE	+ "'"	+ CRLF
		cUpdate += " 	AND	F2_LOJA					=	'" + QRYSUA->F2_LOJA		+ "'"	+ CRLF
		cUpdate += "	AND	F2_XDESPAC					=	'2'"								+ CRLF
		cUpdate += "	AND	RTRIM(LTRIM(F2_CHVNFE))	<>	''"									+ CRLF

		if TCSQLExec(cUpdate) < 0
			conout("Erro na atualização do BD " + CRLF + TCSQLError())
		endif
	endif
return
