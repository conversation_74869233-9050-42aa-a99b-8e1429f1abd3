#Include 'Totvs.ch'

/*/{Protheus.doc} myFunction
(long_description)
@type    Function
<AUTHOR>
@since   25/10/2019
@version version
/*/
User Function LJ720Fim()

    Local aAreaAtu := GetArea()
    Local aAreaSF1 := SF1->(GetArea())
    Local aAreaSD1 := SD1->(GetArea())
    Local aAreaSF2 := SF2->(GetArea())

    Local oDito := Nil
    Local cCpf := ""
    Local cChave := ""
    
    If !Empty(SF1->F1_FILIAL) .And. Alltrim(SF1->F1_TIPO) == "D"
         
        oDito := AFATW016():New()
        cCpf := oDito:getCliCPF(SF1->F1_FORNECE,SF1->F1_LOJA)

        If !Empty(cCpf)

            oDito:GravaEventoDito(cCpf, "SF1", SF1->(Recno()))
            cChave := SF1->F1_FILIAL+SF1->F1_DOC+SF1->F1_SERIE+SF1->F1_FORNECE+SF1->F1_LOJA

            SD1->(DbSetOrder(1)) // D1_FILIAL+D1_DOC+D1_SERIE+D1_FORNECE+D1_LOJA+D1_COD+D1_ITEM
            If SD1->(MsSeek(cChave))
                Do While SD1->(!Eof()) .And. cChave == SD1->D1_FILIAL+SD1->D1_DOC+SD1->D1_SERIE+SD1->D1_FORNECE+SD1->D1_LOJA

                    oDito:GravaEventoDito(cCpf, "SD1", SD1->(Recno()) )

                    SD1->(DbSkip())
                EndDo
            EndIf
        EndIf
	EndIf

    RestArea(aAreaSF2)
    RestArea(aAreaSD1)
    RestArea(aAreaSF1)
    RestArea(aAreaAtu)
Return