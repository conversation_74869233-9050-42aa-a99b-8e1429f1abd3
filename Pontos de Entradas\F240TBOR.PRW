
User Function F240TBOR
/*
ConOut(' ')
ConOut('PE F240TBOR')
ConOut('Log FINA240 Bordero '+SE2->E2_NUMBOR + ' IDCNAB '+SE2->E2_IDCNAB)
ConOut('Log FINA240 em '+Dtoc(DATE())+' - '+Time() + ' Recno SE2-> '+Transform(SE2->(RECNO()),"@E 999,999") )
ConOut('Log FINA240 em '+Dtoc(DATE())+' - '+Time() + ' Recno SEA-> '+Transform(SEA->(RECNO()),"@E 999,999") )
*/
Return

User Function F420CHK
/*
ConOut(' ')
ConOut('PE F420CHK')
ConOut('Log FINA240 Bordero '+SE2->E2_NUMBOR + ' IDCNAB '+SE2->E2_IDCNAB)
ConOut('Log FINA240 em '+Dtoc(DATE())+' - '+Time() + ' Recno SE2-> '+Transform(SE2->(RECNO()),"@E 999,999") )
ConOut('Log FINA240 em '+Dtoc(DATE())+' - '+Time() + ' Recno SEA-> '+Transform(SEA->(RECNO()),"@E 999,999") )
*/
/*
If File( ALLTRIM(MV_PAR04) )
   MsgStop('O arquivo '+ALLTRIM(MV_PAR04)+' ja foi gerado anteriormente')
	Return 1
Endif
*/
Return 1