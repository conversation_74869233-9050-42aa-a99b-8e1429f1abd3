#INCLUDE "PROTHEUS.CH"
#INCLUDE "FIVEWIN.CH"

User Function TMKBARLA(aBtnLat)
Local aAreaAnt	 := GetArea()
Local aAreaSA1	 := SA1->(GetArea())
Local aAreaSUA  := SUA->(GetArea())
Local aSizeAut	 := MsAdvSize(,.F.,400)

IF aSizeAut[5] > 800
	Aadd(aBtnLat,{"DbG06",&("{|| U_XTMKVHI()}" )  ,"Historico de Ligacoes - Sistemaq"} )
Endif

RestArea(aAreaSUA)
RestArea(aAreaSA1)
RestArea(aAreaAnt)

Return(aBtnLat)