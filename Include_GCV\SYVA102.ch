#ifdef SPANISH
	#define STR0001 "Numero do PC"
	#define STR0002 "Data Emissao"
	#define STR0003 "Fornecedor  "
	#define STR0004 "Pedido de Compra com Grade"
	#define STR0005 "Pesquisar"
	#define STR0006 "Visualizar"
	#define STR0007 "Incluir"
	#define STR0008 "Alterar"
	#define STR0009 "Excluir"
	#define STR0010 "Copiar"
	#define STR0011 "Imprimir"
	#define STR0012 "Legenda"
	#define STR0013 "Conhecimento"
	#define STR0014 "Pesquisa"
	#define STR0015 "Atencao"
	#define STR0016 "A Filial Corrente nao e Centralizadora. Para emissao do pedido e necessario logar na filial: "
	#define STR0017 "PC - Inicio do Carregamento: "
	#define STR0018 "Por favor aguarde, carregando..."
	#define STR0019 "PC - Fim do Carregamento: "
	#define STR0020 "Movimentacao de Produto [F4]"
	#define STR0021 "Posicao do Fornecedor [ALT+P]"
	#define STR0022 "Alterar Fornecedor [ALT+Q]"
	#define STR0023 "Fotos do Produto [ALT+F]"
	#define STR0024 "Incluir Produto [ALT+I]"
	#define STR0025 "Alterar Produto [ALT+A]"
	#define STR0026 "Impressao do Pedido [F5]"
	#define STR0027 "Enviar Etiquetas [ALT+E]"
	#define STR0028 "Alocacao/Distribuicao [ALT+L]"
	#define STR0029 "Incluir Grade Padrao [ALT+G]"
	#define STR0030 "Pedido de Compra - Cabecalho: "
	#define STR0031 "Pedido de Compra - Itens"
	#define STR0032 "Numero"
	#define STR0033 "Data de Emissao"
	#define STR0034 "Fornecedor"
	#define STR0035 "Loja"
	#define STR0036 "Cond. Pagto"
	#define STR0037 "Semana"
	#define STR0038 "Atualizando data de entrega"
	#define STR0039 "Contato"
	#define STR0040 "Email"
	#define STR0041 "Comprador"
	#define STR0042 "Local de Entrega"
	#define STR0043 "Qualidade"
	#define STR0044 "Desconto Financeiro"
	#define STR0045 "Frete"
	#define STR0046 "Valor Frete"
	#define STR0047 "Transportadora"
	#define STR0048 "Agendamento"
	#define STR0049 "Peca Piloto"
	#define STR0050 "Consignado"
	#define STR0051 "Aprovador"
	#define STR0052 "Data Aprovacao"
	#define STR0053 "Observacoes"
	#define STR0054 "FILIAIS"
	#define STR0055 "TOTAIS POR FILIAL"
	#define STR0056 "ITENS"
	#define STR0057 "GRADES/PACKS"
	#define STR0058 "DISTRIBUICAO POR FILIAL"
	#define STR0059 "Por favor aguarde, excluindo..."
	#define STR0060 "PC - Inicio da Gravacao: "
	#define STR0061 "PC - Inicio da Gravacao: "
	#define STR0062 "PC - Fim da Gravacao: "
	#define STR0063 "Inclusao"
	#define STR0064 "Alteracao"
	#define STR0065 "Exclusao"
	#define STR0066 "Confirma "
	#define STR0067 " do Pedido de Compra ?"
	#define STR0068 "Pedido de Compra"
	#define STR0069 "PC ---- Executando a100AtuaCols: "
	#define STR0070 "PC ---- Executando a100AtuSC7: "
	#define STR0071 "Produto/SKU nao cadastrado."
	#define STR0072 "Este SKU nao existe ["
	#define STR0073 "] na Filial: "
	#define STR0074 "PC -------- Analisando Itens Entregues: "
	#define STR0075 "PC -------- Excluindo Itens Antigos: "
	#define STR0076 "PC -------- Incluindo Novos Itens: "
	#define STR0077 "Este codigo ja foi informada."
	#define STR0078 "Informe a grade."
	#define STR0079 "Informe a referencia do fornecedor."
	#define STR0080 "Informe as cores."
	#define STR0081 "Informe a data de entrega."
	#define STR0082 "Todas as grades foram deletadas. Nao e possivel confirmar a gravacao do pedido."
	#define STR0083 "Informe o preco unitario de compra."
	#define STR0084 "Informe as quantidades da Grade."
	#define STR0085 "Todas as grades foram deletadas. Nao e possivel confirmar a gravacao do pedido."
	#define STR0086  "Informe a distribuicao da filial."
	#define STR0087 "Filiais"
	#define STR0088 "Nome"
	#define STR0089 "Grade "
	#define STR0090 "Quantidade"
	#define STR0091 "Entregue"
	#define STR0092 "Descontos $"
	#define STR0093 "SubTotal $"
	#define STR0094 "IPI $"
	#define STR0095 "Total $"
	#define STR0096 "Atencao"
	#define STR0097 "Este Item ja foi Entregue. Nao ? possivel altera-lo."
	#define STR0098 "QUANTIDADE TOTAL [ "
	#define STR0099 "JA ENTREGUE [ "
	#define STR0100 "Atencao"
	#define STR0101 "Esta Grade ["
	#define STR0102 "] ja foi Entregue. Nao e possivel altera-la."
	#define STR0103 "Expressão informada é inválida."
	#define STR0104 "Custo Unitario"
	#define STR0105 "Deseja replicar este Valor para?"
	#define STR0106 "Todos"
	#define STR0107 "Sem Custo"
	#define STR0108 "Para Baixo"
	#define STR0109 "So Neste"
	#define STR0110 "Formula: (Preco Venda / Custo)"
	#define STR0111 "Verifique o Markup cadastrado para este Produto ou confira o parametro [MV_01MKPVD] global de Markup."
	#define STR0112 "%] abaixo do Permitido ["
	#define STR0113 "Data de Entrega Invalida."
	#define STR0114 "PC ---- Montando aHeaderSC7: "
	#define STR0115 "PC ---- Montando aColsSC7: "
	#define STR0116 "PC ---- Criando Grades: "
	#define STR0117 "Pedido Pendente"
	#define STR0118 "Pedido Parcialmente Atendido"
	#define STR0119 "Pedido Atendido"
	#define STR0120 "Pedido Bloqueado"
	#define STR0121 "Elim. Residuo"
	#define STR0122 "Pedido Usado em Pre-Nota"
	#define STR0123 "Legenda"
	#define STR0124 "Este pedido nao pode ser excluido, pois existem quantidades entregues. Utilize a rotina Eliminar Residuo."
	#define STR0125 "Este pedido nao pode ser excluido, pois existem quantidades entregues. Utilize a rotina Eliminar Residuo"
	#define STR0126 "Este produto ja existe. Verifique a linha: "
	#define STR0127 "Produto/SKU nao cadastrado."
	#define STR0128 "Filiais"
	#define STR0129 "Nome"
	#define STR0130 "Grd."
	#define STR0131 "Ref"
	#define STR0132 "Referencia"
	#define STR0133 "Cor."
	#define STR0134 "Descricao"
	#define STR0135 "Total"
	#define STR0136 "Custo Final"
	#define STR0137 "Grade "
	#define STR0138 "Quantidade"
	#define STR0139 "Entregue"
	#define STR0140 "Descontos $"
	#define STR0141 "SubTotal $"
	#define STR0142 "Total $"
	#define STR0143 "Este produto ja existe. Verifique a linha: "
	#define STR0144 "Preencha os campos do cabecalho do pedido de compra."
	#define STR0145 "Informe o Multiplicador."
	#define STR0146 "Nova Grade"
	#define STR0147 "Deseja carregar a Grade deste Produto?"
	#define STR0148 "Sim"
	#define STR0149 "Nao"
	#define STR0150 "Data Final de Entrega: "
	#define STR0151 "Atualizar Data de Entrega para todos os itens?"
	#define STR0152 "Sim"
	#define STR0153 "Nao"
	#define STR0154 "Qualidade"
	#define STR0155 
	#define STR0156 
	#define STR0157 
	#define STR0158 "% Desconto: "
	#define STR0159 "Lista de Cores"
	#define STR0160 "Referência"
	#define STR0161 "Cor"
	#define STR0162 "Descrição"
	#define STR0163 "Nao existe cores para esta referencia."
	#define STR0164 "Referência do Produto"
	#define STR0165 "Referência"
	#define STR0166 
	#define STR0167
	#define STR0168 
	#define STR0169 "Descrição"
	#define STR0170 "Nao existem referencias para este produto."
	#define STR0171 "Não existem PACKS para este produto."
	#define STR0172 "Filiais"
	#define STR0173 "Nome"
	#define STR0174 "Quantidade"
	#define STR0175 "Entregue"
	#define STR0176 "Descontos $"
	#define STR0177 "SubTotal $"
	#define STR0178 "Total $"
	#define STR0179 "Informe o armazem do produto."
	#define STR0180 "Informe o preco unitario de venda."
	#define STR0181 "Aprovar"
#else
	#ifdef ENGLISH
		#define STR0001 "Numero do PC"
		#define STR0002 "Data Emissao"
		#define STR0003 "Fornecedor  "
		#define STR0004 "Pedido de Compra com Grade"
		#define STR0005 "Pesquisar"
		#define STR0006 "Visualizar"
		#define STR0007 "Incluir"
		#define STR0008 "Alterar"
		#define STR0009 "Excluir"
		#define STR0010 "Copiar"
		#define STR0011 "Imprimir"
		#define STR0012 "Legenda"
		#define STR0013 "Conhecimento"
		#define STR0014 "Pesquisa"
		#define STR0015 "Atencao"
		#define STR0016 "A Filial Corrente nao e Centralizadora. Para emissao do pedido e necessario logar na filial: "
		#define STR0017 "PC - Inicio do Carregamento: "
		#define STR0018 "Por favor aguarde, carregando..."
		#define STR0019 "PC - Fim do Carregamento: "
		#define STR0020 "Movimentacao de Produto [F4]"
		#define STR0021 "Posicao do Fornecedor [ALT+P]"
		#define STR0022 "Alterar Fornecedor [ALT+Q]"
		#define STR0023 "Fotos do Produto [ALT+F]"
		#define STR0024 "Incluir Produto [ALT+I]"
		#define STR0025 "Alterar Produto [ALT+A]"
		#define STR0026 "Impressao do Pedido [F5]"
		#define STR0027 "Enviar Etiquetas [ALT+E]"
		#define STR0028 "Alocacao/Distribuicao [ALT+L]"
		#define STR0029 "Incluir Grade Padrao [ALT+G]"
		#define STR0030 "Pedido de Compra - Cabecalho: "
		#define STR0031 "Pedido de Compra - Itens"
		#define STR0032 "Numero"
		#define STR0033 "Data de Emissao"
		#define STR0034 "Fornecedor"
		#define STR0035 "Loja"
		#define STR0036 "Cond. Pagto"
		#define STR0037 "Semana"
		#define STR0038 "Atualizando data de entrega"
		#define STR0039 "Contato"
		#define STR0040 "Email"
		#define STR0041 "Comprador"
		#define STR0042 "Local de Entrega"
		#define STR0043 "Qualidade"
		#define STR0044 "Desconto Financeiro"
		#define STR0045 "Frete"
		#define STR0046 "Valor Frete"
		#define STR0047 "Transportadora"
		#define STR0048 "Agendamento"
		#define STR0049 "Peca Piloto"
		#define STR0050 "Consignado"
		#define STR0051 "Aprovador"
		#define STR0052 "Data Aprovacao"
		#define STR0053 "Observacoes"
		#define STR0054 "FILIAIS"
		#define STR0055 "TOTAIS POR FILIAL"
		#define STR0056 "ITENS"
		#define STR0057 "GRADES/PACKS"
		#define STR0058 "DISTRIBUICAO POR FILIAL"
		#define STR0059 "Por favor aguarde, excluindo..."
		#define STR0060 "PC - Inicio da Gravacao: "
		#define STR0061 "PC - Inicio da Gravacao: "
		#define STR0062 "PC - Fim da Gravacao: "
		#define STR0063 "Inclusao"
		#define STR0064 "Alteracao"
		#define STR0065 "Exclusao"
		#define STR0066 "Confirma "
		#define STR0067 " do Pedido de Compra ?"
		#define STR0068 "Pedido de Compra"
		#define STR0069 "PC ---- Executando a100AtuaCols: "
		#define STR0070 "PC ---- Executando a100AtuSC7: "
		#define STR0071 "Produto/SKU nao cadastrado."
		#define STR0072 "Este SKU nao existe ["
		#define STR0073 "] na Filial: "
		#define STR0074 "PC -------- Analisando Itens Entregues: "
		#define STR0075 "PC -------- Excluindo Itens Antigos: "
		#define STR0076 "PC -------- Incluindo Novos Itens: "
		#define STR0077 "Este codigo ja foi informada."
		#define STR0078 "Informe a grade."
		#define STR0079 "Informe a referencia do fornecedor."
		#define STR0080 "Informe as cores."
		#define STR0081 "Informe a data de entrega."
		#define STR0082 "Todas as grades foram deletadas. Nao e possivel confirmar a gravacao do pedido."
		#define STR0083 "Informe o preco unitario de compra."
		#define STR0084 "Informe as quantidades da Grade."
		#define STR0085 "Todas as grades foram deletadas. Nao e possivel confirmar a gravacao do pedido."
		#define STR0086  "Informe a distribuicao da filial."
		#define STR0087 "Filiais"
		#define STR0088 "Nome"
		#define STR0089 "Grade "
		#define STR0090 "Quantidade"
		#define STR0091 "Entregue"
		#define STR0092 "Descontos $"
		#define STR0093 "SubTotal $"
		#define STR0094 "IPI $"
		#define STR0095 "Total $"
		#define STR0096 "Atencao"
		#define STR0097 "Este Item ja foi Entregue. Nao ? possivel altera-lo."
		#define STR0098 "QUANTIDADE TOTAL [ "
		#define STR0099 "JA ENTREGUE [ "
		#define STR0100 "Atencao"
		#define STR0101 "Esta Grade ["
		#define STR0102 "] ja foi Entregue. Nao e possivel altera-la."
		#define STR0103 "Expressão informada é inválida."
		#define STR0104 "Custo Unitario"
		#define STR0105 "Deseja replicar este Valor para?"
		#define STR0106 "Todos"
		#define STR0107 "Sem Custo"
		#define STR0108 "Para Baixo"
		#define STR0109 "So Neste"
		#define STR0110 "Formula: (Preco Venda / Custo)"
		#define STR0111 "Verifique o Markup cadastrado para este Produto ou confira o parametro [MV_01MKPVD] global de Markup."
		#define STR0112 "%] abaixo do Permitido ["
		#define STR0113 "Data de Entrega Invalida."
		#define STR0114 "PC ---- Montando aHeaderSC7: "
		#define STR0115 "PC ---- Montando aColsSC7: "
		#define STR0116 "PC ---- Criando Grades: "
		#define STR0117 "Pedido Pendente"
		#define STR0118 "Pedido Parcialmente Atendido"
		#define STR0119 "Pedido Atendido"
		#define STR0120 "Pedido Bloqueado"
		#define STR0121 "Elim. Residuo"
		#define STR0122 "Pedido Usado em Pre-Nota"
		#define STR0123 "Legenda"
		#define STR0124 "Este pedido nao pode ser excluido, pois existem quantidades entregues. Utilize a rotina Eliminar Residuo."
		#define STR0125 "Este pedido nao pode ser excluido, pois existem quantidades entregues. Utilize a rotina Eliminar Residuo"
		#define STR0126 "Este produto ja existe. Verifique a linha: "
		#define STR0127 "Produto/SKU nao cadastrado."
		#define STR0128 "Filiais"
		#define STR0129 "Nome"
		#define STR0130 "Grd."
		#define STR0131 "Ref"
		#define STR0132 "Referencia"
		#define STR0133 "Cor."
		#define STR0134 "Descricao"
		#define STR0135 "Total"
		#define STR0136 "Custo Final"
		#define STR0137 "Grade "
		#define STR0138 "Quantidade"
		#define STR0139 "Entregue"
		#define STR0140 "Descontos $"
		#define STR0141 "SubTotal $"
		#define STR0142 "Total $"
		#define STR0143 "Este produto ja existe. Verifique a linha: "
		#define STR0144 "Preencha os campos do cabecalho do pedido de compra."
		#define STR0145 "Informe o Multiplicador."
		#define STR0146 "Nova Grade"
		#define STR0147 "Deseja carregar a Grade deste Produto?"
		#define STR0148 "Sim"
		#define STR0149 "Nao"
		#define STR0150 "Data Final de Entrega: "
		#define STR0151 "Atualizar Data de Entrega para todos os itens?"
		#define STR0152 "Sim"
		#define STR0153 "Nao"
		#define STR0154 "Qualidade"
		#define STR0155 
		#define STR0156 
		#define STR0157 
		#define STR0158 "% Desconto: "
		#define STR0159 "Lista de Cores"
		#define STR0160 "Referência"
		#define STR0161 "Cor"
		#define STR0162 "Descrição"
		#define STR0163 "Nao existe cores para esta referencia."
		#define STR0164 "Referência do Produto"
		#define STR0165 "Referência"
		#define STR0166 
		#define STR0167
		#define STR0168 
		#define STR0169 "Descrição"
		#define STR0170 "Nao existem referencias para este produto."
		#define STR0171 "Não existem PACKS para este produto."
		#define STR0172 "Filiais"
		#define STR0173 "Nome"
		#define STR0174 "Quantidade"
		#define STR0175 "Entregue"
		#define STR0176 "Descontos $"
		#define STR0177 "SubTotal $"
		#define STR0178 "Total $"
		#define STR0179 "Informe o armazem do produto."
		#define STR0180 "Informe o preco unitario de venda."
		#define STR0181 "Aprovar"
	#else
		#define STR0001 "Numero do PC"
		#define STR0002 "Data Emissao"
		#define STR0003 "Fornecedor  "
		#define STR0004 "Pedido de Compra com Grade"
		#define STR0005 "Pesquisar"
		#define STR0006 "Visualizar"
		#define STR0007 "Incluir"
		#define STR0008 "Alterar"
		#define STR0009 "Excluir"
		#define STR0010 "Copiar"
		#define STR0011 "Imprimir"
		#define STR0012 "Legenda"
		#define STR0013 "Conhecimento"
		#define STR0014 "Pesquisa"
		#define STR0015 "Atencao"
		#define STR0016 "A Filial Corrente nao e Centralizadora. Para emissao do pedido e necessario logar na filial: "
		#define STR0017 "PC - Inicio do Carregamento: "
		#define STR0018 "Por favor aguarde, carregando..."
		#define STR0019 "PC - Fim do Carregamento: "
		#define STR0020 "Movimentacao de Produto [F4]"
		#define STR0021 "Posicao do Fornecedor [ALT+P]"
		#define STR0022 "Alterar Fornecedor [ALT+Q]"
		#define STR0023 "Fotos do Produto [ALT+F]"
		#define STR0024 "Incluir Produto [ALT+I]"
		#define STR0025 "Alterar Produto [ALT+A]"
		#define STR0026 "Impressao do Pedido [F5]"
		#define STR0027 "Enviar Etiquetas [ALT+E]"
		#define STR0028 "Alocacao/Distribuicao [ALT+L]"
		#define STR0029 "Incluir Grade Padrao [ALT+G]"
		#define STR0030 "Pedido de Compra - Cabecalho: "
		#define STR0031 "Pedido de Compra - Itens"
		#define STR0032 "Numero"
		#define STR0033 "Data de Emissao"
		#define STR0034 "Fornecedor"
		#define STR0035 "Loja"
		#define STR0036 "Cond. Pagto"
		#define STR0037 "Semana"
		#define STR0038 "Atualizando data de entrega"
		#define STR0039 "Contato"
		#define STR0040 "Email"
		#define STR0041 "Comprador"
		#define STR0042 "Local de Entrega"
		#define STR0043 "Qualidade"
		#define STR0044 "Desconto Financeiro"
		#define STR0045 "Frete"
		#define STR0046 "Valor Frete"
		#define STR0047 "Transportadora"
		#define STR0048 "Agendamento"
		#define STR0049 "Peca Piloto"
		#define STR0050 "Consignado"
		#define STR0051 "Aprovador"
		#define STR0052 "Data Aprovacao"
		#define STR0053 "Observacoes"
		#define STR0054 "FILIAIS"
		#define STR0055 "TOTAIS POR FILIAL"
		#define STR0056 "ITENS"
		#define STR0057 "GRADES/PACKS"
		#define STR0058 "DISTRIBUICAO POR FILIAL"
		#define STR0059 "Por favor aguarde, excluindo..."
		#define STR0060 "PC - Inicio da Gravacao: "
		#define STR0061 "PC - Inicio da Gravacao: "
		#define STR0062 "PC - Fim da Gravacao: "
		#define STR0063 "Inclusao"
		#define STR0064 "Alteracao"
		#define STR0065 "Exclusao"
		#define STR0066 "Confirma "
		#define STR0067 " do Pedido de Compra ?"
		#define STR0068 "Pedido de Compra"
		#define STR0069 "PC ---- Executando a100AtuaCols: "
		#define STR0070 "PC ---- Executando a100AtuSC7: "
		#define STR0071 "Produto/SKU nao cadastrado."
		#define STR0072 "Este SKU nao existe ["
		#define STR0073 "] na Filial: "
		#define STR0074 "PC -------- Analisando Itens Entregues: "
		#define STR0075 "PC -------- Excluindo Itens Antigos: "
		#define STR0076 "PC -------- Incluindo Novos Itens: "
		#define STR0077 "Este codigo ja foi informada."
		#define STR0078 "Informe a grade."
		#define STR0079 "Informe a referencia do fornecedor."
		#define STR0080 "Informe as cores."
		#define STR0081 "Informe a data de entrega."
		#define STR0082 "Todas as grades foram deletadas. Nao e possivel confirmar a gravacao do pedido."
		#define STR0083 "Informe o preco unitario de compra."
		#define STR0084 "Informe as quantidades da Grade."
		#define STR0085 "Todas as grades foram deletadas. Nao e possivel confirmar a gravacao do pedido."
		#define STR0086  "Informe a distribuicao da filial."
		#define STR0087 "Filiais"
		#define STR0088 "Nome"
		#define STR0089 "Grade "
		#define STR0090 "Quantidade"
		#define STR0091 "Entregue"
		#define STR0092 "Descontos $"
		#define STR0093 "SubTotal $"
		#define STR0094 "IPI $"
		#define STR0095 "Total $"
		#define STR0096 "Atencao"
		#define STR0097 "Este Item ja foi Entregue. Nao ? possivel altera-lo."
		#define STR0098 "QUANTIDADE TOTAL [ "
		#define STR0099 "JA ENTREGUE [ "
		#define STR0100 "Atencao"
		#define STR0101 "Esta Grade ["
		#define STR0102 "] ja foi Entregue. Nao e possivel altera-la."
		#define STR0103 "Expressão informada é inválida."
		#define STR0104 "Custo Unitario"
		#define STR0105 "Deseja replicar este Valor para?"
		#define STR0106 "Todos"
		#define STR0107 "Sem Custo"
		#define STR0108 "Para Baixo"
		#define STR0109 "So Neste"
		#define STR0110 "Formula: (Preco Venda / Custo)"
		#define STR0111 "Verifique o Markup cadastrado para este Produto ou confira o parametro [MV_01MKPVD] global de Markup."
		#define STR0112 "%] abaixo do Permitido ["
		#define STR0113 "Data de Entrega Invalida."
		#define STR0114 "PC ---- Montando aHeaderSC7: "
		#define STR0115 "PC ---- Montando aColsSC7: "
		#define STR0116 "PC ---- Criando Grades: "
		#define STR0117 "Pedido Pendente"
		#define STR0118 "Pedido Parcialmente Atendido"
		#define STR0119 "Pedido Atendido"
		#define STR0120 "Pedido Bloqueado"
		#define STR0121 "Elim. Residuo"
		#define STR0122 "Pedido Usado em Pre-Nota"
		#define STR0123 "Legenda"
		#define STR0124 "Este pedido nao pode ser excluido, pois existem quantidades entregues. Utilize a rotina Eliminar Residuo."
		#define STR0125 "Este pedido nao pode ser excluido, pois existem quantidades entregues. Utilize a rotina Eliminar Residuo"
		#define STR0126 "Este produto ja existe. Verifique a linha: "
		#define STR0127 "Produto/SKU nao cadastrado."
		#define STR0128 "Filiais"
		#define STR0129 "Nome"
		#define STR0130 "Grd."
		#define STR0131 "Ref"
		#define STR0132 "Referencia"
		#define STR0133 "Cor."
		#define STR0134 "Descricao"
		#define STR0135 "Total"
		#define STR0136 "Custo Final"
		#define STR0137 "Grade "
		#define STR0138 "Quantidade"
		#define STR0139 "Entregue"
		#define STR0140 "Descontos $"
		#define STR0141 "SubTotal $"
		#define STR0142 "Total $"
		#define STR0143 "Este produto ja existe. Verifique a linha: "
		#define STR0144 "Preencha os campos do cabecalho do pedido de compra."
		#define STR0145 "Informe o Multiplicador."
		#define STR0146 "Nova Grade"
		#define STR0147 "Deseja carregar a Grade deste Produto?"
		#define STR0148 "Sim"
		#define STR0149 "Nao"
		#define STR0150 "Data Final de Entrega: "
		#define STR0151 "Atualizar Data de Entrega para todos os itens?"
		#define STR0152 "Sim"
		#define STR0153 "Nao"
		#define STR0154 "Qualidade"
		#define STR0155 
		#define STR0156 
		#define STR0157 
		#define STR0158 "% Desconto: "
		#define STR0159 "Lista de Cores"
		#define STR0160 "Referência"
		#define STR0161 "Cor"
		#define STR0162 "Descrição"
		#define STR0163 "Nao existe cores para esta referencia."
		#define STR0164 "Referência do Produto"
		#define STR0165 "Referência"
		#define STR0166 
		#define STR0167
		#define STR0168 
		#define STR0169 "Descrição"
		#define STR0170 "Nao existem referencias para este produto."
		#define STR0171 "Não existem PACKS para este produto."
		#define STR0172 "Filiais"
		#define STR0173 "Nome"
		#define STR0174 "Quantidade"
		#define STR0175 "Entregue"
		#define STR0176 "Descontos $"
		#define STR0177 "SubTotal $"
		#define STR0178 "Total $"
		#define STR0179 "Informe o armazem do produto."
		#define STR0180 "Informe o preco unitario de venda."
		#define STR0181 "Aprovar"
		#define STR0182 "O fornecedor do produto e diferente do informado no pedido. Por favor, verifique!"
	#endif
#endif
