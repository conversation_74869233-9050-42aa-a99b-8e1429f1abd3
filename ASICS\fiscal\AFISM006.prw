#INCLUDE "Protheus.CH"   
#INCLUDE "TopConn.CH"

/*
Nome: AFISM006
Rotina atualizar os campos referente ao processo da VAF nos clientes e fornecedores do estado de minas gerais.
Autor: <PERSON> (Symm Consultoria)
12/11/2015
Uso: Clean-UP -  ASICS
*/

User Function AFISM006
      
PROCESSA({|| ProcForn() }, "Processando fornecedores...",,.T.)
PROCESSA({|| ProcClie() }, "Processando clientes...",,.T.)

MSGInfo("Rotina processada com sucesso.") 

Return    

/*
Nome: ProcClie
Rotina atualizar os clientes do estado de Minas Gerais para a VAF.
Autor: <PERSON> (Symm Consultoria)
12/11/2015
Uso: Clean-UP -  ASICS
*/
Static Function ProcClie()
      
Local cQry := ""

cQry := "SELECT A1_COD, A1_LOJA, A1_COD_MUN, A1_EST FROM "+RetSqlName("SA1")+" SA1 WHERE D_E_L_E_T_ = '' AND A1_EST = 'MG' ORDER BY A1_COD"

TCQUERY cQry NEW ALIAS "TMPA1"

dbSelectArea("SA1") 
SA1->(dbSetOrder(1))       
                        
While TMPA1->(!Eof())    
                      
	If SA1->(dbSeek(XFilial("SA1")+TMPA1->A1_COD+TMPA1->A1_LOJA))
   
		IncProc("Processando o cliente: "+TMPA1->A1_COD+TMPA1->A1_LOJA+" ...")    
	    
	    RecLock("SA1", .F.)
	    SA1->A1_COD_VAF := Posicione("CC2",1,XFilial("CC2")+SA1->A1_EST+SA1->A1_COD_MUN, "CC2_XCDVAF") 
	    SA1->(MsUnLock()) 
	
	EndIf 

	TMPA1->(dbSkip())
EndDo  

TMPA1->(dbCloseArea())

Return     
 
/*
Nome: ProcForn
Rotina atualizar os fornecedores do estado de Minas Gerais para a VAF.
Autor: Robson Oliveira (Symm Consultoria)
12/11/2015
Uso: Clean-UP -  ASICS
*/
Static Function ProcForn()
      
Local cQry := ""

cQry := "SELECT A2_COD, A2_LOJA, A2_COD_MUN, A2_EST FROM "+RetSqlName("SA2")+" SA2 WHERE D_E_L_E_T_ = '' AND A2_EST = 'MG' ORDER BY A2_COD"

TCQUERY cQry NEW ALIAS "TMPA2"
       
dbSelectArea("SA2") 
SA2->(dbSetOrder(1))       
                     
While TMPA2->(!Eof())
    
	If SA2->(dbSeek(XFilial("SA2")+TMPA2->A2_COD+TMPA2->A2_LOJA))
	
		IncProc("Processando o cliente: "+TMPA2->A2_COD+TMPA2->A2_LOJA+" ...")    
	    
	    RecLock("SA2", .F.)
	    SA2->A2_COD_VAF := Posicione("CC2",1,XFilial("CC2")+SA2->A2_EST+SA2->A2_COD_MUN, "CC2_XCDVAF") 
	    SA2->(MsUnLock())  
	    
	 EndIf

	TMPA2->(dbSkip())
EndDo  

TMPA2->(dbCloseArea())

Return