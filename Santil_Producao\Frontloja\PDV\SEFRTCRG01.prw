#Include "Protheus.ch"
#Include "fileio.ch"
/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  SEFRTCRG01  ºAutor ³Leonardo Espinosa   º Data ³  15-09-2016 º±±                       
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍº±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ jOB para importação de cargas incrementais automaticamente º±± 
±±º          ³ Rotina: SEFRTC01                                           º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ FrontLoja							                      º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function SEFRTCRG01(_cEmp,_cFil)

Local cDir := GetSrvProfString("RootPath"," ")+"\carga\log\"
Local lDir := .T.

RPCSetType(3)
RPCSetEnv(_cEmp, _cFil, ,,,,{})
Conout('Carga Incremental Automatica ---> Iniciado ambiente na empresa ' +_cEmp + ' Filial ' + _cFil)

/*==============================================================================+
| Verifica qual filial e PDV está sendo efetuada a carga incremental			|
+==============================================================================*/
cCompany 		:= GetMv('MV_LJILLCO') //Empresa
cBranch 		:= GetMv('MV_LJILLBR') //Filial
cEnvironment 	:= GetMv('MV_LJILLEN') //Ambiente
cLocation  		:= GetMv('MV_LJILLIP') //IP
cPort 			:= GetMv('MV_LJILLPO') //Porta

// Opcional
// array com a lista de codigos de grupos das cargas inteiras para atualizar.
aEntireLoad		:= {}

//-- Informa no console o inicio do processo de carga.
Conout('+=========== Inicio do Processo de Download de Cargas Incrementais ==========+')
Conout('| Empresa:  ' + cCompany	+ "  											 |")													
Conout('| Filial :  ' + cBranch		+ "  											 |")
Conout('| Ambiente: ' + cEnvironment+ "  											 |")
Conout('| IP:       ' + cLocation	+ "  											 |")	
Conout('| Porta:    ' + cPort		+ "  											 |")
Conout('+============================================================================+')

LOJA1157Job( cLocation, Val(cPort), AllTrim(cEnvironment), cCompany, cBranch, .T., .T., .F., .T., .T., aEntireLoad ) 

//-- Gera Log da Carga
FRTCRG01A(cDir, @lDir)

//-- Verifica arquivos de log antigos e apaga
If lDir
	FRTCRG01B(cDir)
EndIf
Conout('Finalizado Processo de Carga no Ambiente')

Return

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Gera arquivo de Log	da carga.									      ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
Static Function FRTCRG01A(cDir, lDir)
Local cArq := "carga"+DtoS( DATE() )+".log"
Local nHandle

If !ExistDir(cDir)
	If !FWMakeDir(cDir, .F.)
		Conout("Ocorreu um erro na criação da pasta de logs da carga.")
		lDir := .F.
		Return
	EndIf
EndIf

nHandle := IIF(!File(cDir+cArq),FCreate(cDir+cArq),FOpen(cDir+cArq))

If nHandle < 0
	Conout("Erro durante a criacao ou abertura do arquivo log de carga.")
	Conout("Erro: "+STR( FERROR() ) )

Else
	FSeek(nHandle, 0, FS_END)         // Posiciona no fim do arquivo
	FWrite(nHandle, "'Carga Incremental Automatica ---> Finalizado processo "+DtoS( DATE() )+" "+TIME()+ CHR(13) + CHR(13) )
	FClose(nHandle)

EndIf

Return

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Apaga os arquivos de logs mais antigos.							      ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
Static Function FRTCRG01B(cDir)
Local cAtrib 			:= " "
Local lCaseSensitive 	:= .F.
Local nTypeOrder 		:= 2
Local aRet				:= {}
Local nDaysLim			:= SuperGetMv("SE_DMAXLOG",,30)

aRet := Directory( cDir+"*.log", cAtrib, Nil, lCaseSensitive, nTypeOrder )

For i := 0 to Len(aRet)
	If aRet[i][03] > ( DaySub(DATE(), nDaysLim ))
		If FErase(cDir+aRet[i][01]) < 0
			Conout("Erro na exclusão do arquivo de log "+aRet[i][01])
			Conout(STR( FERROR() ))
		Else
			Conout("Arquivo de Log "+aRet[i][01]+" excluido.")
		EndIf
	EndIf
Next i

Return