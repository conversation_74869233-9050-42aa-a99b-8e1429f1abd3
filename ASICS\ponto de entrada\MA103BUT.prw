#Include "Protheus.ch"

/*
???????????????????????????????????????
???????????????????????????????????????
???????????????????????????????????????
??rograma  ?MA103BUT ?Autor ? Alexandro Dias    ?Data ? 03/03/06    ??
???????????????????????????????????????
??escricao ?Cria botao e tecla F11 para selecao do pedido de compra.    ??
???????????????????????????????????????
??lteracoes ?Autor 			?Data 	  ? Motivo		   				   ??
???????????????????????????????????????
??		  ?Douglas Telles	?8/10/15 ?Change 432					   ??
???????????????????????????????????????
??		  ?				?	  ?	    					   ??
???????????????????????????????????????
???????????????????????????????????????
???????????????????????????????????????
*/

User Function MA103BUT()

Local aButtons 	 := {}
Local l005Clasif := IF( Type('l103Class') <> 'U' , l103Class , .F. )

SetKey( VK_F12 , Nil 				 )
SetKey( VK_F3  , { || U_ACOMM002() } ) //"Abre Nota[F3]"											
SetKey( VK_F4  , { || U_ACOMM003() } ) //"Abre Boleto[F4]"	

IF l005Clasif
	SetKey( VK_F12 , { || U_MT103TOP() } )
	Aadd(aButtons,{"PROJETPMS",{|| U_MT103TOP() } , "Definir Tipo de Opera?o [F12]" } )
EndIF

AAdd( aButtons, { "Abre Nota[F3]"  		, { || U_ACOMM002() }, "Abre Nota[F3]" 			} )
AAdd( aButtons, { "Abre Boleto[F4]"		, { || U_ACOMM003() }, "Abre Boleto[F4]" 		} )

//CHG0042377 - Leonardo
AAdd( aButtons, { "Prod. FedEx", { ||MsgRun("Reenviando cadastros...", "Produtos Fedex", {||  U_ACOMM042() }) }, "Prod. FedEx" 	} )

Return(aButtons)
