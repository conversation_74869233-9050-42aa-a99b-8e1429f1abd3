#Include "Totvs.ch"

/*==========================================================================
 Funcao...........:	SIGATMK
 Descricao........:	Ponto de entrada na abertura do SIGATMK
 					(Call Center - Filtro para vendedores)
 Autor............:	Amedeo D. Paoli 	
 Data.............:	15/05/2015
 Parametros.......:	Nil
 Retorno..........:	Nil
==========================================================================*/
User Function SIGATMK
	Local cUsrNoFil	:= SuperGetMV( "ST_NOFILTE", Nil, "000000" )
	Local cUserAtu	:= __cUserID
	Local lVendedor	:= .F.
	Local lSupervi	:= .F.
	Local cCodVen	:= ""
	Local nRecSA3	:= 0
	
	If cUserAtu $ cUsrNoFil	//Parametro com usuarios que nao passam por filtro
		Return .T.
	EndIf               

	//Posiciona em operadores
	DbSelectArea("SU7")             
	SU7->( DbSetOrder(4) )
	If !SU7->( DbSeek(xFilial("SU7") + cUserAtu ) )
		Return .T.
	Else
		
		//Caso seja so Telemarketing ou Telecobranca nao executa filtro
		If SU7->U7_TIPOATE $ ("1/3")
			Return .T.
		Else
			
			//Verifica se e vendedor
			If !Empty( SU7->U7_CODVEN )
				lVendedor	:= .T.
				
				//Posiciona Vendedor
				DbSelectarea("SA3")
				SA3->( DbSetorder(1) )
				SA3->( DbSeek(xFilial("SA3") + SU7->U7_CODVEN) )
				
				cCodVen		:= SA3->A3_COD
				nRecSA3		:= SA3->( Recno() )
				
			EndIf
			
			//Verifica se e Supervisor
			If SU7->U7_TIPO == "2"
				lSupervi	:= .T.
			EndIf
			
		Endif
	
	EndIf
	
	//Cria Filtros
	If lVendedor .And. !lSupervi
		FiltroVend(	cCodVen, nRecSA3 )
	Else
		Return .T.
	EndIf

/*==========================================================================
 Funcao...........:	FiltroVend
 Descricao........:	Funcao que executa filtros em tabelas conforme
 					Vendedor logado
 Autor............:	Amedeo D. Paoli 	
 Data.............:	15/05/2015
 Parametros.......:	Nil
 Retorno..........:	Nil
==========================================================================*/
Static Function FiltroVend( cVendedor, nRecSA3 )
	Local aAreaAT		:= GetArea()
	Public __cVenFil	:= cVendedor
	
	//Filtra Vendedores
	//Removido filtro de Vendedor
	//DbSelectArea("SA3")
	//SA3->( DbSetFilter( { || A3_COD $ __cVenFil }	, "A3_COD $ __cVenFil" ) )
	//SA3->( DbGoTo( nRecSA3 ) )
	
	//Filtra Clientes
	//Removido filtro do cliente
	//DbSelectArea("SA1")       
	//SA1->( DbSetFilter( { || A1_VEND $ __cVenFil }	, "A1_VEND $ __cVenFil .Or. Empty(A1_VEND)" ) )
	
	//Filtra pedidos de Venda
	DbSelectArea("SC5")
	SC5->( DbSetFilter( { || C5_VEND1 $ __cVenFil }	, "C5_VEND1 $ __cVenFil" ) )
	
	//Filtra atendimentos
	DbSelectArea("SUA")
	SUA->( DbSetFilter( { || UA_VEND $ __cVenFil }	, "UA_VEND $ __cVenFil" ) )

	RestArea( aAreaAT )
	
Return Nil
