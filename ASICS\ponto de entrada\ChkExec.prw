#Include "Protheus.ch"
#Include "TopConn.ch"

/*
Funcao      : ChkExec
Objetivos   : Grava tabela de log de rotina
Autor       : rubens simi
Data/Hora   : 14/04/21
*/
User Function ChkExec() 
    Local lRet        := .T.
    Local cRotina     := substr(paramixb,1,len(paramixb)-2)
    Local cCodUser     :=retCodUsr()
    Local cChave      :=xfilial('ZBI')+cCodUser+PADR(alltrim(cRotina),TamSx3("ZBI_FUNCAO")[1])+cModulo

    dbSelectArea('ZBI')
    DBSetOrder(1)//ZBI_FILIAL+ZBI_CODUSR+ZBI_FUNCAO+ZBI_MODULO
    if !DBSeek(cChave)
        RecLock("ZBI",.T.)
        ZBI->ZBI_FILIAL :=xfilial('ZBI')
        ZBI->ZBI_ULTDT  := DATE()
        ZBI->ZBI_HR     := TIME()
        ZBI->ZBI_CODUSR := cCodUser
        ZBI->ZBI_USER   := cUsername
        ZBI->ZBI_PRIDT  := DATE()
        ZBI->ZBI_VEZES  := 1
        ZBI->ZBI_MODULO :=cModulo
        ZBI->ZBI_FUNCAO :=cRotina
        MsUnLock("ZBI")
    Else
        RecLock("ZBI",.F.)
        ZBI->ZBI_ULTDT  := DATE()
        ZBI->ZBI_HR     := TIME()
        ZBI->ZBI_VEZES  := ZBI->ZBI_VEZES+1
        MsUnLock("ZBI")
    EndIF
Return lRet


