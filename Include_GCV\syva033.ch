#ifdef SPANISH
	#define STR0001 "Cobrancas de Royalties e Fundo de Promocao"
	#define STR0002 "Ticket"
	#define STR0003 "Emissao"
	#define STR0004 "Tipo"
	#define STR0005 "Produto"
	#define STR0006 "Descricao"
	#define STR0007 "Fornecedor"
	#define STR0008 "Loj.Forn."
	#define STR0009 "Qtd."
	#define STR0010 "Prc.Venda"
	#define STR0011 "Vlr.Total"
	#define STR0012 "Custo"
	#define STR0013 "Custo Total"
	#define STR0014 "% Royalties"
	#define STR0015 "Vlr.Royalties"
	#define STR0016 "% Fund.Prom."
	#define STR0017 "Vlr.Fund.Prom."
	#define STR0018 "Reembolso"
	#define STR0019 "Filial Orig."
	#define STR0020 "Cabecalho"
	#define STR0021 "Selecione os Royalties e Creditos"
	#define STR0022 "Totais"
	#define STR0023 "Royalties/Fundos"
	#define STR0024 "Credito Defeitos"
	#define STR0025 "ROYALTIES/FUNDO"
	#define STR0026 "DEFEITOS"
	#define STR0027 "SUB-TOTAL"
	#define STR0028 "% DESCONTO"
	#define STR0029 "VLR. DESCONTO"
	#define STR0030 "TOTAL"
	#define STR0031 "Aguarde, Gravando Dados..."
	#define STR0032 "Aguarde, Excluindo Dados..."
	#define STR0033 "Atencao"
	#define STR0034 "Nao e possivel alterar ou excluir cobranca faturada. NF"
	#define STR0035 "Nao e possivel alterar ou excluir cobranca aprovada. Pedido"
	#define STR0036 "Nao e possivel alterar ou excluir cobranca compensada."
	#define STR0037 "Atencao"
	#define STR0038 "Necessario selecionar, no minimo, um registro de Royalties."
	#define STR0039 "Atencao"
	#define STR0040 "O valor final da cobranca nao pode ficar negativo."
	#define STR0041 "Atencao"
	#define STR0042 "O numero da cobranca foi alterado para"
	#define STR0043 "pois o anterior estava em uso."
	#define STR0044 "Com Uso"
	#define STR0045 "Sem Uso"
	#define STR0046 "Pesquisar"
	#define STR0047 "Visualizar"
	#define STR0048 "Incluir"
	#define STR0049 "Alterar"
	#define STR0050 "Excluir"
	#define STR0051 "Aprovar"
	#define STR0052 "Imprime Ficha"
	#define STR0053 "Legenda"
	#define STR0054 "Pendente"
	#define STR0055 "Aprovado"
	#define STR0056 "Compensado sem Fatura"
	#define STR0057 "Faturado"
	#define STR0058 "Legenda"
	#define STR0059 "Atencao"
	#define STR0060 "Nao foi possivel localizar o cliente da filial logada."
	#define STR0061 "Atencao"
	#define STR0062 "Esta cobranca ja foi aprovada."
	#define STR0063 "Produto"
	#define STR0064 "Tipo de Saida"
	#define STR0065 "Informe os Parametros"
	#define STR0066 "Aguarde, aprovando cobranca..."
	#define STR0067 "Atencao"
	#define STR0068 "Esta cobranca tem valor liquido igual a zero, ao confirmar ela sera 'compensada sem fatura'. "
	#define STR0069 "Deseja continuar?"
	#define STR0070 Sim"
	#define STR0071 "Nao"
	#define STR0072 "SERVICOS PRESTADOS A FRANQUIA"
	#define STR0073 "FICHA DE COBRANCA"
	#define STR0074 "Por favor, aguarde... Carregando Royalties..."
	#define STR0075 "Por favor aguarde, carregando os tickets de defeitos..."
#else
	#ifdef ENGLISH
		#define STR0001 "Cobrancas de Royalties e Fundo de Promocao"
		#define STR0002 "Ticket"
		#define STR0003 "Emissao"
		#define STR0004 "Tipo"
		#define STR0005 "Produto"
		#define STR0006 "Descricao"
		#define STR0007 "Fornecedor"
		#define STR0008 "Loj.Forn."
		#define STR0009 "Qtd."
		#define STR0010 "Prc.Venda"
		#define STR0011 "Vlr.Total"
		#define STR0012 "Custo"
		#define STR0013 "Custo Total"
		#define STR0014 "% Royalties"
		#define STR0015 "Vlr.Royalties"
		#define STR0016 "% Fund.Prom."
		#define STR0017 "Vlr.Fund.Prom."
		#define STR0018 "Reembolso"
		#define STR0019 "Filial Orig."
		#define STR0020 "Cabecalho"
		#define STR0021 "Selecione os Royalties e Creditos"
		#define STR0022 "Totais"
		#define STR0023 "Royalties/Fundos"
		#define STR0024 "Credito Defeitos"
		#define STR0025 "ROYALTIES/FUNDO"
		#define STR0026 "DEFEITOS"
		#define STR0027 "SUB-TOTAL"
		#define STR0028 "% DESCONTO"
		#define STR0029 "VLR. DESCONTO"
		#define STR0030 "TOTAL"
		#define STR0031 "Aguarde, Gravando Dados..."
		#define STR0032 "Aguarde, Excluindo Dados..."
		#define STR0033 "Atencao"
		#define STR0034 "Nao e possivel alterar ou excluir cobranca faturada. NF"
		#define STR0035 "Nao e possivel alterar ou excluir cobranca aprovada. Pedido"
		#define STR0036 "Nao e possivel alterar ou excluir cobranca compensada."
		#define STR0037 "Atencao"
		#define STR0038 "Necessario selecionar, no minimo, um registro de Royalties."
		#define STR0039 "Atencao"
		#define STR0040 "O valor final da cobranca nao pode ficar negativo."
		#define STR0041 "Atencao"
		#define STR0042 "O numero da cobranca foi alterado para"
		#define STR0043 "pois o anterior estava em uso."
		#define STR0044 "Com Uso"
		#define STR0045 "Sem Uso"
		#define STR0046 "Pesquisar"
		#define STR0047 "Visualizar"
		#define STR0048 "Incluir"
		#define STR0049 "Alterar"
		#define STR0050 "Excluir"
		#define STR0051 "Aprovar"
		#define STR0052 "Imprime Ficha"
		#define STR0053 "Legenda"
		#define STR0054 "Pendente"
		#define STR0055 "Aprovado"
		#define STR0056 "Compensado sem Fatura"
		#define STR0057 "Faturado"
		#define STR0058 "Legenda"
		#define STR0059 "Atencao"
		#define STR0060 "Nao foi possivel localizar o cliente da filial logada."
		#define STR0061 "Atencao"
		#define STR0062 "Esta cobranca ja foi aprovada."
		#define STR0063 "Produto"
		#define STR0064 "Tipo de Saida"
		#define STR0065 "Informe os Parametros"
		#define STR0066 "Aguarde, aprovando cobranca..."
		#define STR0067 "Atencao"
		#define STR0068 "Esta cobranca tem valor liquido igual a zero, ao confirmar ela sera 'compensada sem fatura'. "
		#define STR0069 "Deseja continuar?"
		#define STR0070 Sim"
		#define STR0071 "Nao"
		#define STR0072 "SERVICOS PRESTADOS A FRANQUIA"
		#define STR0073 "FICHA DE COBRANCA"
		#define STR0074 "Por favor, aguarde... Carregando Royalties..."
		#define STR0075 "Por favor aguarde, carregando os tickets de defeitos..."
	#else
		#define STR0001 "Cobrancas de Royalties e Fundo de Promocao"
		#define STR0002 "Ticket"
		#define STR0003 "Emissao"
		#define STR0004 "Tipo"
		#define STR0005 "Produto"
		#define STR0006 "Descricao"
		#define STR0007 "Fornecedor"
		#define STR0008 "Loj.Forn."
		#define STR0009 "Qtd."
		#define STR0010 "Prc.Venda"
		#define STR0011 "Vlr.Total"
		#define STR0012 "Custo"
		#define STR0013 "Custo Total"
		#define STR0014 "% Royalties"
		#define STR0015 "Vlr.Royalties"
		#define STR0016 "% Fund.Prom."
		#define STR0017 "Vlr.Fund.Prom."
		#define STR0018 "Reembolso"
		#define STR0019 "Filial Orig."
		#define STR0020 "Cabecalho"
		#define STR0021 "Selecione os Royalties e Creditos"
		#define STR0022 "Totais"
		#define STR0023 "Royalties/Fundos"
		#define STR0024 "Credito Defeitos"
		#define STR0025 "ROYALTIES/FUNDO"
		#define STR0026 "DEFEITOS"
		#define STR0027 "SUB-TOTAL"
		#define STR0028 "% DESCONTO"
		#define STR0029 "VLR. DESCONTO"
		#define STR0030 "TOTAL"
		#define STR0031 "Aguarde, Gravando Dados..."
		#define STR0032 "Aguarde, Excluindo Dados..."
		#define STR0033 "Atencao"
		#define STR0034 "Nao e possivel alterar ou excluir cobranca faturada. NF"
		#define STR0035 "Nao e possivel alterar ou excluir cobranca aprovada. Pedido"
		#define STR0036 "Nao e possivel alterar ou excluir cobranca compensada."
		#define STR0037 "Atencao"
		#define STR0038 "Necessario selecionar, no minimo, um registro de Royalties."
		#define STR0039 "Atencao"
		#define STR0040 "O valor final da cobranca nao pode ficar negativo."
		#define STR0041 "Atencao"
		#define STR0042 "O numero da cobranca foi alterado para"
		#define STR0043 "pois o anterior estava em uso."
		#define STR0044 "Com Uso"
		#define STR0045 "Sem Uso"
		#define STR0046 "Pesquisar"
		#define STR0047 "Visualizar"
		#define STR0048 "Incluir"
		#define STR0049 "Alterar"
		#define STR0050 "Excluir"
		#define STR0051 "Aprovar"
		#define STR0052 "Imprime Ficha"
		#define STR0053 "Legenda"
		#define STR0054 "Pendente"
		#define STR0055 "Aprovado"
		#define STR0056 "Compensado sem Fatura"
		#define STR0057 "Faturado"
		#define STR0058 "Legenda"
		#define STR0059 "Atencao"
		#define STR0060 "Nao foi possivel localizar o cliente da filial logada."
		#define STR0061 "Atencao"
		#define STR0062 "Esta cobranca ja foi aprovada."
		#define STR0063 "Produto"
		#define STR0064 "Tipo de Saida"
		#define STR0065 "Informe os Parametros"
		#define STR0066 "Aguarde, aprovando cobranca..."
		#define STR0067 "Atencao"
		#define STR0068 "Esta cobranca tem valor liquido igual a zero, ao confirmar ela sera 'compensada sem fatura'. "
		#define STR0069 "Deseja continuar?"
		#define STR0070 Sim"
		#define STR0071 "Nao"
		#define STR0072 "SERVICOS PRESTADOS A FRANQUIA"
		#define STR0073 "FICHA DE COBRANCA"
		#define STR0074 "Por favor, aguarde... Carregando Royalties..."
		#define STR0075 "Por favor aguarde, carregando os tickets de defeitos..."
	#endif
#endif