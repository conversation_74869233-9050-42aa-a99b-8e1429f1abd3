#ifdef SPANISH
	#define STR0002"Analizar por:"
	#define STR0003"Indicadores:"
	#define STR0004"Categorias"
	#define STR0005"Productos"
	#define STR0006"Marcas"
	#define STR0007"Proveedor"
	#define STR0008"Coleccion"
	#define STR0009"Vendedor"
	#define	STR0010"Hora"
	#define	STR0011"Tiendas vs. Categorias"
	#define	STR0012"Categorias vs. Tiendas"
	#define	STR0013"Tiendas vs. Marcas"
	#define	STR0014"Tiendas vs. Coleccion"
	#define	STR0015"Tiendas vs. Vendedor"
	#define	STR0016"Tiendas vs. Categoria vs. Marca"
	#define	STR0017"Tiendas vs. Hora"
	#define	STR0018"Valor"
	#define	STR0019"Cantidad"
	#define	STR0020"Sin autorizacion"
	#define	STR0021"Consultas de gestion"
	#define	STR0022"Verificando historial de pedidos..."
	#define	STR0023"Verificando movimiento de productos..."
	#define	STR0024"Montando panel de mantenimiento de fotos..."
	#define	STR0025"Verificando ventas por semana..."
	#define	STR0026"Verificando ventas por grilla..."
	#define	STR0027"Exportando al excel..."
	#define	STR0028"Generando graficos..."
	#define	STR0029"Generando graficos por seleccion..."
	#define	STR0030"Desempeno comercial -"
	#define	STR0031"Montando consulta..."
	#define	STR0032"Muestra la pantalla de Historial de pedidos"
	#define	STR0033"Pantalla de Mov. del producto"
	#define	STR0034"Foto del producto"
	#define	STR0035"Ventas por semana"
	#define	STR0036"Grilla del producto"
	#define	STR0037"Exporta al excel"
	#define	STR0038"Busqueda de texto"
	#define	STR0039"Graficos"
	#define	STR0040"Graficos por seleccion"
	#define	STR0041"Ventas consolidadas por semana"
	#define	STR0042"Busqueda en la pantalla"
	#define	STR0043"Division"
	#define	STR0044"Ubica el primero"
	#define	STR0045"Espere..."
	#define	STR0046"Ubicando valor..."
	#define	STR0047"Expresion no encontrada"
	#define	STR0048"Ubica el proximo"
	#define	STR0049"Salir"
	#define	STR0050"Val.Sem-4"
	#define	STR0051"Val.Sem-3"
	#define	STR0052"Val.Sem-2"
	#define	STR0053"Val.Sem-1"
	#define	STR0054"Val.Sem.Actual"
	#define	STR0055"Precio medio"
	#define	STR0056"Cant.Venta periodo"
	#define	STR0057"Venta periodo"
	#define	STR0058"Venta mes"
	#define	STR0059"Cant.Venta mes"
	#define	STR0060"Cant.Entradas periodo"
	#define	STR0061"Entradas periodo"
	#define	STR0062"Cant.Entradas mes"
	#define	STR0063"Entradas mes"
	#define	STR0064"Cobert.Sem.Cant."
	#define	STR0065"Cobert.Sem.Val"
	#define	STR0066"Cobert.Per.Cant."
	#define	STR0067"Cobert.Per.Val"
	#define	STR0068"Cobert.Cant.(Dias)"
	#define	STR0069"Cobert.Val.(Dias)"
	#define	STR0070"Val.Cart.Atrasada"
	#define	STR0071"Val.Cart.Sem.Actual"
	#define	STR0072"Val.Cart.Sem+1"
	#define	STR0073"Val.Cart.Sem+2"
	#define	STR0074"Val.Cart.Sem+3"
	#define	STR0075"Val.Cart.Sem+4"
	#define	STR0076"Val.Cart.Sem.Futuras"
	#define	STR0077"Cant.Sem-4"
	#define	STR0078"Cant.Sem-3"
	#define	STR0079"Cant.Sem-2"
	#define	STR0080"Cant.Qtd.Sem-1"
	#define	STR0081"Cant.Sem.Actual"
	#define	STR0082"Cant.Cart.Atrasada"
	#define	STR0083"Cant.Cart.Sem.Actual"
	#define	STR0084"Cant.Cart.Sem+1"
	#define	STR0085"Cant.Cart.Sem+2"
	#define	STR0086"Cant.Cart.Sem+3"
	#define	STR0087"Cant.Cart.Sem+4"
	#define	STR0088"Cant.Cart.Sem.Futuras"
	#define	STR0089"Vta. Neta"
	#define	STR0090"Costo Unit"
	#define	STR0091"Impuestos"
	#define	STR0092"Descuento"
	#define	STR0093"Cant. Stock actual"
	#define	STR0094"Stock costo"
	#define	STR0095"Stock ventas"
	#define	STR0096"Precio de venta"
	#define	STR0097"CMV"
	#define	STR0098"Valor de margen"
	#define	STR0099"Margen %"
	#define	STR0100"% Participacion"
	#define	STR0101"Filtro por fecha"
	#define	STR0102"Por semana"
	#define	STR0103"Por intervalo de fecha"
	#define	STR0104"Fecha inicial"
	#define	STR0105"Fecha final"
	#define	STR0106"Fecha de referencia"
	#define	STR0107"De comprador"
	#define	STR0108"A comprador"
	#define	STR0109"¿Selecciona sucursales?"
	#define	STR0110"¿Selecciona marcas?"
	#define	STR0111"¿Selecciona clientes?"
	#define	STR0112"Ordena por"
	#define	STR0113"Drill"
	#define	STR0114"Indicadores"
	#define	STR0115"Informe los parametros"
	#define	STR0116"Seleccion de sucursales"
	#define	STR0117"Seleccion de marcas"
	#define	STR0118"Seleccion de clientes"
	#define	STR0119"Periodo seleccionado: De"
	#define	STR0120" A"
	#define	STR0121"Pedidos"
	#define	STR0122"Movimiento de producto"
	#define	STR0123"Mant.Foto"
	#define	STR0124"Ventas por semana"
	#define	STR0125"Grilla"
	#define	STR0126"Excel"
	#define	STR0127"Buscar"
	#define	STR0128"Graficos"
	#define	STR0129"Graficos por seleccion"
	#define STR0130"Categorias x Proveedor x Productos"
	#define STR0131"Evolucao Preco"
	#define STR0132"Verificando Evolucao de Preco dos Produtos..."
	#define STR0133"Considera CD?"
	#define STR0134"Categoria 3"
	#define STR0135"Categoria 4"
	#define STR0136"Clientes x Categorias"
#else
	#ifdef ENGLISH
		#define	STR0002 "Analyse by:"
		#define	STR0003 "Indicators:"
		#define	STR0004 "Categories"
		#define	STR0005 "Products"
		#define	STR0006 "Brands"
		#define	STR0007 "Supplier"
		#define	STR0008 "Collection"
		#define	STR0009 "Sales Representative"
		#define	STR0010 "Time"
		#define	STR0011 "Units x Categories"
		#define	STR0012 "Categories x Units"
		#define	STR0013 "Units x Brands"
		#define	STR0014 "Units x Collection"
		#define	STR0015 "Units x Sales Representative"
		#define	STR0016 "Units x Category x Brand"
		#define	STR0017 "Unit x Time"
		#define	STR0018 "value"
		#define	STR0019 "Quantity"
		#define	STR0020 "No Permission"
		#define	STR0021 "Managerial Queries"
		#define	STR0022 "Checking order history..."
		#define	STR0023 "Checking product operation..."
		#define	STR0024 "Assembling photo maintenance dashboard..."
		#define	STR0025 "Checking sales by week..."
		#define	STR0026 "Checking sales by grid..."
		#define	STR0027 "Exporting to Excel..."
		#define	STR0028 "Generating graphs..."
		#define	STR0029 "Generating graphs by selection..."
		#define	STR0030 "Business Performance -"
		#define	STR0031 "Assembling query..."
		#define	STR0032 "Display order history screen."
		#define	STR0033 "Product Operation Screen"
		#define	STR0034 "Product Photo"
		#define	STR0035 "Sales by Week"
		#define	STR0036 "Product Grid"
		#define	STR0037 "Export to Excel"
		#define	STR0038 "Search Text"
		#define	STR0039 "Graphs"
		#define	STR0040 "Graphs by Selection"
		#define	STR0041 "Cons.Sales per Week"
		#define	STR0042 "Search in Screen"
		#define	STR0043 "Skip"
		#define	STR0044 "Locate First"
		#define	STR0045 "Wait..."
		#define	STR0046 "Locate value..."
		#define	STR0047 "Idiom not found"
		#define	STR0048 "Find Next"
		#define	STR0049 "Exit"
		#define	STR0050 "Val.Week-4"
		#define	STR0051 "Val.Week-3"
		#define	STR0052 "Val.Week 2"
		#define	STR0053 "Val.Week1"
		#define	STR0054 "Cur.Week.Val"
		#define	STR0055 "Average Price"
		#define	STR0056 "Sales Qtty.Period"
		#define	STR0057 "Sales Period"
		#define	STR0058 "Sales Month"
		#define	STR0059 "Sales Qtty.Month"
		#define	STR0060 "Qtty.Entries Period"
		#define	STR0061 "Entries Period"
		#define	STR0062 "Qtty.Entries Month"
		#define	STR0063 "Entries Month"
		#define	STR0064 "Cover.Week.Qtty"
		#define	STR0065 "Cover.Week.Val"
		#define	STR0066 "Cover.Per.Qtty"
		#define	STR0067 "Cover.Per.Val"
		#define	STR0068 "Cover.Qtty.(Days)"
		#define	STR0069 "Cover.Val.(Days)"
		#define	STR0070 "Val.Port.Delay"
		#define	STR0071 "Val.Port.Week.Current"
		#define	STR0072 "Val.Port.Week+1"
		#define	STR0073 "Val.Port.Week+2"
		#define	STR0074 "Val.Port.Week+3"
		#define	STR0075 "Val.Port.Week+4"
		#define	STR0076 "Val.Port.Week.Future"
		#define	STR0077 "Qtty.Week-4"
		#define	STR0078 "Qtty.Week-3"
		#define	STR0079 "Qtty.Week-2"
		#define	STR0080 "Qtty.Week-1"
		#define	STR0081 "Qtty.Week.Current"
		#define	STR0082 "Qtty.Port.Delay"
		#define	STR0083 "Qtty.Port.Week.Current"
		#define	STR0084 "Qtty.Port.Week+1"
		#define	STR0085 "Qtty.Port.Week+2"
		#define	STR0086 "Qtty.Port.Week+3"
		#define	STR0087 "Qtty.Port.Week+4"
		#define	STR0088 "Qtty.Port.Week.Future"
		#define	STR0089 "Net Sales"
		#define	STR0090 "Unit Cost"
		#define	STR0091 "Taxes"
		#define	STR0092 "Discount"
		#define	STR0093 "Qtty.Curr.Stock"
		#define	STR0094 "Stock Cost"
		#define	STR0095 "Sales Stock"
		#define	STR0096 "Sales Price"
		#define	STR0097 "CMV"
		#define	STR0098 "Margin Value"
		#define	STR0099 "Margin %"
		#define	STR0100 "% Participation"
		#define	STR0101 "Data Filter"
		#define	STR0102 "By Week"
		#define	STR0103 "By Date Interval"
		#define	STR0104 "Start Date"
		#define	STR0105 "End Date"
		#define	STR0106 "Reference Date"
		#define	STR0107 "From Buyer"
		#define	STR0108 "To Buyer"
		#define	STR0109 "Select Branches?"
		#define	STR0110 "Select Brands?"
		#define	STR0111 "Select Customers?"
		#define	STR0112 "Order by"
		#define	STR0113 "Drill"
		#define	STR0114 "Indicators"
		#define	STR0115 "Enter Parameters"
		#define	STR0116 "Select Branches"
		#define	STR0117 "Select Brands"
		#define	STR0118 "Select Customers"
		#define	STR0119 "Selected Period: From "
		#define	STR0120 " To "
		#define	STR0121 "Orders"
		#define	STR0122 "Product Movement"
		#define	STR0123 "Photo.Maint"
		#define	STR0124 "Sales by Week"
		#define	STR0125 "Grid"
		#define	STR0126 "Excel"
		#define	STR0127 "Query"
		#define	STR0128 "Graphs"
		#define	STR0129 "Graphs by Selection"
		#define STR0130 "Categories x Supplier x Products"
		#define STR0131"Evolucao Preco"
		#define STR0132"Verificando Evolucao de Preco dos Produtos..."
		#define STR0133"Considera CD?"
		#define STR0134"Categoria 3"
		#define STR0135"Categoria 4"
		#define STR0136"Clientes x Categorias"
	#else
		#define STR0002"Analisar por:"
		#define STR0003"Indicadores:"
		#define STR0004"Categorias"
		#define STR0005"Produtos"
		#define STR0006"Marcas"
		#define STR0007"Fornecedor"
		#define STR0008"Colecao"
		#define STR0009"Vendedor"
		#define STR0010"Hora"
		#define STR0011"Lojas x Categorias"
		#define STR0012"Categorias x Lojas"
		#define STR0013"Lojas x Marcas"
		#define STR0014"Lojas x Colecao"
		#define STR0015"Lojas x Vendedor"
		#define STR0016"Lojas x Categoria x Marca"
		#define STR0017"Lojas x Hora"
		#define STR0018"Valor"
		#define STR0019"Quantidade"
		#define STR0020"Sem Permissao"
		#define STR0021"Consultas Gerenciais"
		#define STR0022"Verificando Historico de Pedidos..."
		#define STR0023"Verificando Movimentacao de Produtos..."
		#define STR0024"Montando Painel de Manutencao de Fotos..."
		#define STR0025"Verificando Vendas por Semana..."
		#define STR0026"Verificando Vendas por Grade..."
		#define STR0027"Exportando para o Excel..."
		#define STR0028"Gerando Graficos..."
		#define STR0029"Gerando Graficos Por Selecao..."
		#define STR0030"Desempenho Comercial -"
		#define STR0031"Montando Consulta..."
		#define STR0032"Exibe a Tela de Historico de Pedidos"
		#define STR0033"Tela de Mov. do Produto"
		#define STR0034"Foto do Produto"
		#define STR0035"Vendas por Semana"
		#define STR0036"Grade do Produto"
		#define STR0037"Exporta para Excel"
		#define STR0038"Pesquisa Texto"
		#define STR0039"Graficos"
		#define STR0040"Graficos por Selecao"
		#define STR0041"Vendas Consolidada por Semana"
		#define STR0042"Pesquisa na Tela"
		#define STR0043"Quebra"
		#define STR0044"Localiza Primeiro"
		#define STR0045"Aguarde..."
		#define STR0046"Localizando valor..."
		#define STR0047"Expressao nao encontrada"
		#define STR0048"Localiza Proximo"
		#define STR0049"Sair"
		#define STR0050"Val.Sem-4"
		#define STR0051"Val.Sem-3"
		#define STR0052"Val.Sem-2"
		#define STR0053"Val.Sem-1"
		#define STR0054"Val.Sem.Atual"
		#define STR0055"Preco Medio"
		#define STR0056"Qt.Venda Periodo"
		#define STR0057"Venda Periodo"
		#define STR0058"Venda Mes"
		#define STR0059"Qt.Venda Mes"
		#define STR0060"Qt.Entradas Periodo"
		#define STR0061"Entradas Periodo"
		#define STR0062"Qt.Entradas Mes"
		#define STR0063"Entradas Mes"
		#define STR0064"Cobert.Sem.Qtd"
		#define STR0065"Cobert.Sem.Val"
		#define STR0066"Cobert.Per.Qtd"
		#define STR0067"Cobert.Per.Val"
		#define STR0068"Cobert.Qtd.(Dias)"
		#define STR0069"Cobert.Val.(Dias)"
		#define STR0070"Val.Cart.Atrasado"
		#define STR0071"Val.Cart.Sem.Atual"
		#define STR0072"Val.Cart.Sem+1"
		#define STR0073"Val.Cart.Sem+2"
		#define STR0074"Val.Cart.Sem+3"
		#define STR0075"Val.Cart.Sem+4"
		#define STR0076"Val.Cart.Sem.Futuras"
		#define STR0077"Qtd.Sem-4"
		#define STR0078"Qtd.Sem-3"
		#define STR0079"Qtd.Sem-2"
		#define STR0080"Qtd.Sem-1"
		#define STR0081"Qtd.Sem.Atual"
		#define STR0082"Qtd.Cart.Atrasado"
		#define STR0083"Qtd.Cart.Sem.Atual"
		#define STR0084"Qtd.Cart.Sem+1"
		#define STR0085"Qtd.Cart.Sem+2"
		#define STR0086"Qtd.Cart.Sem+3"
		#define STR0087"Qtd.Cart.Sem+4"
		#define STR0088"Qtd.Cart.Sem.Futuras"
		#define STR0089"Vd.Liquida"
		#define STR0090"Custo Unit"
		#define STR0091"Impostos"
		#define STR0092"Desconto"
		#define STR0093"Qt.Est.Atual"
		#define STR0094"Estoque Custo"
		#define STR0095"Estoque Vendas"
		#define STR0096"Preco Venda"
		#define STR0097"CMV"
		#define STR0098"Valor Margem"
		#define STR0099"Margem %"
		#define STR0100"% Participacao"
		#define STR0101"Filtro Data"
		#define STR0102"Por Semana"
		#define STR0103"Por Intervalo de Data"
		#define STR0104"Data Inicial"
		#define STR0105"Data Final"
		#define STR0106"Data Referencia"
		#define STR0107"Do Comprador"
		#define STR0108"Ate Comprador"
		#define STR0109"Seleciona Filiais?"
		#define STR0110"Seleciona Marcas?"
		#define STR0111"Seleciona Clientes?"
		#define STR0112"Ordena por"
		#define STR0113"Drill"
		#define STR0114"Indicadores"
		#define STR0115"Informe os Parametros"
		#define STR0116"Selecao de Filiais"
		#define STR0117"Selecao de Marcas"
		#define STR0118"Selecao de Clientes"
		#define STR0119"Periodo Selecionado: De"
		#define STR0120" Ate"
		#define STR0121"Pedidos"
		#define STR0122"Movimentacao de Produto"
		#define STR0123"Manut.Foto"
		#define STR0124"Vendas por Semana"
		#define STR0125"Grade"
		#define STR0126"Excel"
		#define STR0127"Pesquisar"
		#define STR0128"Graficos"
		#define STR0129"Graficos Por Selecao"
		#define STR0130"Categ. x Forn. x Prod."
		#define STR0131"Evolucao Preco"
		#define STR0132"Verificando Evolucao de Preco dos Produtos..."
		#define STR0133"Considera CD?"
		#define STR0134"Categoria 3"
		#define STR0135"Categoria 4"
		#define STR0136"Clientes x Categorias"
	#endif
#endif