#Include 'Protheus.ch'

User Function VLDIVA()

Local nPosProd	 := Ascan( aHeader, { |x| Alltrim(x[2]) == "C6_PRODUTO" } )		// LOCAL
Local nPosIVA	 := Ascan( aHeader, { |x| Alltrim(x[2]) == "C6_PICMRET" } )		// LOCAL
Local cOrig		 := ''

U_NSAtuSX6()
cOrig		 := SuperGetMV("NS_IVAIMP", NIL, '1')

SB1->(DbSetOrder(1))
SA1->(DbSetOrder(1))
SF7->(DbSetOrder(1))
If SB1->(DbSeek(xFilial("SB1")+M->C6_PRODUTO))
	If SB1->B1_ORIGEM $ cOrig
		SA1->(DbSeek(xFilial("SA1")+M->C5_CLIENTE+M->C5_LOJACLI)) 
		cQuery := " select F7_XMARGE2 MARG2"
		cQuery += " from "
		cQuery += RetSqlName("SF7") + " SF7 "
		cQuery += " Where "
		cQuery += " SF7.F7_FILIAL = '" + xFilial("SF7") + "' and"
		cQuery += " SF7.F7_GRTRIB = '" + SB1->B1_GRTRIB + "' and"
		cQuery += " SF7.F7_GRPCLI = '" + SA1->A1_GRPTRIB + "' and"
		cQuery += " SF7.F7_EST = '" + SA1->A1_EST  + "' and"
		cQuery += " SF7.D_E_L_E_T_ <> '*'  "
		
		cQuery := ChangeQuery(cQuery)
		
		if SELECT ("QRY3") > 0
			QRY3->(dbCloseArea())
		endif		
		
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),'QRY3',.T.,.T.)
		
		If QRY3->(!EOF())
			Acols[n][nPosIVA]	:=  QRY3->MARG2 
		Else
			Acols[n][nPosIVA]	:= 0	
		Endif 
					
	
	Else
		Acols[n][nPosIVA]	:= 0	
	Endif
Endif

Return .T.

