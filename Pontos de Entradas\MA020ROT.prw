#include "rwmake.ch"

** Alterado por: <PERSON> - amjg<PERSON><EMAIL> - Em: 12/07/2006

// TRECHO QUE FAZ PARTE DO PE M020INC
User Function MA020ROT

If Select("__SX6") > 0
	dbSelectArea("__SX6")
	__SX6->(dbCloseArea())
Endif

Use SX6010 Index SX6010 Alias __SX6 Share New Via "DBFCDX"

Return

User Function ACHACODSA2(cCodsSA2)
LOCAL nHdlLock := 0

If Empty(M->A2_DDD) .And. M->A2_LOJA == '01'
	If Left(cCodsSA2,2) == '99'
		__SX6->( DbSeek( "  " + "MV_ULCOD99" ) )
	Else
		__SX6->( DbSeek( "  " + "MV_ULCOD00" ) )
	Endif
	Return( Alltrim(__SX6->X6_CONTEUD) )
Endif

Return(cCodsSA2)