#Include "Protheus.ch"
#Include "Topconn.ch"

/*
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
??rograma  ?SySmnEnt ?Autor ?Irineu <PERSON>ho ?Data ?03/15/13 ??
??????????????????????????????????
??esc.     ?Rotina para preenchimento de Semana e Dt. Entrega ??
??         ?para as rotinas de Pedido de Compra e Embarque    ??
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
*/
User Function SySmnEnt(cTpRotina,lLote,aLote,aAltera,aDupli,aVerificar,nOpt) //1 - Pedido de Compra = 2 - Embarque = 3 - Purchase Order (P.O)

Local aSize		:= MsAdvSize(.T.,.F.,400)
Local cTitTela	:= "[SELECIONAR A SEMANA/DATA DE ENTREGA]"
Local cGet1 := Space(6)
Local cGet2 := CTOD("")
Local cGet3 := PADR( "",TAMSX3("ZA6_CODIGO")[1])
Local cGet4 := PADR( "",TAMSX3("ZA6_CODIGO")[1])
Local oGet2 := Nil
Local oGet3 := Nil
Local nY    := 0
Local aErro := {}
Local aMsgC7 := {}
Local aMsgZE := {}
Local oFont1 :=  TFont():New("Tahoma",,017,,.T.,,,,,.F.,.F.)

Private oDlg	:= Nil
Private nValid	:= GetMv("MV_SYVALCT",,15)
Private aNumSC 	:= {}

Default cTpRotina := ""
Default lLote		:=	.F.
Default aLote 	:=	{}

If !Empty(cTpRotina)

	aSize[6] -= (40/100) * aSize[6]
	aSize[5] -= (70/100) * aSize[5]

	If cTpRotina == "1"

		If Empty(SC7->C7_PO_EIC)
			cDescTipo := "PEDIDO"
			cGetA	:= SC7->C7_NUM
			cGet1	:= SC7->C7_01SEMAN
			cGet2	:= SC7->C7_DATPRF
		Else
			Aviso( "SYSMNENT" , "Pedido de Compra j?est?com o embarque realizado. Alterar a semana de entrega pela rotina de Embarque." , {"Ok"} )
			Return
		EndIf
	ElseIf cTpRotina == "2" .AND. !lLote
		cDescTipo := "PROCESSO"
		cGetA	:= SW6->W6_HAWB
		cGet1	:= SW6->W6_01SEMAN
		cGet2	:= SW6->W6_01ENTRE
	ElseIf cTpRotina == "3"
		cDescTipo := "PURCHASE ORDER (P.O)"
		SW3->(DbSetOrder(1))
		SW3->(DbSeek(xFilial("SW3") + SW2->W2_PO_NUM))
		cGetA	:= SW2->W2_PO_NUM
		cGet1	:= SW3->W3_SEMANA
		cGet2	:= SW3->W3_DT_ENTR
	EndIf
	If !lLote

		//-------------------------------------------------------
		// Andre Lanzieri 30/07/2019
		// Busca ultimo motivo informado.
		//-------------------------------------------------------
		u_SYSMEMOT(@cGet3, @cGet4, cTpRotina, cGetA)
		
		DEFINE MSDIALOG oDlg FROM 0,0 TO aSize[6],aSize[5] TITLE cTitTela Of oMainWnd PIXEL

		oLayer := FWLayer():new()

		//?????????????????????????????
		//?Inicializa o objeto com a janela que ele pertencera. ?
		//?????????????????????????????
		oLayer:init(oDlg,.F.)

		oLayer:addLine("Lin01",35,.F.)

		//??????????????
		//?Cria a coluna do Layer. ?
		//??????????????
		oLayer:addCollumn('Col01',100,.F.,"Lin01")
		oLayer:addWindow("Col01","L1_Win01",cDescTipo,100,.F.,.F.,,"Lin01",)

		oPanelA := oLayer:GetWinPanel("Col01","L1_Win01","Lin01")

		@ 002,001 SAY oSay1 PROMPT cGetA SIZE 042,008 OF oPanelA PIXEL

		//????????????
		//?Cria Linha do Layer. ?
		//????????????
		oLayer:addLine("Lin02",050,.F.)

		//??????????????
		//?Cria a coluna do Layer. ?
		//??????????????
		//oLayer:addCollumn('Col01',050,.F.,"Lin02")
		oLayer:addCollumn('Col02',100,.F.,"Lin02")

		//?????????????????????????
		//?Adiciona Janelas as suas respectivas Colunas. ?
		//?????????????????????????
		// oLayer:addWindow("Col01","L2_Win01","SEMANA DE ENTREGA",100,.F.,.F.,,"Lin02",)
		oLayer:addWindow("Col02","L2_Win01","DATA DE ENTREGA",100,.F.,.F.,,"Lin02",)

		// oPanel1 := oLayer:GetWinPanel("Col01","L2_Win01","Lin02")

		// @ 002, 001	MSGET oGet1 VAR cGet1	SIZE 060, 020 OF oPanel1 PIXEL F3 "AY6" Valid fDtEnt(@cGet1,cGetA,@cGet2,cTpRotina,"SEMANA",,aErro)

		oPanel2 := oLayer:GetWinPanel("Col02","L2_Win01","Lin02")
		@ 002, 001	MSGET oGet2 VAR cGet2	SIZE 060, 012 OF oPanel2 PIXEL Valid fDtEnt(@cGet1,cGetA,@cGet2,cTpRotina,"DATA",,aErro)

		@ 022, 001 SAY oSay2 PROMPT "Motivo" 		SIZE 999, 012 OF oPanel2 FONT oFont1 PIXEL

		@ 032, 001	MSGET oGet3 VAR cGet3	SIZE 060, 012 F3 "ZA6" OF oPanel2 PIXEL Valid fCdMot(cGet3,@cGet4)

		@ 046, 001	MSGET oGet4 VAR cGet4	SIZE 080, 012 F3 WHEN .F. OF oPanel2 PIXEL

		ACTIVATE MSDIALOG oDlg CENTERED ON INIT ( EnchoiceBar(oDlg,{|| SyfOK(cGet1,cGetA,cGet2,cTpRotina,,,,,cGet3) }, {|| SyfClose() },,) )
	Else
		For nY := 1 to Len(aLote)

			If nOpt == 1
				If (len(aLote[nY]) == 2 .and. nOpt == 1)
					aadd(aLote[nY],Nil)
					aIns(aLote[nY],2)
					aLote[nY][2] := ""
					If	!fDtEnt(@aLote[nY][2],aLote[nY][1],@aLote[nY][3],cTpRotina,"DATA",lLote,@aErro)
						loop
					Endif
					SyfOK(aLote[nY][2],aLote[nY][1],aLote[nY][3],cTpRotina,lLote,@aMsgC7,@aMsgZE,@aErro)
				Else
					aadd(aLote[nY],"Layout")
					aadd(aVerificar,aLote[nY])
				Endif
			Else

				If (len(aLote[nY]) == 5 .and. nOpt == 3)

					aadd(aLote[nY],Nil)
					aLote[nY][6] := ""

					If !Empty(aLote[nY][4]) // Data

						If !Empty(aLote[nY][1])
							cTpRotina := "3" // Purchase Order (P.O)
							cPedCom	:= aLote[nY][1]

							DbSelectArea("SW2")
							DbSetOrder(1)
							If SW2->(!DbSeek(xFilial("SW2")+cPedCom))
								MsgInfo("Aten?o, n? encontrada PO em tabela SW2. Informe TI."+cPedCom)
							EndIf
						Else
							cTpRotina := "2" // Embarque
							cPedCom	:= aLote[nY][2]
						EndIf

						If	!fDtEnt(@aLote[nY][6],cPedCom, @aLote[nY][4], cTpRotina,"DATA",lLote,@aErro, aLote[nY][5])
							loop
						Endif

						SyfOK(aLote[nY][6], cPedCom, aLote[nY][4], cTpRotina,lLote,@aMsgC7,@aMsgZE,@aErro,aLote[nY][5])

					EndIf

				Else
					aadd(aLote[nY],"Layout")
					aadd(aVerificar,aLote[nY])
				Endif

			EndIf

		Next
		If !Empty(aMsgC7)
			aadd(aAltera,aMsgC7)
		Endif
		If !Empty(aMsgZE)
			aadd(aAltera,aMsgZE)
		Endif
		For nY:=1 to Len(aErro)
			aadd(aVerificar,aErro[nY])
		Next
	Endif
Else

	Alert("Rotina executada de forma incorreta.")

EndIf

Return

/*/{Protheus.doc} RetMot
Busca ultimo motivo de altera?o de data informado.

<AUTHOR>
@since 30/07/2019
/*/
User Function SYSMEMOT(cGet3, cGet4, cTpRotina, cGetA)

	Local cPedido := ""
	Local cQuery  := ""

	If cTpRotina == "1"
		cPedido := cGetA
	ElseIf cTpRotina == "2"

		If SW6->(dbSeek( xFilial("SW6") + cGetA ))

			SW7->(dbSetOrder(1))
			If SW7->(dbSeek( xFilial("SW7") + SW6->W6_HAWB ))

				SC7->(dbOrderNickName("C7_NUM_PO"))
				If SC7->(dbSeek( xFilial("SC7") + SW7->W7_PO_NUM ))
					cPedido := SC7->C7_NUM
				EndIf

			EndIf

		Else
			MsgInfo("Processo n? encontrado: "+cGetA)
		EndIf

	ElseIf cTpRotina == "3"

		cPedido := SW2->W2_PO_NUM

	EndIf

	If !Empty(cPedido)

		cQuery := "SELECT TOP 1									" + CRLF
		cQuery += "	ZA7.ZA7_MOTIVO, ZA6.ZA6_DESCRI				" + CRLF
		cQuery += "FROM "+RetSQLName("ZA7") + " ZA7 (NOLOCK) 	" + CRLF

		cQuery += "LEFT JOIN "+RetSQLName("ZA6") + " ZA6 (NOLOCK) ON " + CRLF
		cQuery += "ZA6.ZA6_FILIAL = '"+xFilial("ZA6")+"'		" + CRLF
		cQuery += "AND ZA6.ZA6_CODIGO = ZA7.ZA7_MOTIVO			" + CRLF
		cQuery += "AND ZA6.D_E_L_E_T_ = ''						" + CRLF

		cQuery += "WHERE 										" + CRLF

		cQuery += " ZA7.ZA7_FILIAL = '"+xFilial("ZA7")+"' 		" + CRLF
		cQuery += "	AND ZA7.ZA7_TIPO 	= 'PC' 					" + CRLF
		cQuery += "	AND ZA7.ZA7_NUM 	= '"+cPedido+"' 		" + CRLF
		cQuery += "	AND ZA7.D_E_L_E_T_ = ' ' 					" + CRLF
		cQuery += "	ORDER BY ZA7.R_E_C_N_O_ DESC "

		If Select("TRBP") > 0
			TRBP->(DbCloseArea())
		EndIf

		TcQuery cQuery New Alias "TRBP"

		If TRBP->(!Eof())

			cGet3 := TRBP->ZA7_MOTIVO
			cGet4 := TRBP->ZA6_DESCRI

		EndIf

		TRBP->(DbCloseArea())

	EndIf

Return

Static Function fCdMot(cGet3,cGet4)

	Local lRet := .T.

	If !Empty(cGet3)

	 	DbSelectArea("ZA6")
		DbSetOrder(1)

		If !DbSeek(xFilial("ZA6")+cGet3)

			MsgInfo("Motivo n? encontrado.")
			lRet := .F.

		ElseIf ZA6->ZA6_ATIVO == 'N'

			MsgInfo("Motivo n? est?ativo.")
			lRet := .F.
		Else
			cGet4 := ZA6->ZA6_DESCRI
		EndIf

	EndIf

Return(lRet)

/*
?????????????????????????????????
?????????????????????????????????
?????????????????????????????????
??rograma  ?SyfOK ?Autor ?Irineu Filho ?Data ?03/15/13 ??
?????????????????????????????????
??esc.     ?Fun?o que processa a confirma?o da tela.     ??
?????????????????????????????????
?????????????????????????????????
?????????????????????????????????
*/

Static Function SyfOK(cParam,cPedCom,dRetorno,cTpRotina,lLote,aMsgC7,aMsgZE,aErro,cGet3)

Local nPos := 0
Local nX	:=	0
Local lExec := .T.
Default lLote := .F.
Default aErro	:=	{}
Default cGet3 := ""

If Empty(cParam)
	Aviso( "SYSMNENT" , "Por Favor, Preencher a semana." , {"Ok"} )
	Return
EndIf

If Empty(dRetorno)
	Aviso( "SYSMNENT" , "Por Favor, Preencher a Dt. Entrega." , {"Ok"} )
	Return
EndIf
If !lLote
	lExec := Iif (Aviso( "SYSMNENT" , "Confirma as datas ?" , {"Sim","N?"} ) == 1,.T.,.F.)
Endif

If lExec
	Begin Transaction
	If cTpRotina == "1"
		SC7->(dbSetOrder(1))
		If SC7->(dbSeek( xFilial("SC7") + cPedCom ))
			While SC7->(!EOF()) .AND. xFilial("SC7") + cPedCom == SC7->C7_FILIAL + SC7->C7_NUM

				U_M030ILOG("PC", SC7->C7_NUM, SC7->C7_ITEM, SC7->C7_PRODUTO, SC7->C7_LOCAL, SC7->C7_01PACKS, SC7->C7_DATPRF, dRetorno, "SYSMNENT", cGet3)

				If Reclock("SC7",.F.)
					SC7->C7_01SEMAN	:= cParam
					SC7->C7_DATPRF	:= dRetorno
					MsUnlock()
				EndIf

				IF !Empty(SC7->C7_NUMSC)
					nPos := Ascan(aNumSC,{ |x| Alltrim(x[1]) == Alltrim(SC7->C7_NUMSC)})
					IF nPos == 0
						AAdd( aNumSC , {SC7->C7_NUMSC,dRetorno} )
					EndIF
				EndIF

				SC7->(dbSkip())
			EndDo
			fProcSZE( cPedCom , cParam , dRetorno , "2" )
		EndIf
	ElseIf cTpRotina == "2"
		If SW6->(dbSeek( xFilial("SW6") + cPedCom ))
			If Reclock("SW6",.F.)
				SW6->W6_01SEMAN	:= cParam
				SW6->W6_01ENTRE	:= dRetorno
				MsUnlock()
			EndIf
		Else
			aadd(aErro,{cPedCom,"Embarque"})
		Endif
		fProcSC7( SW6->W6_HAWB , SW6->W6_01SEMAN , SW6->W6_01ENTRE,lLote,@aMsgC7, cGet3 )
		fProcSZE( SW6->W6_HAWB , SW6->W6_01SEMAN , SW6->W6_01ENTRE , "1",lLote,@aMsgZE )
	ElseIf cTpRotina == "3"
		LjMsgRun("Por favor aguarde, atualizando base de dados...",, {|| SYPRCTABS(cParam,dRetorno,SW2->W2_PO_NUM, cGet3, @aMsgC7, lLote) })
	EndIf

	IF Len(aNumSC) > 0

		For nX := 1 To Len(aNumSC)

			cQry:="	SELECT SC1.R_E_C_N_O_ AS C1_RECNO "
			cQry+="	FROM "+RetSqlName("SC1")+" SC1"+CRLF
			cQry+="	WHERE C1_FILIAL 	= '"+xFilial("SC1")+"'"+CRLF
			cQry+="	AND   C1_NUM		= '"+aNumSC[nX,1]+"'"+CRLF
			cQry+="	AND SC1.D_E_L_E_T_	= ' '"+CRLF

			If Select("TRB1") > 0
				TRB1->(DbCloseArea())
			Endif
			TcQuery cQry New Alias "TRB1"

			While TRB1->(!Eof())

				SC1->(DbGoTo(TRB1->C1_RECNO))

					U_M030ILOG("SC", SC1->C1_NUM, SC1->C1_ITEM, SC1->C1_PRODUTO, SC1->C1_LOCAL, SC1->C1_01PACK, SC1->C1_01DTCH1, aNumSC[nX,2], "SYSMNENT", cGet3)

					RecLock("SC1",.F.)
						SC1->C1_01DTCH1	:= aNumSC[nX,2]
					SC1->(MsUnLock())
				TRB1->(DbSkip())

			EndDo

		Next

	EndIF

	End Transaction
	If !lLote
		oDlg:End()
	Endif
EndIf

Return

/*
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
??rograma  ?SyfClose ?Autor ?Irineu Filho ?Data ?03/15/13 ??
??????????????????????????????????
??esc.     ?Processa o cancelamento da tela.                  ??
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
*/
Static Function SyfClose()
oDlg:End()
Return

/*
?????????????????????????????????
?????????????????????????????????
?????????????????????????????????
??rograma  ?fDtEnt ?Autor ?Irineu Filho ?Data ?03/15/13 ??
?????????????????????????????????
??esc.     ?Busca as informa?es de Semana e Dt. Entrega da ??
??         ?tabela AYH.                                     ??
?????????????????????????????????
?????????????????????????????????
?????????????????????????????????
*/
Static Function fDtEnt(cParam,cPedCom,dRetorno,cTpRotina,cCampoExec,lLote,aErro, cMotivo)

Local lRetorno		:= .F.
Local cSemana		:= cParam
Local aArea			:= GetArea()
Local cPesqSemana	:= Right(cSemana,4) + Left(cSemana,2)
Local cColecao		:= ""
Local aMascara		:= Separa(GetMv('MV_MASCGRD',,'09,04,02,02'),',')
Local nTamPai   	:= Val(aMascara[1])
Local cProduto	    := ""
Default lLote := .F.

If lLote .AND. !Empty(cMotivo)

	DbSelectArea("ZA6")
	DbSetOrder(1)

	If !DbSeek(xFilial("ZA6")+cMotivo)

		aadd(aErro,{cPedCom+" Motivo n? encontrado: "+cMotivo,cParam,dRetorno,"MOTIVO"})
		Return(.F.)

	ElseIf ZA6->ZA6_ATIVO == 'N'

		add(aErro,{cPedCom+" Motivo n? ativo: "+cMotivo,cParam,dRetorno,"MOTIVO"})
		Return(.F.)

	EndIf

EndIf

If lLote
	dRetorno := ctod(dRetorno)
Endif

If cTpRotina == "1"
	SC7->(dbSetOrder(1))
	If SC7->(dbSeek( xFilial("SC7") + cPedCom ))
		cColecao	:= SC7->C7_XCOLECA
		cProduto	:= Left(SC7->C7_PRODUTO,nTamPai)
	EndIf
ElseIf cTpRotina == "2"
	SW7->(dbSetOrder(1))
	If SW7->(dbSeek( xFilial("SW7") + iif(lLote, cPedCom, SW6->W6_HAWB) ))
		If Sw6->W6_TipoDes="14"
			ChkFile("SW2")
			cColecao	:=	Posicione("SC7", 1, xFilial("SC7")+Posicione("SW2",1,xfilial("SW2")+Sw7->W7_po_num, "W2_PO_SIGA"), "C7_XCOLECA")
			cProduto	:= Left(SW7->W7_COD_I,nTamPai)

		Else
			SC7->(dbOrderNickName("C7_NUM_PO"))
			If SC7->(dbSeek( xFilial("SC7") + SW7->W7_PO_NUM ))
				cColecao	:= SC7->C7_XCOLECA
				cProduto	:= Left(SW7->W7_COD_I,nTamPai)

			EndIf

		EndIf

	EndIf

ElseIf cTpRotina == "3"
	SC7->(dbOrderNickName("C7_NUM_PO"))
	If SC7->(dbSeek( xFilial("SC7") + cPedCom ))
		cColecao	:= SC7->C7_XCOLECA
		cProduto	:= Left(SW7->W7_COD_I,nTamPai)
	EndIf
EndIf

If Empty(cColecao)
	cColecao := "F13"
EndIf

If !Empty(cColecao) .AND. !Empty(cProduto)

	If Alltrim(cCampoExec) == "SEMANA"
		AY6->(DbSetOrder(1))
		IF AY6->(DbSeek(xFilial('AY6')+cPesqSemana))

			//Valida se a data escolhida faz parte da vigencia da colecao
			/*
			AYH->(DbSetOrder(1))
			If AYH->(DbSeek(xFilial("AYH") + cColecao ))
				If (AY6->AY6_DTFIM > AYH->AYH_DTFIM)
					Help(Nil,Nil,"SELECIONAR A SEMANA/DATA DE ENTREGA",,'A data selecionada est?fora do inicio da vigencia da cole?o.',1,1)
					dRetorno := CTOD("")
					Return(.F.)
				Endif
			Endif
			*/

			dRetorno := AY6->AY6_DTFIM
			lRetorno := .T.
		Else
			dRetorno := CTOD("")
			If !Empty(cParam)
				aadd(aErro,{cPedCom,cParam,cColecao,"Semana"})
				lRetorno := .F.
			Else
				lRetorno := .T.
			EndIf
		EndIf

	Else

		If !Empty(dRetorno)
			cParam := U_RetSmnCal(dRetorno)
			lRetorno := .T.
		Else
			cParam := CriaVar("C7_01SEMAN")
			lRetorno := .T.
		EndIf

	EndIf

Else
	If lLote
		aadd(aErro,{cPedCom,cParam,dRetorno,"Problema"})
		Return(.F.)
	Else
		Help(Nil,Nil,"SELECIONAR A SEMANA/DATA DE ENTREGA",,"Problema no tratamento dos campos de Cole?o e Produto.",1,1)
		Return(.F.)
	EndIf
Endif
RestArea(aArea)

Return lRetorno


/*
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
??rograma  ?fProcSC7 ?Autor ?Irineu Filho ?Data ?03/21/13 ??
??????????????????????????????????
??esc.     ?Troca os valores do campo C7_01SEMAN e C7_DATPRF, ??
??         ?de acordo com os valores preenchidos na tabela    ??
??         ?SW6.                                              ??
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
*/
Static Function fProcSC7( cW6HAWB , cW601SEMAN , cW601ENTRE,lLote,aMsgC7, cGet3 )

Local aProcs	:= {}
Local nExiste	:= 0
Local nPos		:= 0
Local ii		:= 1
Default lLote	:= .F.

SW7->(dbSetOrder(1))
If SW7->(dbSeek( xFilial("SW7") + cW6HAWB ))
	While SW7->(!EOF()) .AND. xFilial("SW7") + cW6HAWB == SW7->W7_FILIAL + SW7->W7_HAWB
		nExiste := Ascan(aProcs,{ |x| Alltrim(x[1]) == Alltrim(SW7->W7_PO_NUM) })
		If nExiste == 0
			Aadd( aProcs , { SW7->W7_PO_NUM , SC7->C7_NUM } )
			If Sw6->W6_TipoDes="14"
				ChkFile("SW2")
				SC7->(DbSetOrder(1))
				If SC7->(dbSeek( xFilial("SC7") + Posicione("SW2",1,xfilial("SW2")+Sw7->W7_po_num, "W2_PO_SIGA") ))
					While SC7->(!EOF()) .AND. xFilial("SC7") + SW2->W2_PO_Siga == SC7->C7_FILIAL + SC7->C7_Num

						U_M030ILOG("PC", SC7->C7_NUM, SC7->C7_ITEM, SC7->C7_PRODUTO, SC7->C7_LOCAL, SC7->C7_01PACKS, SC7->C7_DATPRF, cW601ENTRE, "SYSMNENT", cGet3)

						If Reclock( "SC7" , .F. )
							SC7->C7_01SEMAN	:= cW601SEMAN
							SC7->C7_DATPRF	:= cW601ENTRE
							MsUnlock()
						EndIf

						IF !Empty(SC7->C7_NUMSC)
							nPos := Ascan(aNumSC,{ |x| Alltrim(x[1]) == Alltrim(SC7->C7_NUMSC)})
							IF nPos == 0
								AAdd( aNumSC , {SC7->C7_NUMSC,cW601ENTRE} )
							EndIF
						EndIF

						SC7->(dbSkip())
					EndDo

				EndIf

			Else
				SC7->(dbOrderNickName("C7_NUM_PO"))
				If SC7->(dbSeek( xFilial("SC7") + SW7->W7_PO_NUM ))
					While SC7->(!EOF()) .AND. xFilial("SC7") + SW7->W7_PO_NUM == SC7->C7_FILIAL + SC7->C7_PO_EIC

						U_M030ILOG("PC", SC7->C7_NUM, SC7->C7_ITEM, SC7->C7_PRODUTO, SC7->C7_LOCAL, SC7->C7_01PACKS, SC7->C7_DATPRF, cW601ENTRE, "SYSMNENT", cGet3)

						If Reclock( "SC7" , .F. )
							SC7->C7_01SEMAN	:= cW601SEMAN
							SC7->C7_DATPRF	:= cW601ENTRE
							MsUnlock()

						EndIf
						SC7->(dbSkip())

						IF !Empty(SC7->C7_NUMSC)
							nPos := Ascan(aNumSC,{ |x| Alltrim(x[1]) == Alltrim(SC7->C7_NUMSC)})
							IF nPos == 0
								AAdd( aNumSC , {SC7->C7_NUMSC,cW601ENTRE} )
							EndIF
						EndIF

					EndDo

				EndIf

			EndIf

		EndIf

		SW7->(dbSkip())

	EndDo

EndIf

If lLote
	IF Empty(aMsgC7)
		aadd(aMsgC7,"Datas Alteradas dos pedido(s) de Compras vinculado(s) ao(s) processo(s) de Embarque: " + CRLF + "Embarque(s): " + Alltrim(Upper(cW6HAWB)))
	Else
		aMsgC7[1] += ","+  Alltrim(Upper(cW6HAWB))
	Endif
	For ii := 1 To Len(aProcs)
		If len(aMsgC7) == 1
			aadd(aMsgC7,+ CRLF + "Pedido(s): " + Alltrim(aProcs[ii][2]))
		Else
			aMsgC7[2] += ","+ Alltrim(aProcs[ii][2])
		Endif
	Next ii
	If len(aMsgC7) == 2
		aadd(aMsgC7, + CRLF + Replicate("-",50) + CRLF)
	Endif
Else
	cTexto := "PEDIDOS DE COMPRAS vinculados ao processo de Embarque: " + Alltrim(Upper(cW6HAWB)) + CRLF
	cTexto += Replicate("-",50) + CRLF

	For ii := 1 To Len(aProcs)
		cTexto += Alltrim(aProcs[ii][2]) + CRLF
	Next ii

	Aviso("SySmnEnt", cTexto ,{"OK"},3)
Endif

Return


/*
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
??rograma  ?fProcSZE ?Autor ?Irineu Filho ?Data ?03/21/13 ??
??????????????????????????????????
??esc.     ?Preenche os campos da tabela SZE, conforme parame-??
??         ?tros                                              ??
??????????????????????????????????
??????????????????????????????????
??????????????????????????????????
*/
Static Function fProcSZE( cW6HAWB , cW601SEMAN , cW601ENTRE , cTIPOX,lLote,aMsgZE )

Local aProcs	:= {}
Local nExiste	:= 0
Local ii		:= 1
Local cxNum		:= ""
Default lLote	:=	.F.

SZE->(dbSetOrder(4))

If Alltrim(cTIPOX) == "1"
	SW7->(dbSetOrder(1))
	If SW7->(dbSeek( xFilial("SW7") + cW6HAWB ))
		While SW7->(!EOF()) .AND. xFilial("SW7") + cW6HAWB == SW7->W7_FILIAL + SW7->W7_HAWB
			nExiste := Ascan(aProcs,{ |x| Alltrim(x[1]) == Alltrim(SW7->W7_PO_NUM) })
			If nExiste == 0
				Aadd( aProcs , { SW7->W7_PO_NUM } )
				//Caio Pereira
				//Alteracao para filtrar a P.O com base no P.C.
				cxNum		:= SYFILPO(SW7->W7_PO_NUM)
				If SZE->(dbSeek( xFilial("SZE") + cxNum))
					While SZE->(!EOF()) .AND. xFilial("SZE") + cxNum == SZE->ZE_FILIAL + SZE->ZE_NUMERO
						If Reclock( "SZE" , .F. )
							SZE->ZE_SEMANA	:= cW601SEMAN
							SZE->ZE_DTENTRE	:= cW601ENTRE
							SZE->ZE_DTVALID	:= cW601ENTRE + nValid
							MsUnlock()
						EndIf
						SZE->(dbSkip())
					EndDo
				EndIf
				/*
				If SZE->(dbSeek( xFilial("SZE") + "1" + SW7->W7_PO_NUM ))
					While SZE->(!EOF()) .AND. xFilial("SZE") + "1" + SW7->W7_PO_NUM == SZE->ZE_FILIAL + "1" + SZE->ZE_NUMERO
						If Reclock( "SZE" , .F. )
							SZE->ZE_SEMANA	:= cW601SEMAN
							SZE->ZE_DTENTRE	:= cW601ENTRE
							SZE->ZE_DTVALID	:= cW601ENTRE + nValid
							MsUnlock()
						EndIf
						SZE->(dbSkip())
					EndDo
				EndIf
				*/
			EndIf
			SW7->(dbSkip())
		EndDo
	EndIf

	If lLote
		IF Empty(aMsgZE)
			aadd(aMsgZE,"PO(s) vinculado(s) ao(s) processo(s) de Embarque: " + CRLF + "Embarque(s): " + Alltrim(Upper(cW6HAWB)))
		Else
			aMsgZE[1] += "," + Alltrim(Upper(cW6HAWB))
		Endif
		For ii := 1 To Len(aProcs)
			If len(aMsgZE) == 1
				aadd(aMsgZE,+ CRLF +"PO(s): " + Alltrim(aProcs[ii][1]))
			Else
				aMsgZE[2] += ","+ Alltrim(aProcs[ii][1])
			Endif
		Next ii
		If len(aMsgZE) == 2
			aadd(aMsgZE, + CRLF + Replicate("-",50) + CRLF)
		Endif
	Else
		cTexto := "POs vinculados ao processo de Embarque: " + Alltrim(Upper(cW6HAWB)) + CRLF
		cTexto += Replicate("-",50) + CRLF

		For ii := 1 To Len(aProcs)
			cTexto += Alltrim(aProcs[ii][1]) + CRLF
		Next ii

		Aviso("SySmnEnt", cTexto ,{"OK"},3)
	Endif

ElseIf Alltrim(cTIPOX) == "2"
	If SZE->(dbSeek( xFilial("SZE") + cW6HAWB ))
		While SZE->(!EOF()) .AND. xFilial("SZE") + cW6HAWB == SZE->ZE_FILIAL + SZE->ZE_NUMERO
			If Reclock( "SZE" , .F. )
				SZE->ZE_SEMANA	:= cW601SEMAN
				SZE->ZE_DTENTRE	:= cW601ENTRE
				SZE->ZE_DTVALID	:= cW601ENTRE + nValid
				MsUnlock()
			EndIf
			SZE->(dbSkip())
		EndDo
	EndIf
EndIf

Return

/*
???????????????????????????????????????
???????????????????????????????????????
???????????????????????????????????????
??rograma  ?YSMNENT  ?utor  ?aio Pereira        ?Data ? 08/26/13   ??
???????????????????????????????????????
??esc.     ?uncao que filtra numero da p.o com base no pedido de compra??
???????????????????????????????????????
??so       ?AP                                                         ??
???????????????????????????????????????
???????????????????????????????????????
???????????????????????????????????????
*/
Static Function SYFILPO(cNumPo)

	Local cQry:= ""

	cQry+="	SELECT TOP 1 C7_NUM FROM "+RETSQLNAME("SC7")+" SC7"+CRLF
	cQry+="	WHERE C7_FILIAL = '"+xFilial("SC7")+"'"+CRLF
	cQry+="	AND C7_PO_EIC = '"+cNumPo+"'"+CRLF
	cQry+="	AND SC7.D_E_L_E_T_ = ' '"+CRLF

	If Select("TRB2") > 0
		TRB2->(DbCloseArea())
	EndIf

	TcQuery cQry New Alias "TRB2"

Return(TRB2->C7_NUM)

/*
???????????????????????????????????????
???????????????????????????????????????
???????????????????????????????????????
??rograma  ?YSMNENT  ?utor  ?aio Pereira        ?Data ? 09/02/13   ??
???????????????????????????????????????
??esc.     ?uncao que ira gravar data e semana de entrega nas tabelas  ??
??         ?esponsaveis pelo processo.                                 ??
???????????????????????????????????????
??so       ?AP                                                         ??
???????????????????????????????????????
???????????????????????????????????????
???????????????????????????????????????
*/
Static Function SYPRCTABS(cxSemana,dxDtaEntr,cxNumPo, cGet3, aMsgC7, lLote)

	Local cQry		:= ""
	Local aPedidos	:= {}
	Local nX		:= 0
	Local aProcs	:= {}

	Local ii		:= 0
	
	//??????????????????
	//?tualizacao da SW3 - Itens da P.O?
	//??????????????????

	cQry:="	SELECT R_E_C_N_O_ AS W3_RECNO FROM "+RETSQLNAME("SW3")+" SW3"+CRLF
	cQry+="	WHERE W3_FILIAL 	= '"+xFilial("SW3")+"'"+CRLF
	cQry+="	AND W3_PO_NUM		= '"+cxNumPo+"'"+CRLF
	cQry+="	AND SW3.D_E_L_E_T_	= ' '"+CRLF

	If Select("TRB1") > 0
		TRB1->(DbCloseArea())
	Endif

	TcQuery cQry New Alias "TRB1"

	While TRB1->(!Eof())
		SW3->(DbGoTo(TRB1->W3_RECNO))
		RecLock("SW3",.F.)
			SW3->W3_SEMANA	:= cxSemana
			SW3->W3_DT_ENTR	:= dxDtaEntr
		SW3->(MsUnLock())
		TRB1->(DbSkip())
	EndDo

	//????????????????????
	//?tualizacao da SC7 - Pedido de compra?
	//????????????????????

	cQry:="	SELECT R_E_C_N_O_ AS C7_RECNO, C7_NUM FROM "+RETSQLNAME("SC7")+" SC7"+CRLF
	cQry+="	WHERE C7_FILIAL 	= '"+xFilial("SC7")+"'"+CRLF
	cQry+="	AND C7_PO_EIC		= '"+cxNumPo+"'"+CRLF
	cQry+="	AND SC7.D_E_L_E_T_	= ' '"+CRLF

	If Select("TRB1") > 0
		TRB1->(DbCloseArea())
	Endif

	TcQuery cQry New Alias "TRB1"

	While TRB1->(!Eof())

		If Ascan(aPedidos,{|x| Alltrim(x) == Alltrim(TRB1->C7_NUM)}) == 0
			aAdd(aPedidos,TRB1->C7_NUM)
		EndIf

		nExiste := Ascan(aProcs,{ |x| Alltrim(x[1]) == Alltrim(TRB1->C7_NUM) })
		If nExiste == 0
			Aadd( aProcs , { TRB1->C7_NUM } )
		Endif

		SC7->(DbGoTo(TRB1->C7_RECNO))

		U_M030ILOG("PC", SC7->C7_NUM, SC7->C7_ITEM, SC7->C7_PRODUTO, SC7->C7_LOCAL, SC7->C7_01PACKS, SC7->C7_DATPRF, dxDtaEntr, "SYSMNENT", cGet3)

		RecLock("SC7",.F.)
			SC7->C7_01SEMAN	:= cxSemana
			SC7->C7_DATPRF	:= dxDtaEntr
		SC7->(MsUnLock())

		IF !Empty(SC7->C7_NUMSC)
			nPos := Ascan(aNumSC,{ |x| Alltrim(x[1]) == Alltrim(SC7->C7_NUMSC)})
			IF nPos == 0
				AAdd( aNumSC , {SC7->C7_NUMSC,dxDtaEntr} )
			EndIF
		EndIF

		TRB1->(DbSkip())
	EndDo

	If lLote
		IF Empty(aMsgC7)
			aadd(aMsgC7,"Datas Alteradas dos Pedido(s) de Compras vinculado(s) ao(s) processo(s) de PO: " + CRLF + "PO(s): " + Alltrim(Upper(cxNumPo)))
		Else
			aMsgC7[1] += ","+  Alltrim(Upper(cxNumPo))
		Endif
		For ii := 1 To Len(aProcs)
			If len(aMsgC7) == 1
				aadd(aMsgC7,+ CRLF + "Pedido(s): " + Alltrim(aProcs[ii][1]))
			Else
				aMsgC7[2] += ","+ Alltrim(aProcs[ii][1])
			Endif
		Next ii
		If len(aMsgC7) == 2
			aadd(aMsgC7, + CRLF + Replicate("-",50) + CRLF)
		Endif
	EndIf

	//??????????????
	//?tualizacao da SZE - Cotas?
	//??????????????
	For nX:= 1 To Len(aPedidos)

		cQry:="	SELECT R_E_C_N_O_ AS ZE_RECNO FROM "+RETSQLNAME("SZE")+" SZE"+CRLF
		cQry+="	WHERE ZE_FILIAL 	= '"+xFilial("SZE")+"'"+CRLF
		cQry+="	AND ZE_NUMERO		= '"+aPedidos[nX]+"'"+CRLF
		cQry+="	AND SZE.D_E_L_E_T_	= ' '"+CRLF

		If Select("TRB1") > 0
			TRB1->(DbCloseArea())
		Endif

		TcQuery cQry New Alias "TRB1"

		While TRB1->(!Eof())
			SZE->(DbGoTo(TRB1->ZE_RECNO))
			RecLock("SZE",.F.)
				SZE->ZE_SEMANA	:= cxSemana
				SZE->ZE_DTENTRE	:= dxDtaEntr
			SZE->(MsUnLock())
			TRB1->(DbSkip())
		EndDo

	Next nX

Return()
