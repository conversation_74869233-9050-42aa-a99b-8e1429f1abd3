#include "rwmake.ch"

User Function F440DEL

Local cVnd	:= ParamIxb
Local aArea		 := GetArea()

If SE1->E1_TIPO == 'NCC'
	
	aDadosAge:= {}
	AAdd( aDadosAge, { "<PERSON>e", "Nome do Agente" } )
	AAdd( aDadosAge, { SE1->E1_AGE+'-'+SE1->E1_AGELOJA, SE1->E1_NOMAGE } )
	
	aDadosNCC:= {}
	AAdd( aDadosNCC, { "Prefixo","Numero","Parc<PERSON>","Tipo","Emissao","Valor da NCC" } )
	AAdd( aDadosNCC, { SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, SE1->E1_TIPO, Dtoc(SE1->E1_EMISSAO), Transform(SE1->E1_VALOR,"@E 999,999.99")  } )
	
	aDadosCli:= {}
	AAdd( aDadosCli, { "<PERSON><PERSON><PERSON>-Lj","Nome do Cliente" } )
	AAdd( aDadosCli, { SE1->E1_CLIENTE+'-'+SE1->E1_LOJA, SE1->E1_NOMCLI } )
	
	If ! Empty(SE1->E1_AGE)
		aDados  := { aDadosNCC, aDadosCli, aDadosAge }
	Else
		aDados  := { aDadosNCC, aDadosCli }
	Endif
	// encontra-se na funcao FIN460E1
	MenviaMail("S02",aDados,"","","",.T.)
	
Endif

RestArea(aArea)
Return