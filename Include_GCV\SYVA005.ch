#ifdef SPANISH
	#define STR0001"Cantidad entregada"
	#define STR0002"Datos del pedido"
	#define STR0003"Saldo pendiente"
	#define STR0004"Complete los campos del encabezado de esta Fact. de entrada."
	#define STR0005"Este local/sucursal no tiene autorizacion para recibir este pedido, porque este está dirigido al CD."
	#define STR0006"Por favor espere..."
	#define STR0007"Sucursal"
	#define STR0008"Producto"
	#define STR0009"Referencia"
	#define	STR0010"Color"
	#define	STR0011"Descripcion del producto"
	#define	STR0012"Cant.Entregada"
	#define	STR0013"Sald.Pendiente"
	#define	STR0014"Cant. Original"
	#define	STR0015"Prec.Compra"
	#define	STR0016"Fch Emision"
	#define	STR0017"Periodo entrega"
	#define	STR0018"Grilla"
	#define	STR0019"Tamanos"
	#define	STR0020"No existe pedido para el proveedor seleccionado, o para esta tienda."
	#define	STR0021"Anula item [F4]"
	#define	STR0022"Cross Docking [F5]"
	#define	STR0023"ANALITICO"
	#define	STR0024"Definir TES/CFOP [F6]"
	#define	STR0025"Buscar [F7]"
	#define	STR0026"Entrada del pedido de compra con grilla:"
	#define	STR0027"Proveedor:"
	#define	STR0028"Entrada de productos por referencia/color"
	#define	STR0029"Datos del pedido de compra:"
	#define	STR0030"Pedido pendiente"
	#define	STR0031"Parcialmente atendido"
	#define	STR0032"Totalmente atendido"
	#define	STR0033"Fch Entrega invalida"
	#define	STR0034"No se verifico ningun item para realizar la entrada."
	#define	STR0035"¿Desea confirmar las modificaciones y actualizar los datos de la Fact. de entrada?."
	#define	STR0036"Atencion"
	#define	STR0037"Por favor espere..."
	#define	STR0038"Seleccione el pedido de compra"
	#define	STR0039"Nº Pedido de compra:"
	#define	STR0040"RCPJ Proveedor:"
	#define	STR0041"Este pedido no existe."
	#define	STR0042"RCPJ no existe."
	#define	STR0043"DATOS DEL PEDIDO:"
	#define	STR0044"PROVEEDOR:"
	#define	STR0045"CONFIRMA CAMBIO PARA EL PROVEEDOR:"
	#define	STR0046"Error al cambiar el proveedor del pedido."
	#define	STR0047"Val. Unitario R$"
	#define	STR0048"Total"
	#define	STR0049"Pedido no existe para esta tienda"
	#define	STR0050"El periodo para Entrega es de"
	#define	STR0051" a"
	#define	STR0052"MV_01VLDPZ = L - Libera entrada del PC fuera del plazo."
	#define	STR0053"MV_01VLDPZ = B - Bloquea la entrada de PC fuera del plazo."
	#define	STR0054"Este item ya se atendio totalmente."
	#define	STR0055"Datos de la grilla"
	#define	STR0056"Valores"
	#define	STR0057"Informe las cantidades del producto"
	#define	STR0058"No se informo el numero del pedido."
	#define	STR0059"Este pedido no existe."
	#define	STR0060"Seleccione un item verificado para anular."
	#define	STR0061"¿Desea anular la entrada de este item?"
	#define	STR0062"No se permite Cross Docking de items entregados."
	#define	STR0063"¿Desea realizar Cross Docking?"
	#define	STR0064"No se permite clasificar items entregados."
	#define	STR0065"Definicion del TES/CFOP"
	#define	STR0066"¿Desea copiar este TES a?"
	#define	STR0067"Este item"
	#define	STR0068"De este item hacia abajo"
	#define	STR0069"Para todos los items"
	#define	STR0070"1=Producto"
	#define	STR0071"2=Referencia"
	#define	STR0072"3=Descripcion"
	#define	STR0073"1=Contiene la expresion"
	#define	STR0074"2=Inicia con la expresion"
	#define	STR0075"Ubicar"
	#define	STR0076"Espere..."
	#define	STR0077"Ubicando producto..."
	#define	STR0078"Expresion no encontrada"
	#define	STR0079"Salir"
#else
	#ifdef ENGLISH
		#define STR0001"Quantity Deliveried"
		#define STR0002"Order Data"
		#define STR0003"Pending Balance"
		#define STR0004"Fillin the header fields of the Inflow Invoice."
		#define STR0005"This place/branch not allowed to receive this order, as it was directed to CD."
		#define STR0006"Please, wait..."
		#define STR0007"Branch"
		#define STR0008"Product"
		#define STR0009"Reference"
		#define STR0010"Colour"
		#define STR0011"Product Description"
		#define STR0012"Qtty.Delivered"
		#define STR0013"Pending Balance"
		#define STR0014"Source Quantity"
		#define STR0015"Purchase Price"
		#define STR0016"Issue Date"
		#define STR0017"Delivery Period"
		#define STR0018"Grid"
		#define STR0019"Sizes"
		#define STR0020"There is no order for the selected Supplier or Unit."
		#define STR0021"CancelItem [F4]"
		#define STR0022"Cross Docking [F5]"
		#define STR0023"DETAILED"
		#define STR0024"Define TES/CFOP [F6]"
		#define STR0025"Search [F7]"
		#define STR0026"Purchase Order Inflow with Grid: "
		#define STR0027"Supplier:"
		#define STR0028"Product Inflow by Reference/Colour"
		#define STR0029"Purchase Order Data:"
		#define STR0030"Open Order"
		#define STR0031"Partially Supported"
		#define STR0032"Fully Supported"
		#define STR0033"Invalid Delivery Dt."
		#define STR0034"No item checked to perform Inflow."
		#define STR0035"Do you want to confirm inflow invoice editions and updates?"
		#define STR0036"Attention"
		#define STR0037"Please, wait..."
		#define STR0038"Select the purchase order."
		#define STR0039"Purchase Order No.: "
		#define STR0040"Suppliers CNPJ: "
		#define STR0041"This order does not exist."
		#define STR0042"CNPJ does not exist."
		#define STR0043"ORDER INFORMATION:"
		#define STR0044"SUPPLIER"
		#define STR0045"CONFIRM CHANGE TO SUPPLIER:"
		#define STR0046"Error while changing order supplier."
		#define STR0047"Unit Value R$"
		#define STR0048"Total"
		#define STR0049"Order does not exist for this unit."
		#define STR0050"Period for delivery is from"
		#define STR0051"to"
		#define STR0052"MV_01VLDPZ = L - Releases the PO inflow out of the term."
		#define STR0053"MV_01VLDPZ = B - Blocks the PO inflow out of the term."
		#define STR0054"Item fully supported already."
		#define STR0055"Grid Data"
		#define STR0056"Values"
		#define STR0057"Enter the product quantity."
		#define STR0058"Order number not informed."
		#define STR0059"This order does not exist."
		#define STR0060"Select na item already checked for cancelation."
		#define STR0061"Do you want to cancel this item inflow?"
		#define STR0062"Not allowed Cross Docking of items already deliveried."
		#define STR0063"Do you want to perform Cross Docking?"
		#define STR0064"Not allowed to classify items already deliveried."
		#define STR0065"TIO/CFOP Definition"
		#define STR0066"Do you want to double this TIO to?"
		#define STR0067"This item"
		#define STR0068"From this item down"
		#define STR0069"For all items."
		#define STR0070"1=Product"
		#define STR0071"2=Reference"
		#define STR0072"3=Description"
		#define STR0073"1=With the Expression"
		#define STR0074"2=Begin with the Expression"
		#define STR0075"Find"
		#define STR0076"Wait..."
		#define STR0077"Finding product..."
		#define STR0078"Expression not found."
		#define STR0079"Exit"
	#else
		#define STR0001"Quantidade Entregue"
		#define STR0002"Dados do Pedido"
		#define STR0003"Saldo Pendente"
		#define STR0004"Preencha os campos do cabecalho desta NF de Entrada."
		#define STR0005"Este local/filial nao tem permissao para receber este pedido, pois o mesmo foi direcionado para o CD."
		#define STR0006"Por favor aguarde..."
		#define STR0007"Filial"
		#define STR0008"Produto"
		#define STR0009"Referencia"
		#define STR0010"Cor"
		#define STR0011"Descricao do Produto"
		#define STR0012"Qtd.Entregue"
		#define STR0013"Sld.Pendente"
		#define STR0014"Qtd.Original"
		#define STR0015"Prc.Compra"
		#define STR0016"Dt Emissao"
		#define STR0017"Periodo Entrega"
		#define STR0018"Grade"
		#define STR0019"Tamanhos"
		#define STR0020"Nao existe pedido para o Fornecedor selecionado, ou para esta loja."
		#define STR0021"Cancelar Item [F4]"
		#define STR0022"Cross Docking [F5]"
		#define STR0023"ANALITICO"
		#define STR0024"Definir TES/CFOP [F6]"
		#define STR0025"Pesquisar [F7]"
		#define STR0026"Entrada do Pedido de Compra com Grade:"
		#define STR0027"Fornecedor:"
		#define STR0028"Entrada de Produtos por Referencia/Cor"
		#define STR0029"Dados do Pedido de Compra:"
		#define STR0030"Pedido em Aberto"
		#define STR0031"Parcialmente Atendido"
		#define STR0032"Totalmente Atendido"
		#define STR0033"Dt Entrega Invalida"
		#define STR0034"Nenhum item foi conferido para realizar a Entrada."
		#define STR0035"Deseja Confirma as alteracoes e Atualizar os dados da Nota Fiscal de Entrada ?."
		#define STR0036"Atencao"
		#define STR0037"Por favor aguarde..."
		#define STR0038"Selecione o Pedido de Compra"
		#define STR0039"No. Pedido de Compra:"
		#define STR0040"CNPJ Fornecedor:"
		#define STR0041"Este pedido nao existe."
		#define STR0042"CNPJ nao existe."
		#define STR0043"DADOS DO PEDIDO:"
		#define STR0044"FORNECEDOR:"
		#define STR0045"CONFIRMA TROCA PARA O FORNECEDOR:"
		#define STR0046"Erro ao trocar o fornecedor do Pedido."
		#define STR0047"Vlr. Unitario R$"
		#define STR0048"Total"
		#define STR0049"Pedido nao existe para esta loja"
		#define STR0050"O periodo para Entrega e de"
		#define STR0051" ate"
		#define STR0052"MV_01VLDPZ = L - Libera entrada do PC fora do prazo."
		#define STR0053"MV_01VLDPZ = B - Bloqueia a entrada de PC fora do prazo."
		#define STR0054"Este item ja foi totalmente atendido."
		#define STR0055"Dados da Grade"
		#define STR0056"Valores"
		#define STR0057"Informe as Quantidades do Produto"
		#define STR0058"O Numero do Pedido nao foi informado."
		#define STR0059"Este pedido nao existe."
		#define STR0060"Selecione um item ja conferido para cancelar."
		#define STR0061"Deseja cancelar a entrada deste item?"
		#define STR0062"Nao e permite Cross Docking de itens ja entregues."
		#define STR0063"Deseja realizar Cross Docking?"
		#define STR0064"Nao e permite classificar itens ja entregues."
		#define STR0065"Definicao do TES/CFOP"
		#define STR0066"Deseja replicar este TES para ?"
		#define STR0067"Este Item"
		#define STR0068"Deste Item para Baixo"
		#define STR0069"Para todos os Itens"
		#define STR0070"1=Produto"
		#define STR0071"2=Referencia"
		#define STR0072"3=Descricao"
		#define STR0073"1=Contem a Expressao"
		#define STR0074"2=Inicia com a Expressao"
		#define STR0075"Localizar"
		#define STR0076"Aguarde..."
		#define STR0077"Localizando produto..."
		#define STR0078"Expressao nao encontrada"
		#define STR0079"Sair"
	#endif
#endif
