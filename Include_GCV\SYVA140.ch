#ifdef SPANISH
	#define STR0001"Mantenimiento de precio"
	#define STR0002"Sin autorizacion"
	#define STR0003"Fecha de la programacion en agenda"
	#define STR0004"Informe los parametros"
	#define STR0005"Movimiento de producto [F4]"
	#define STR0006"Historial [F6]"
	#define STR0007"Productos"
	#define STR0008"Excepcion de precios / Precios por tienda"
	#define STR0009"TIENDAS"
	#define	STR0010"Por favor espere, actualizando el mantenimiento de precio"
	#define	STR0011"Es obligatorio informar el valor."
	#define	STR0012"Es obligatorio informar el solicitante."
	#define	STR0013"Es obligatorio informar el motivo."
	#define	STR0014"¿Confirma la inclusion del mantenimiento del precio?"
	#define	STR0015"Por favor espere, haciendo efectivo mantenimiento de precio"
	#define	STR0016"Operacion"
	#define	STR0017"Producto"
	#define	STR0018"Descripcion  "
	#define	STR0019"Valor actual"
	#define	STR0020"Valor nuevo"
	#define	STR0021"Tiendas"
	#define	STR0022"Solicitante"
	#define	STR0023"Nombre"
	#define	STR0024"Motivo"
	#define	STR0025"Descripcion"
	#define	STR0026"Tienda"
	#define	STR0027"Valor"
#else
	#ifdef ENGLISH
		#define STR0001"Price Maintenance"
		#define STR0002"No Permission"
		#define STR0003"Schedule Date"
		#define STR0004"Enter Parameters"
		#define STR0005"Product Movement [F4]"
		#define STR0006"History [F6]"
		#define STR0007"Products"
		#define STR0008"Price Exception / Store Prices"
		#define STR0009"STORES"
		#define STR0010"Please wait, updating price maintenance"
		#define STR0011"and require fill out value."
		#define STR0012"and require fill out requester."
		#define STR0013"and require fill out reason."
		#define STR0014"Confirm add price maintenance?"
		#define STR0015"Please wait, running price maintenance"
		#define STR0016"Operation"
		#define STR0017"Product"
		#define STR0018"Description"
		#define STR0019"Current Value"
		#define STR0020"New Value"
		#define STR0021"Stores"
		#define STR0022"Requester"
		#define STR0023"Name"
		#define STR0024"Reason"
		#define STR0025"Description"
		#define STR0026"Store"
		#define STR0027"Value"
	#else
		#define STR0001"Manutencao de Preco"
		#define STR0002"Sem Permissao"
		#define STR0003"Data de Agendamento"
		#define STR0004"Informe os Parametros"
		#define STR0005"Movimentacao de Produto [F4]"
		#define STR0006"Historico [F6]"
		#define STR0007"Produtos"
		#define STR0008"Excecao de Precos / Precos por Loja"
		#define STR0009"LOJAS"
		#define STR0010"Por favor aguarde, atualizando manutencao de preco"
		#define STR0011"e obrigatotio o preenchimento do valor."
		#define STR0012"e obrigatotio o preenchimento do solicitante."
		#define STR0013"e obrigatotio o preenchimento do motivo."
		#define STR0014"Confirma a inclusao da manutencao de preco ?"
		#define STR0015"Por favor aguarde, efetivando manutencao de preco"
		#define STR0016"Operacao"
		#define STR0017"Produto"
		#define STR0018"Descricao"
		#define STR0019"Valor Atual"
		#define STR0020"Valor Novo"
		#define STR0021"Lojas"
		#define STR0022"Solicitante"
		#define STR0023"Nome"
		#define STR0024"Motivo"
		#define STR0025"Descricao"
		#define STR0026"Loja"
		#define STR0027"Valor"
		#define STR0028 "Por favor, informe o comprador."
		#define STR0029 "Por favor, informe o motivo."
		#define STR0030 "Por favor, informe a data de agendamento."
		#define STR0031 "A data de agendamento nao pode ser menor a data do dia."
	#endif
#endif
