#include 'protheus.ch'
#include 'parmtype.ch'
#include 'FWMVCDEF.ch'

user function Atv01()

	Local oBrowse	 := FWMBrowse():New()

	//Nome da tela
	oBrowse:SetAlias("ZA1")
	oBrowse:SetDescription("Cadastro de musicas")

	//Defini��o das legendas
	oBrowse:AddLegend("ZA1_GENERO == 'R'","BR_PRETO ","ROCK")
	oBrowse:AddLegend("ZA1_GENERO == 'I'","BR_VIOLETA ","INDIE")
	oBrowse:AddLegend("ZA1_GENERO == 'F'","BR_PINK ","FUNK")

	oBrowse:Activate()

Return

Static function MenuDef 

	Local aRotina := {}

	ADD OPTION aRotina TITLE "Incluir" 		ACTION "ViewDef.Atv01" OPERATION MODEL_OPERATION_INSERT ACCESS 0 
	ADD OPTION aRotina TITLE "Alterar" 		ACTION "ViewDef.Atv01" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
	ADD OPTION aRotina TITLE "Excluir" 		ACTION "ViewDef.Atv01" OPERATION MODEL_OPERATION_DELETE ACCESS 0
	ADD OPTION aRotina TITLE "Visualizar" 	ACTION "ViewDef.Atv01" OPERATION MODEL_OPERATION_VIEW ACCESS 0

return aRotina 
	


Static function ModelDef()

	Local oModel 	:= MPFormModel():New("Atv01") //Criar modelo com o nome do fonte somente quando for Function 
	Local oStrZA1 	:= FWFormStruct(1,"ZA1") //Cria a estrutura da tabela. Quando modelo primeiro parametro, quando view 2

	oModel:SetDescription("Musicas") // Descri��o do model

	oModel:AddFields("ZA1_Form", ,oStrZA1) //Cria Formulario 	

Return oModel

Static Function ViewDef()

	Local oView 	:= FWFormVIew():New() 
	Local oStrZa1 	:= FWFormStruct(2, "ZA1")//Cria a estrutura da tabela. Quando modelo primeiro parametro, quando view 2
	Local oModel 	:= ModelDef() //Atribui Modeldef na view
	
	oView:SetModel(oModel) //atribui view no modeldef
	
	oView:AddField("FormZA1", oStrZa1, "ZA1_Form") //liko/Atribui o formulario a view e a estrutura para montar a tela
	oView:CreateHorizontalBox("Box_ZA1", 100) //Caixa box criada na view. 
	
	oView:SetOwnerView("FormZA1", "Box_ZA1") //Exibir a caixa box criada na view
	
	
Return oView
