<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
	<fileVersion appName="xl" lastEdited="7" lowestEdited="7" rupBuild="17927"/>
	<workbookPr defaultThemeVersion="124226"/>
	<bookViews>
		<workbookView xWindow="240" yWindow="135" windowWidth="20115" windowHeight="8250"/>
	</bookViews>
	<sheets>
		<%For nCont:=1 to Len(oSelf:aPlanilhas)%>
			<%=oSelf:aPlanilhas[nCont]:GetTag()%>
	<%Next%></sheets>
	<%=If(!Empty(oSelf:odefinedNames:GetValor()),oSelf:odefinedNames:GetTag(),chr(13)+chr(10))%><calcPr calcId="145621"/>
</workbook>