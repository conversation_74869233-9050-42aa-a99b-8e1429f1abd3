<EASYLINK>
<SERVICE>
<ID>004</ID>
<DATA_SELECTION>
   <FIN_NUM>EEQ->EEQ_FINNUM</FIN_NUM>
   <FIN_SEND>
      <FIN_IT>
         <FIN_ELE1>'E1_NUM'</FIN_ELE1>
         <E1_NUM>AvKey(#TAG FIN_NUM#,"E1_NUM")</E1_NUM>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_PREFIXO'</FIN_ELE1>
         <E1_PREFIXO>Avkey(cModulo,"E1_PREFIXO")</E1_PREFIXO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_PARCELA'</FIN_ELE1>
         <E1_PARCELA>EECGetFinParc(EEQ->EEQ_PARC)</E1_PARCELA>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_TIPO'</FIN_ELE1>
         <E1_TIPO>AvKey("NF","E1_TIPO")</E1_TIPO>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_CLIENTE'</FIN_ELE1>
         <E1_CLIENTE>AvKey(EEQ->EEQ_IMPORT,"E1_CLIENTE")</E1_CLIENTE>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
      <FIN_IT>
         <FIN_ELE1>'E1_LOJA'</FIN_ELE1>
         <E1_LOJA>AvKey(EEQ->EEQ_IMLOJA,"E1_LOJA")</E1_LOJA>
         <FIN_ELE3>''</FIN_ELE3>
      </FIN_IT>
   </FIN_SEND>
</DATA_SELECTION>

<DATA_SEND>
	<SEND>EECINFIN(#TAG FIN_SEND#, 'SE1', 'EXCLUIR')</SEND>
   <CMD>EEQ->(RECLOCK("EEQ",.F.))</CMD>
   <CMD>EEQ->EEQ_FINNUM := Space(AvSx3('EEQ_FINNUM', 3))</CMD>
</DATA_SEND>

<DATA_RECEIVE>
   <SRV_STATUS>.T.</SRV_STATUS>
</DATA_RECEIVE>
</SERVICE>
</EASYLINK>
