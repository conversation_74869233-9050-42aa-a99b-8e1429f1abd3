#include "Tbiconn.ch"
#include "topconn.ch"
#include "rwmake.ch"


User Function FA060Qry()
 
 Local cRet 		:= ""
 Local cCliente 	:= ""
 Local cConsulta 	:= ""

 cConsulta := " SELECT A1_COD
 cConsulta += " FROM " + RetSqlName("SA1") + " SA1"
 cConsulta += " WHERE A1_BCO1 = '999' "
 cConsulta += " AND D_E_L_E_T_ = '' "
 
 TcQuery cConsulta New Alias '_cQRY'

 

 Do While !_cQRY->(Eof())
 cCliente += "'" + _cQRY->A1_COD + "',"
 _cQRY->(dbSkip())
 EndDo

 _cQRY->(dbCloseArea())

 If !Empty(cCliente)
 cCliente := SubString(cCliente,1,LEN(cCliente) - 1)
 cRet := " E1_CLIENTE  NOT IN ("+ cCliente+ ") "
 EndIf

 Return cRet 