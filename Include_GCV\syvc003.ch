#ifdef SPANISH
	#define STR0001"Calculando movimientos del producto..."
	#define STR0002"Pedidos"
	#define STR0003"Transferencia"
	#define STR0004"Mant.Foto"
	#define STR0005"Ventas por semana"
	#define STR0006"Grilla"
	#define STR0007"Tienda vs. Grilla"
	#define STR0008"Precios"
	#define STR0009"Asignacion"
	#define	STR0010"Transf.Productos"
	#define	STR0011"Vis.Fotos"
	#define	STR0012"Verificando historial de pedidos..."
	#define	STR0013"Verificando transferencias..."
	#define	STR0014"Montando panel de mantenimiento de fotos..."
	#define	STR0015"Generando ventas por semana..."
	#define	STR0016"Generando ventas por grilla..."
	#define	STR0017"Generando informe de stock por grilla..."
	#define	STR0018"Consultando precios..."
	#define	STR0019"Verificando asignacion..."
	#define	STR0020"Verificando transferencias..."
	#define	STR0021"Montando panel de fotos..."
	#define	STR0022"Calculando movimientos del producto..."
	#define	STR0023"Historial del producto"
	#define	STR0024"Informacion sobre el producto"
	#define	STR0025"Informacion totalizada"
	#define	STR0026"Desempeno por tienda"
	#define	STR0027"De:"
	#define	STR0028"A:"
	#define	STR0029"Producto:"
	#define	STR0030"OK"
	#define	STR0031"Calculando movimientos del producto..."
	#define	STR0032"Ult.Prec.Compra"
	#define	STR0033"Costo medio"
	#define	STR0034"Precio"
	#define	STR0035"Ult.Compra"
	#define	STR0036"Stock medio"
	#define	STR0037"Giro"
	#define	STR0038"MkpVV"
	#define	STR0039"MkpVC"
	#define	STR0040"Indice"
	#define	STR0041"Fch.Registro"
	#define	STR0042"Marca"
	#define	STR0043"Coleccion"
	#define	STR0044"Referencia:"
	#define	STR0045"Nombre"
	#define	STR0046"Stock Ini(C)"
	#define	STR0047"Compras(C)"
	#define	STR0048"Otr.Ent.(C)"
	#define	STR0049"Ventas(C)"
	#define	STR0050"Cambios(C)"
	#define	STR0051"Otr.Sal.(C)"
	#define	STR0052"Stock Fin.(C)"
	#define	STR0053"Ult.Entrada"
	#define	STR0054"Vta.Per.Ant(C)"
	#define	STR0055"Cartera(C)"
	#define	STR0056"Cobertura(dias)"
	#define	STR0057"Giro"
	#define	STR0058"Stock Ini($)"
	#define	STR0059"Compras($)"
	#define	STR0060"Otr.Ent.($)"
	#define	STR0061"Costo venta($)"
	#define	STR0062"Cambios($)"
	#define	STR0063"Otr.Sal.($)"
	#define	STR0064"Stock Fin($)"
	#define	STR0065"CostoVent.Ant.($)"
	#define	STR0066"Cartera($)"
	#define	STR0067"Tienda"
	#define	STR0068"Sin referencia para el producto"
	#define	STR0069"Nombre"
	#define	STR0070"Cant."
	#define	STR0071"Costo"
	#define	STR0072"Stock inicial"
	#define	STR0073"Compras"
	#define	STR0074"Otras entradas"
	#define	STR0075"Ventas en el periodo"
	#define	STR0076"Cambios en el periodo"
	#define	STR0077"Otras salidas"
	#define	STR0078"Stock final"
	#define	STR0079"Stock con defecto"
	#define	STR0080"Ventas Per.Ant"
	#define	STR0081"Cartera actual"
	#define	STR0082"Cobertura (dias)"
#else
	#ifdef ENGLISH
		#define STR0001"Calculating Product Movements..."
		#define STR0002"Orders"
		#define STR0003"Transfer"
		#define STR0004"Photo.Maint"
		#define STR0005"Sales by Week"
		#define STR0006"Grid"
		#define STR0007"Store X Grid"
		#define STR0008"Prices"
		#define STR0009"Allocation"
		#define STR0010"Product.Transf"
		#define STR0011"View.Photos"
		#define STR0012"Verifying Order History..."
		#define STR0013"Verifying Transfers..."
		#define STR0014"Building Photo Panel Maintenance..."
		#define STR0015"Generating Sales by Week..."
		#define STR0016"Generating Sales by Grid..."
		#define STR0017"Generating Stock Report by Grid..."
		#define STR0018"Checking Prices..."
		#define STR0019"Verifying Allocation..."
		#define STR0020"Verifying Transfers..."
		#define STR0021"Building Photo Panel Maintenance..."
		#define STR0022"Calculating Product Movements..."
		#define STR0023"Product History"
		#define STR0024"Product Information"
		#define STR0025"Totalized Information"
		#define STR0026"Performance by Store"
		#define STR0027"From:"
		#define STR0028"To:"
		#define STR0029"Product:"
		#define STR0030"OK"
		#define STR0031"Calculating Product Movements..."
		#define STR0032"Last.Purch.Price"
		#define STR0033"Average Cost"
		#define STR0034"Price"
		#define STR0035"Last.Purchase"
		#define STR0036"Average.Stk"
		#define STR0037"Turn"
		#define STR0038"MkpVV"
		#define STR0039"MkpVC"
		#define STR0040"Index"
		#define STR0041"Register.Dt"
		#define STR0042"Brand"
		#define STR0043"Collection"
		#define STR0044"Reference:"
		#define STR0045"Name:"
		#define STR0046"Est.Ini(Q)"
		#define STR0047"Purchases(Q)"
		#define STR0048"Other.Ent.(Q)"
		#define STR0049"Sales(Q)"
		#define STR0050"Exchanges(Q)"
		#define STR0051"Other.Ex.(Q)"
		#define STR0052"Est.End(Q)"
		#define STR0053"Last Entry"
		#define STR0054"Sl.Prev.Per(Q)"
		#define STR0055"Portfolio(Q)"
		#define STR0056"Coverage(days)"
		#define STR0057"Turn"
		#define STR0058"St.Stk($)"
		#define STR0059"Purchases($)"
		#define STR0060"Oth.Ent.($)"
		#define STR0061"Sales Cost($)"
		#define STR0062"Exchanges($)"
		#define STR0063"Oth.Exit.($)"
		#define STR0064"End.Stk($)"
		#define STR0065"SalesCost.Prev.($)"
		#define STR0066"Portfolio($)"
		#define STR0067"Store"
		#define STR0068"No reference for the product"
		#define STR0069"Name:"
		#define STR0070"Qtty."
		#define STR0071"Cost"
		#define STR0072"Start Stock"
		#define STR0073"Purchases"
		#define STR0074"Other Entries"
		#define STR0075"Sales on Period"
		#define STR0076"Exchanges on Period"
		#define STR0077"Other Exits"
		#define STR0078"End Stock"
		#define STR0079"Defect Stock"
		#define STR0080"Sales Per.Prev"
		#define STR0081"Current Portfolio"
		#define STR0082"Coverage (days)"
	#else
		#define STR0001"Calculando Movimentos do Produto..."
		#define STR0002"Pedidos"
		#define STR0003"Transferencia"
		#define STR0004"Manut.Foto"
		#define STR0005"Vendas por Semana"
		#define STR0006"Grade"
		#define STR0007"Loja X Grade"
		#define STR0008"Precos"
		#define STR0009"Alocacao"
		#define STR0010"Transf.Produtos"
		#define STR0011"Vis.Fotos"
		#define STR0012"Verificando Historico de Pedidos..."
		#define STR0013"Verificando Transferencias..."
		#define STR0014"Montando Painel de Manutencao de Fotos..."
		#define STR0015"Gerando Vendas por Semana..."
		#define STR0016"Gerando Vendas por Grade..."
		#define STR0017"Gerando Relatorio Estoque por Grade..."
		#define STR0018"Consultando Precos..."
		#define STR0019"Verificando Alocacao..."
		#define STR0020"Verificando Transferencias..."
		#define STR0021"Montando Painel de Fotos..."
		#define STR0022"Calculando Movimentos do Produto..."
		#define STR0023"Historico do Produto"
		#define STR0024"Informacoes Sobre o Produto"
		#define STR0025"Informacoes Totalizadas"
		#define STR0026"Desempenho por Loja"
		#define STR0027"De:"
		#define STR0028"Ate:"
		#define STR0029"Produto:"
		#define STR0030"OK"
		#define STR0031"Calculando Movimentos do Produto..."
		#define STR0032"Ult.Prc.Compra"
		#define STR0033"Custo Medio"
		#define STR0034"Preco"
		#define STR0035"Ult.Compra"
		#define STR0036"Est.Medio"
		#define STR0037"Giro"
		#define STR0038"MkpVV"
		#define STR0039"MkpVC"
		#define STR0040"Indice"
		#define STR0041"Dt.Cadastro"
		#define STR0042"Marca"
		#define STR0043"Colecao"
		#define STR0044"Referencia:"
		#define STR0045"Nome"
		#define STR0046"Est.Ini(Q)"
		#define STR0047"Compras(Q)"
 		#define STR0048"Out.Ent.(Q)"
		#define STR0049"Vendas(Q)"
		#define STR0050"Trocas(Q)"
		#define STR0051"Out.Sai.(Q)"
		#define STR0052"Est.Fim(Q)"
		#define STR0053"Ult.Entrada"
		#define STR0054"Vd.Per.Ant(Q)"
		#define STR0055"Carteira(Q)"
		#define STR0056"Cobertura(dias)"
		#define STR0057"Giro"
		#define STR0058"Est.Ini($)"
		#define STR0059"Compras($)"
		#define STR0060"Out.Ent.($)"
		#define STR0061"Custo Venda($)"
		#define STR0062"Trocas($)"
		#define STR0063"Out.Sai.($)"
		#define STR0064"Est.Fim($)"
		#define STR0065"CustoVend.Ant.($)"
		#define STR0066"Carteira($)"
		#define STR0067"Loja"
		#define STR0068"Sem Referencia para o produto"
		#define STR0069"Nome"
		#define STR0070"Qtd."
		#define STR0071"Custo"
		#define STR0072"Estoque Inicial"
		#define STR0073"Compras"
		#define STR0074"Outras Entradas"
		#define STR0075"Vendas no Periodo"
		#define STR0076"Trocas no Periodo"
		#define STR0077"Outras Saidas"
		#define STR0078"Estoque Final"
		#define STR0079"Estoque Defeito"
		#define STR0080"Vendas Per.Ant"
		#define STR0081"Carteira Atual"
		#define STR0082"Cobertura (dias)"
		#define STR0083"Markup"
		#define STR0084"% Markup"
	#endif
#endif