#ifdef SPANISH
	#define STR0001"Estructura de categorias"
	#define STR0002"Incluir (F5)"
	#define STR0003"<PERSON><PERSON><PERSON> (F6)"
	#define STR0004"Incluir subcategoria"
	#define STR0005"Categoria:"
	#define STR0006"Subcategoria:"
	#define STR0007"&Confirmar"
	#define STR0008"&Finalizar"
	#define STR0009"No es posible incluir esta categoria porque se creara una referencia circular."
	#define	STR0010"Atencion"
	#define	STR0011"Esta subcategoria ya esta registrada para la categoria."
	#define	STR0012"Informe una categoria valida."
	#define	STR0013"Esta categoria tiene subcategorias vinculadas y al borrarla tambien se borraran sus subcategorias."
	#define	STR0014"¿Confirma el borrado?"
	#define	STR0015"Aviso."
	#define	STR0016"¿Confirma el borrado de la categoria?"
	#define	STR0017"Espere... Borrando la categoria y subcategorias..."
	#define	STR0018"Esta categoria tiene subcategorias vinculadas, por lo tanto no puede borrarse."
	#define	STR0019"¡Esta estructura de categoria ya existe!"
	#define	STR0020"¡Esta estructura tiene una referencia circular!"
#else
	#ifdef ENGLISH
		#define STR0001"Category Structure"
		#define STR0002"Add (F5)"
		#define STR0003"Delete (F6)"
		#define STR0004"Add subcategory"
		#define STR0005"Category:"
		#define STR0006"Subcategory"
		#define STR0007"&Confirm"
		#define STR0008"&Close"
		#define STR0009"Unable to add this category, as there will be a circular reference!"
		#define STR0010"Attention!"
		#define STR0011"This subcategory already registered for the category!"
		#define STR0012"Enter a valid category!"
		#define STR0013"This category has linked subcategories and if deleted, all subcategories will be deleted too!"
		#define STR0014"Do you confirm deletion?"
		#define STR0015"Warning!"
		#define STR0016"Do you confirm category deletion?"
		#define STR0017"Wait... Deleting categories and subcategories..."
		#define STR0018"This category has linked subcategories, then it cannot be deleted!"
		#define STR0019"This category structure already exists!"
		#define STR0020"This strucutre has a circular reference!"
	#else
		#define STR0001"Estrutura de Categorias"
		#define STR0002"Incluir (F5)"
		#define STR0003"Excluir (F6)"
		#define STR0004"Incluir Sub-Categoria"
		#define STR0005"Categoria:"
		#define STR0006"Sub-Categoria:"
		#define STR0007"&Confirmar"
		#define STR0008"&Fechar"
		#define STR0009"Nao e possivel incluir esta categoria pois ira criar uma referencia circular!"
		#define STR0010"Atencao!"
		#define STR0011"Essa sub-categoria ja esta cadastrada para a categoria!"
		#define STR0012"Informe uma categoria valida!"
		#define STR0013"Esta categoria possui sub-categorias relacionadas e ao exclui-la suas sub-categorias tambem serao excluidas!"
		#define STR0014"Confirma a exclusao?"
		#define STR0015"Aviso!"
		#define STR0016"Confirma a exclusao da categoria?"
		#define STR0017"Aguarde... Excluindo categoria e sub-categorias..."
		#define STR0018"Esta categoria possui sub-categorias amarradas, e portanto nao pode ser excluida!"
		#define STR0019"Esta estrutura de categoria ja existe!"
		#define STR0020"Esta estrutura possui uma referencia circular!"
	#endif
#endif