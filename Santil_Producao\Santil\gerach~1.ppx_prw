#line 1 "C:\SMARTC~2\INCLUDE\protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\Dialog.ch"
#line 28 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\Font.ch"
#line 29 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\PTMenu.ch"
#line 31 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\Print.ch"
#line 33 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\Colors.ch"
#line 35 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\Folder.ch"
#line 37 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\msobject.ch"
#line 38 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\VKey.ch"
#line 42 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\WinApi.ch"
#line 44 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\FWCommand.ch"
#line 47 "protheus.ch"
#line 1 "C:\SMARTC~2\INCLUDE\FWCSS.CH"
#line 50 "protheus.ch"
#line 2 "\\192.168.6.102\e\fonteswc\santil\GERACH~1.PRW"
#line 1 "C:\SMARTC~2\INCLUDE\rwmake.ch"
#line 1 "C:\SMARTC~2\INCLUDE\stdwin.ch"
#line 14 "rwmake.ch"
#line 3 "\\192.168.6.102\e\fonteswc\santil\GERACH~1.PRW"
#line 1 "C:\SMARTC~2\INCLUDE\TopConn.ch"
#line 4 "\\192.168.6.102\e\fonteswc\santil\GERACH~1.PRW"
#line 1 "C:\SMARTC~2\INCLUDE\report.ch"
#line 6 "\\192.168.6.102\e\fonteswc\santil\GERACH~1.PRW"




















Function U_gerachave()


local cChave :=""

Local aUF     	:= {}
Private cPerg			:= "ACPISCOFIS"

IF !PERGUNTE(cPerg, .t. )
	RETURN
ENDIF
Processa({|| CHAVEATU(MV_PAR01,MV_PAR02,MV_PAR03,MV_PAR04) }," - GERANDO CHAVE NFE..........."+SUBSTR(DTOS(MV_PAR02),1,4)+"/"+SUBSTR(DTOS(MV_PAR02),5,2)+"/"+SUBSTR(DTOS(MV_PAR02),7,2))





Aviso("Finalizado","Gera Chave processado com sucesso.",{"Ok"})

return()
STATIC FUNCTION CHAVEATU(ddata01,ddata02,cfilde,cfilate)
Local aUF     	:= {}
Local cCNPJ     :=""
Local cEst     :=""
nHdl		:= 0
lContinua	:= .T. 



aadd(aUF,{"RO","11"})
aadd(aUF,{"AC","12"})
aadd(aUF,{"AM","13"})
aadd(aUF,{"RR","14"})
aadd(aUF,{"PA","15"})
aadd(aUF,{"AP","16"})
aadd(aUF,{"TO","17"})
aadd(aUF,{"MA","21"})
aadd(aUF,{"PI","22"})
aadd(aUF,{"CE","23"})
aadd(aUF,{"RN","24"})
aadd(aUF,{"PB","25"})
aadd(aUF,{"PE","26"})
aadd(aUF,{"AL","27"})
aadd(aUF,{"MG","31"})
aadd(aUF,{"ES","32"})
aadd(aUF,{"RJ","33"})
aadd(aUF,{"SP","35"})
aadd(aUF,{"PR","41"})
aadd(aUF,{"SC","42"})
aadd(aUF,{"RS","43"})
aadd(aUF,{"MS","50"})
aadd(aUF,{"MT","51"})
aadd(aUF,{"GO","52"})
aadd(aUF,{"DF","53"})
aadd(aUF,{"SE","28"})
aadd(aUF,{"BA","29"})
aadd(aUF,{"EX","99"})

If Select("QRY") > 0
	QRY->(DbCloseArea())
EndIf
cdoc:=alltrim(MV_PAR05)















































cQvy := " " +chr(13)+chr(10)
cQvy += " SELECT R_E_C_N_O_ FTRECNO " +chr(13)+chr(10)
cQvy += " FROM "+RetSqlName("SFT")+" SFT " +chr(13)+chr(10)
cQvy += " WHERE FT_ENTRADA BETWEEN '"+DTOS(ddata01)+"' AND '"+DTOS(ddata02)+"'" +chr(13)+chr(10)
cQvy += " AND SFT.D_E_L_E_T_='' AND FT_ESPECIE IN ('CTE','NFE','SPED') " +chr(13)+chr(10)
cQvy += " AND FT_CHVNFE=''" +chr(13)+chr(10)
cQvy += " and  length(rtrim(ft_nfiscal))>6 " +chr(13)+chr(10)

cQvy += " AND FT_FILIAL BETWEEN '"+cfilde+"' AND  '"+cfilate+"' " +chr(13)+chr(10)
cQvy += " order by R_E_C_N_O_ " +chr(13)+chr(10)



DBUseArea( .T. ,"TOPCONN", TCGenQry(,,cQvy),"QRY", .F. , .T. )
dbSelectArea("SA2")
dbSetOrder(1)
dbSelectArea("SA1")
dbSetOrder(1)

DbSelectArea("QRY")
QRY->(DBGOTOP())
dbSelectArea("SFT")
SFT->(DBGOTOP())
cNomeArq		:= "FORNECE_N_ENCONTRADOS"+SUBSTR(DTOS(MV_PAR02),1,4)+"_"+SUBSTR(DTOS(MV_PAR02),5,2)+".TXT"

nHdl := fCreate(cNomeArq)
If nHdl == -1
	Iif(FindFunction("APMsgAlert"), APMsgAlert("O arquivo "+AllTrim(cNomeArq)+" nao pode ser criado! Verifique os parametros.", "Atencao!"), MsgAlert("O arquivo "+AllTrim(cNomeArq)+" nao pode ser criado! Verifique os parametros.", "Atencao!"))
	Return
Endif


While QRY->(!Eof())




	SFT->(dbGoto(QRY->FTRECNO))

	SA2->(dbSeek(xFilial("SA2")+SFt->FT_CLIEFOR+SFT->FT_LOJA) )
	SA1->(dbSeek(xFilial("SA1")+SFt->FT_CLIEFOR+SFT->FT_LOJA) )

	Sm0->(dbSeek(cEmpAnt+SFT->FT_FILIAL, .T. ))

	IF SFT->FT_TIPOMOV="S"
		IF EMPTY(SFT->FT_NFORI)

			cEst:=SM0->M0_ESTENT
			cCNPJ:=SM0->M0_CGC

		else
			if SFT->FT_FORMUL="S" .or.  SFT->FT_FORMUL==" "
				cEst:=SM0->M0_ESTENT
				cCNPJ:=SM0->M0_CGC
			else
				cEst:=SA2->A2_EST
				cCNPJ:=SA2->A2_CGC
			ENDIF
		ENDIF
	ELSE
		IF EMPTY(SFT->FT_NFORI)

			cEst:=SA2->A2_EST
			cCNPJ:=SA2->A2_CGC

		ELSE

			if SFT->FT_FORMUL="S" .or.  SFT->FT_FORMUL==" "
				cEst:=SM0->M0_ESTENT
				cCNPJ:=SM0->M0_CGC
			else
				cEst:=SA1->A1_EST
				cCNPJ:=SA1->A1_CGC
			ENDIF


		ENDIF


	ENDIF


	IF (!EMPTY (cCNPJ ) .AND.  !EMPTY (cEst   )  )
		cChave := aUF[aScan(aUF,{|x| x[1] == cEst})][02]
		cChave+=FsDateConv(SFT->FT_EMISSAO,"YYMM")
		cChave+=cCNPJ+IF (SFT->FT_ESPECIE="CTE","57","55")
		cChave+=StrZero(Val(SFT->FT_SERIE),3)
		cChave+=StrZero(Val(SFT->FT_NFISCAL),9)
		cChave+="1"+SUBSTR(InvertM(StrZero(Val(SFT->FT_NFISCAL),8)),1,8)
		cChave+=MODULO11(cChave)


		sft->(RecLock("SFT", .F. ))
		IF EMPTY(SFT->FT_PDV)
		SFT->FT_CHVNFE := cChave
		SFT->FT_OBSERV:=alltrim(SFT->FT_OBSERV)+IF ("--CHV"$SFT->FT_OBSERV,"","--CHV")
		ELSE
		SFT->FT_CHVNFE := ""
		SFT->FT_OBSERV:=STRTRAN(SFT->FT_OBSERV,"--CHV","")

		ENDIF

		sft->(MsUnlock())
	ELSE
		CLINLOC()
	ENDIF
	QRY->(DBSKIP())


EndDo







If nHdl > 0
	If fClose(nHdl)
		If lContinua
			Aviso("AVISO","Gerado o arquivo " + AllTrim(cNomeArq) + "...",{"OK"})
		Else
			If fErase(cNomeArq) == 0
				If lContinua
					Aviso("AVISO","Nao existem registros a serem gravados. A geracao do arquivo " + AllTrim(cNomeArq) + " foi abortada ...",{"OK"})
				EndIf
			Else

				Iif(FindFunction("APMsgAlert"), APMsgAlert("Ocorreram problemas na tentativa de delecao do arquivo "+AllTrim(cNomeArq)+".",), MsgAlert("Ocorreram problemas na tentativa de delecao do arquivo "+AllTrim(cNomeArq)+".",))

			EndIf
		EndIf
	Else

		Iif(FindFunction("APMsgAlert"), APMsgAlert("Ocorreram problemas no fechamento do arquivo "+AllTrim(cNomeArq)+".",), MsgAlert("Ocorreram problemas no fechamento do arquivo "+AllTrim(cNomeArq)+".",))

	EndIf
EndIf

return()




Static Function InvertM(uCpo, nDig)
Local nDiv	:= 0

cRet	:=	GCifra(Val(SFT->FT_NFISCAL),nDig)




























Return(cRet)


Static Function CLINLOC()

cLin	:= ""
cLin 	+=SFt->FT_CLIEFOR+";"+SFT->FT_LOJA+";"
cLin 	+= Chr(13)+Chr(10)

fGravaReg(cLin)

Return
















Static Function fGravaReg(cLin)

If fWrite(nHdl,cLin,Len(cLin)) <> Len(cLin)
	If !Iif(FindFunction("APMsgYesNo"), APMsgYesNo("Ocorreu um erro na gravacao do arquivo "+AllTrim(cNomeArq)+".   Continua?", "Atencao!"), (cMsgYesNo:="MsgYesNo", &cMsgYesNo.("Ocorreu um erro na gravacao do arquivo "+AllTrim(cNomeArq)+".   Continua?", "Atencao!")))
		lContinua := .F. 
		Return
	Endif
Endif

Return