#Include "rwmake.ch"

/* PONTO DE ENTRADA PARA FILTRAR OS REGISTROS DA LIQUIDACAO */
// ponto de entrada em conjunto com o F460OK1

User Function FA460FIL
Local	cRet := ""
// 2o da minha customizacao
pergunte("XAFI46",.F.)

If MV_PAR01 == 2 // Consumidor Final
	cRet := '.And.E1_AGE="'+MV_PAR02+'".And.'
	cRet += 'E1_AGELOJA="'+MV_PAR03+'"'
ElseIf MV_PAR01 == 1  // Revenda
	cRet := '.And.E1_AGE="'+Space(Len(E1_AGE))+'".And.'
	cRet += 'E1_AGELOJA="'+Space(Len(E1_AGELOJA))+'"'
ElseIf MV_PAR01 == 3  // Suprimentos
	cRet := '.And.E1_AGE="'+Space(Len(E1_AGE))+'".And.'
	cRet += 'E1_AGELOJA="'+Space(Len(E1_AGELOJA))+'"'
	cRet += '.And.E1_VEND1="'+MV_PAR04+'"'
Endif

pergunte("AFI460",.F.)

Return(cRet)