#Include 'Protheus.ch'

User Function dic001()

local aDados := {}
local aStruct := NIL, aX3
local nTotal := 0

DBSELECTAREA("SX3")
DBSETORDER(2)
dbGoTop()

aStruct := SX3->(dbStruct())
nTotal := Len(aStruct)

dbUseArea( .T., "DBFCDX", "SX3", "TRAB", .T. ,.F.)
dbGoTop()

ax3 := TRAB->(dbStruct())

while TRAB->(!eof())


SX3->(dbGoTop())

if SX3->(dbSeek(TRAB->(X3_CAMPO)))
	
	for nx := 1 TO nTotal
	
		lret:= .F.
		
		nb := 1
		
	 	while  nb <= len(ax3) .and. !lRet
			if alltrim(aStruct[nx,1]) = allTrim(ax3[nb,1])
				lRet := .T.
			endif
			nb++
		enddo
		
		if lRet
		x3 := SX3->(&(aStruct[nx,1]))
		tr := TRAB->(&(aStruct[nx,1]))
	
		if x3 <> tr
			aadd(aDados,{"S",TRAB->(X3_ARQUIVO),TRAB->(X3_CAMPO),aStruct[nx,1]})
		endIf
		endIf
	next	

ELSE
	aadd(aDados,{"N",TRAB->(X3_ARQUIVO),TRAB->(X3_CAMPO)})
endIf

TRAB->(dbSkip())

ENDDO


DlgToExcel({ {"ARRAY", "sx3", {}, aDados} })

Return

