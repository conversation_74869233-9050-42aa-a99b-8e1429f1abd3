#include "protheus.ch"

/*
Programa   : TMKINCU5()
Objetivo   : Ponto de entrada para gravar o contato como prospect automaticamente e vincular com a AC8
Retorno    : aBtnLat
Autor      : <PERSON><PERSON><PERSON>
Data/Hora  : 22/07/2014 - 10:00
*/
User Function TMKINCU5()

Local cContato	:= ""
Local cProspect	:= ""
Local cLjProspec:= ""

If Inclui
//Contato para prospect e Prospect X Contato
//SU5 para SUS e AC8

	Begin Transaction
	RecLock("SUS",.T.)
	
		cContato := SU5->U5_CODCONT
		
		SUS->US_FILIAL 	:= xFilial("SU5")
		SUS->US_COD 	:= cProspect  := GetSx8Num("SUS","US_COD")//IIF(INCLUI,GetSx8Num("SUS","US_COD"),"")
		SUS->US_LOJA 	:= cLjProspec :="01"
		SUS->US_NOME	:= SU5->U5_CONTAT
		SUS->US_NREDUZ	:= SU5->U5_CONTAT
		SUS->US_TIPO	:= "F"
		SUS->US_END		:= SU5->U5_END
		SUS->US_MUN		:= SU5->U5_MUN
		SUS->US_BAIRRO 	:= SU5->U5_BAIRRO
		SUS->US_CEP 	:= SU5->U5_CEP
		SUS->US_EST 	:= SU5->U5_EST
		SUS->US_DDI	 	:= SU5->U5_CODPAIS
		SUS->US_DDD 	:= SU5->U5_DDD
		SUS->US_TEL 	:= SU5->U5_FONE
		SUS->US_FAX 	:= SU5->U5_FAX
		SUS->US_EMAIL 	:= SU5->U5_EMAIL 
		SUS->US_URL 	:= SU5->U5_URL
		SUS->US_PAIS 	:= SU5->U5_PAIS
		SUS->US_CGC 	:= SU5->U5_CPF
		
	SUS->(MsUnLock())
	
	RecLock("AC8",.T.)
		AC8->AC8_FILIAL := xFilial("AC8")
		AC8->AC8_FILENT := xFilial("SUS") 
		AC8->AC8_ENTIDA := "SUS"
		AC8->AC8_CODENT := Avkey(cProspect,"US_COD")+AvKey(cLjProspec,"US_LOJA")//CODIGO + LOJA - Prospect
		AC8->AC8_CODCON := cContato//CODIGO DO CONTATO	
	AC8->(MsUnLock())
	ConfirmSX8()
	End Transaction
End If


Return