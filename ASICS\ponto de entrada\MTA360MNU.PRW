#INCLUDE 'PROTHEUS.CH'

#DEFINE USER_ID    1
#DEFINE USER_NOME  2
#DEFINE USER_EMAIL 3
#DEFINE USER_GRUPO 4

STATIC FUNC_NAME    := 'MATA360' // INFORME AQUI O NOME DA FUNCAO
STATIC ACESSOS_USER := {}

/*
#==================================================================================#
| +------------------------------------------------------------------------------+ |
| ! FUNCAO.........:	JSOXIT01-02-06-33-34-35-36-38                            ! |
| ! DESCRICAO......:    ADD/REMOVE ROTINAS MENU - USADO PARA VALIDAR ACESSOS USER! |
| ! AUTOR..........:	BRUNNO ABRIGO               	                         ! |
| ! DATA...........:	08/03/2014                                               ! |
| ! PARAMETROS.....:	NIL                                                      ! |
| ! RETORNO........:	AROTINA - COM ACESSOS PERMITIDOS    - CONDICAO PAGAMENTO ! |
| +------------------------------------------------------------------------------+ |
#==================================================================================#
*/


User Function MTA360MNU

	Local  aInfoUser := {}
	
	// NO CASO DE NOVAS FUNCOES ALTERAR O NOME DA VARIAVEL ABAIXO
	// VARIAVEL DE CONTROLE
	LOCAL __PROCA360 := If( TYPE("__PROCA360") == "U", .T., __PROCA360 )
	
	// NO CASO DE NOVAS FUNCOES ALTERAR O NOME DA VARIAVEL ABAIXO
	// VARIAVEL DE CONTROLE
	LOCAL _AROT_MTA360 := If( TYPE("_AROT_MTA360") == "U" , {} , _AROT_MTA360 )
	Local lExistReg := .F.
	
	If __PROCA360
		
		aInfoUser := U_INFOUSER()//{IdUsuario,NomeUsuario,EmailUsuario,GrupoUsuario}
		
		If IsInCallStack(FUNC_NAME)
			If !Empty(aInfoUser)
				//----------------------------------------------------------------------------------------------------------------
				// FUNCAO........: RETORNA PERMISSOES DO USUARIO CONECTADO OU SE USUARIO TEM PERMISSOES NO GRUPO
				// SINTAXE.......: U_JSOXFUN1( <NOME FUNCAO> , <ID USUARIO> , <GRUPO USER> )
				// OBRIGATORIO...:                  X                 X
				// RETORNO.......:  .T.=TEM ACESSO ; .F.= SEM ACESSO;  { PESQUI , VISUAL, INCLUI, ALTERA, EXCLUI, OUTROS }
				//----------------------------------------------------------------------------------------------------------------
				ACESSOS_USER := aClone( U_JSOXFUN1(FUNC_NAME,aInfoUser[USER_ID],aInfoUser[USER_GRUPO],,@lExistReg) )
				If lExistReg .AND. !Empty(ACESSOS_USER)
					//-------------------------------------------------------------------------------------
					// FUNCAO.........: SETA PERMISSOES NO MENUDEF PADRAO
					// SINTAXE........: U_JSOXFUN2( <MENUDEF> )
					// OBRIGATORIO....:                 X
					// DESCRICAO......: VETOR CONTENDO ROTINAS DO MENU
					//-------------------------------------------------------------------------------------
					U_JSOXFUN2(ACESSOS_USER, @AROTINA)
					_AROT_MTA360 := AClone(aRotina)
					If Valtype(__PROCA360) == "L"
						__PROCA360 := .F.
					Endif
					
				Endif
			Endif
		Endif
	/*	
	Else
		//-------------------------------------
		// ATIVA AROTINA ARMAZENADO NA MEMORIA
		//-------------------------------------
		If Valtype(_AROT_MTA360)=="A"
			If Len(_AROT_MTA360) == Len(aRotina)
				AROTINA := {}
				AROTINA := ACLONE(_AROT_MTA360)
			Endif
		Endif
	*/	
	Endif
	
Return
