
** Alterado por: <PERSON> - amjg<PERSON>@gmail.com - Em: 21/11/2006

User Function F150EXC
Local cEnv := Alltrim(Upper(GetEnvServer()))
Local lRet := .T.
//Local lExibe := GetMv("MV_EXIBE")

If .T. //"ENVTESTETMP" $ cEnv

	If SEE->(FieldPos("EE_BLOCKED")) > 0 
		If SEE->EE_BLOCKED == "S"
	  		lRet := .F.
			Final("Layout de Envio Bloqueado, o arquivo nao sera gerado")
			/*
			If lExibe
				Alert("Layout de Envio Bloqueado, o arquivo nao sera gerado")
				// PutMv("MV_EXIBE",.F.)  // verificado
			Endif
			*/
		Endif
	Endif
	
Endif
Return lRet