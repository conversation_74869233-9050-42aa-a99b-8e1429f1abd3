#Include 'Protheus.ch'

/*/{Protheus.doc} CadCatgJet
@description 	Cadastro de Categorias E-commerce
<AUTHOR>
@version		1.0
@since 			29/09/2015
@type 			Function
/*/

USER FUNCTION CadCatgJet()
 
PRIVATE cCadastro  := "Cadastro de Categorias E-commerce"
PRIVATE aRotina     :=  { {"Pesquisar" ,"AxPesqui",0,1} ,; 
                        {"Visualizar","AxVisual",0,2} ,; 
                        {"Incluir"   ,"AxInclui",0,3} ,; 
                        {"Alterar"   ,"AxAltera",0,4} ,; 
                        {"Excluir"   ,"AxDeleta",0,5} } 
   Private cDelFunc := ".T." // Validacao para a exclusao. 
   Private cString := "PA2" 

dbselectarea('PA2')
dbsetorder(1)

 
AxCadastro("PA2", cCadastro, ".T.", ".T." )
 
Return Nil