<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
	<Default Extension="jpg" ContentType="image/jpeg"/>
	<Default Extension="png" ContentType="image/png"/>
	<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
	<Default Extension="xml" ContentType="application/xml"/>
	<Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
	<%For nCont:=1 to Len(oSelf:aPlanilhas)%>
	<Override PartName="/xl/worksheets/sheet<%=nCont%>.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
	<%Next%>
	<%For nCont:=1 to Len(oSelf:atable)%>
	<Override PartName="/xl/tables/table<%=oSelf:atable[nCont]:nIdRelat%>.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml"/>
	<%Next%>
	<%For nCont:=1 to Len(oSelf:aworkdrawing)%>
	<Override PartName="/xl/drawings/drawing<%=oSelf:aworkdrawing[nCont]%>.xml" ContentType="application/vnd.openxmlformats-officedocument.drawing+xml"/>
	<%Next%>
	<Override PartName="/xl/theme/theme1.xml" ContentType="application/vnd.openxmlformats-officedocument.theme+xml"/>
	<Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml"/>
	<Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
	<Override PartName="/docProps/core.xml" ContentType="application/vnd.openxmlformats-package.core-properties+xml"/>
	<Override PartName="/docProps/app.xml" ContentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"/>
</Types>