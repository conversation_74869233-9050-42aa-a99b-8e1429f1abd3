#Include 'Protheus.ch'

#Define NPOSPLAN	2
#Define NPOSCORTE	3
#Define NPOSRECNO	17
#Define NPOSQTDCOM	13
                      
/*----------------------------------------------
@Autor: <PERSON><PERSON><PERSON>
@Data: 26/02/2017 @Hora: 13:30:14 @Uso: Asics Brasil
@Descrição: Melhoria no processo para trazer qtde
total comprada e vendida. 
Aumento de 17 para 19 colunas
------------------------------------------------
Change: 847
--------------------------------------------*/
#Define NTOTCOLS	19

Static NATGETDAD 	:= 1

/*--------------------------------------------
@Autor: <PERSON><PERSON><PERSON>
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Rotina responsavel pela importacao 
da necessidade de compras com as qtdes que
deverao serem compradas definindas. 
---------------------------------------------
Change: 383
--------------------------------------------*/
User Function ACOMM008()
	
	Local lRet			:= .F.
	
	Private cPerg		:= Padr("ACOMM008",10)	

	Private aDadosOk	:= {}
	Private aDadosEr	:= {}

	Private nSay3		:= 0
	Private nSay5		:= 0	
	
	If !Z34->Z34_STATUS $ "1|2|3"
		MsgInfo("Não é permitido importar uma planilha em que a necessidade não econtra-se em aberto( status Verde, Amarelo ou vermelho ), verifique.")
		Return()
	EndIf
	
	If !ASCRIATELA()
		Return()
	EndIf
	
	If !MsgYesNo("Tem certeza que deseja continuar?")
		Return()		
	EndIf
	
	FwMsgRun( ,{|| lRet:= ASVALARQ() }, , "Por favor aguarde, validando estrutura do arquivo...")
	
	If !lRet
		Return()
	EndIf
	
	FwMsgRun( ,{|| ASVALINF() }, , "Por favor aguarde, validando informações do arquivo...")
	
	If Len(aDadosEr) == 0 .And. Len(aDadosOk) == 0
		Return()
	EndIf
	
	If Len(aDadosEr) > 0
		If !MsgYesNo("Atenção, erros foram encontrados na validação do arquivo, deseja visualizar a interface de log?")
			Return()
		Endif
		
		FwMsgRun( ,{|| ASTELAERR() }, , "Por favor aguarde, criando interface de Log de erros")
	Else
		If !MsgYesNo("Parabéns, não existe erros no arquivo anexo, deseja continuar com a rotina?")
			Return()
		EndIf
					
		Begin Transaction
			Processa( {|lEnd| ASPROCESS() },"Aguarde, incluíndo solicitações de compra.")
		End Transaction
		
		MsgInfo("Rotina finalizada com sucesso.")
					
	EndIf	

Return()

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que cria interface de
parametros da rotina. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASCRIATELA()

	Local oDlg		:= Nil
	Local oFolder	:= Nil
	Local oFwLayer	:= Nil
	Local oPanel0A	:= Nil
	Local oPanel0B	:= Nil
	Local oSay1	:= Nil
	Local oSay2	:= Nil
	Local oSay3	:= Nil
	Local oSay4	:= Nil
	Local oBut1	:= Nil
	Local oBut2	:= Nil
	Local oBut3	:= Nil
	Local oFont1	:= TFont():New("Tahoma",,15,,.T.,,,,.F.,.F.)
	
	Local lRet		:= .F.
	
	Local cTexto	:= 	"Está rotina tem como objetivo efetuar a importação de um arquivo .CSV contendo"+CRLF+;
					"as solicitações de compras que deverão ser incluídos no ERP TOTVS."
	
	Pergunte(cPerg,.F.)
	MV_PAR01:= ""
	
	Define MsDialog oDlg From 000,000 To 310,550 Title "Asics Brasil - "+DTOC(DDATABASE) Pixel Of oDlg
		
		oDlg:lMaximized:= .F.
		
		oFolder 		:= TFolder():New(0,0,{"Rotina de importação de Compras"},,oDlg,,,,.T.,.F.,oDlg:nClientWidth/2,oDlg:nClientHeight/2+10)
		oFolder:Align	:= CONTROL_ALIGN_ALLCLIENT
			
		oFwLayer := FWLayer():New()
		oFwLayer:Init(oFolder:aDialogs[1],.T.)
		    
		oFwLayer:addLine("Lin00",030,.T.)
		oFwLayer:addCollumn("Col00",100,.T.,"Lin00")
		oFwLayer:addWindow("Col00","Win00","Descritivo",100,.F.,.T.,,"Lin00")
			
		oFwLayer:addLine("Lin01",055,.T.)
		oFwLayer:addCollumn("Col00",100,.T.,"Lin01")
		oFwLayer:addWindow("Col00","Win00","Ações",100,.F.,.T.,,"Lin01")
		
		oPanel0A:= oFwLayer:GetWinPanel("Col00","Win00","Lin00")
		oPanel0B:= oFwLayer:GetWinPanel("Col00","Win00","Lin01")
					
		oSay1	:= TSay():New(003,005,{||cTexto},oPanel0A,,oFont1,,,,.T.,,,999,999)
		
		oBut1:= TBitmap():Create(oPanel0B,003,005,050,050,,"OK",.T.,{||lRet:= .T.,oDlg:End()},,.F.,.F.,,,.F.,,.T.,,.F.)
		oSay2	:= TSay():New(005,030,{||"Processar"},oPanel0B,,oFont1,,,,.T.,,,999,999)
		 		
		oBut2:= TBitmap():Create(oPanel0B,025,005,050,050,,"CANCEL",.T.,{||oDlg:End()},,.F.,.F.,,,.F.,,.T.,,.F.)
		oSay3	:= TSay():New(027,030,{||"Sair"},oPanel0B,,oFont1,,,,.T.,,,999,999)
		
		oBut3:= TBitmap():Create(oPanel0B,045,005,050,050,,"HISTORIC",.T.,{||Pergunte(cPerg,.T.)},,.F.,.F.,,,.F.,,.T.,,.F.)
		oSay4	:= TSay():New(047,030,{||"Parâmetros"},oPanel0B,,oFont1,,,,.T.,,,999,999)		
		
	Activate MsDialog oDlg Centered
	
Return(lRet)

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que valida estrutura do
arquivo anexado. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASVALARQ()
	
	Local nHandle	:= 0
	
	Local aColunas	:= {}
	
	Local cBuffer	:= ""
	
	If !File(MV_PAR01)
		MsgStop("Atenção, arquivo informado não existe no diretório informado, verifique.")
		Return(.F.)
	EndIf
	
	nHandle	 := FT_FUSE(MV_PAR01)
	
	If nHandle < 0
		MsgStop("Atenção, erro ao abrir arquivo, verifique com o administrador do sistema.")
		Return(.F.)
	EndIf
	
	FT_FGOTOP()
	cBuffer	:= FT_FREADLN()
	aColunas	:= Separa(cBuffer,";")
	If Len(aColunas) <> NTOTCOLS
		MsgStop("Atenção, erro na quantidade de colunas do arquivo, verifique com o administrador do sistema.")
		Return(.F.)		
	EndIf		
	
Return(.T.)

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que valida informacoes do
arquivo anexado. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASVALINF()

	Local aTexto	:= {}
	
	Local cBuffer	:= ""
	Local cCorte	:= ""
	Local cPlan	:= ""
	
	Local nRecno	:= 0
	Local nLinha	:= 0
	
	aDadosOk		:= {}
	aDadosEr		:= {}
	
	nSay3			:= 0
	nSay5			:= 0		

	FT_FGOTOP()
	FT_FSKIP()	
	While !FT_FEOF()
	
		cBuffer	:= FT_FREADLN()
		aTexto	:= Separa(cBuffer,";")
		nRecno	:= StrTran(aTexto[NPOSRECNO],"'","")
		cCorte	:= StrTran(aTexto[NPOSCORTE],"'","")
		cPlan		:= StrTran(aTexto[NPOSPLAN],"'","")
			
		nLinha	+= 1
					
		If Select("TRB1") > 0
			TRB1->(DbCloseArea())
		EndIf

		BeginSql Alias "TRB1"
			SELECT *
			FROM %Table:Z35% Z35
			WHERE
			Z35_FILIAL			= %xfilial:Z35%
			AND Z35_NUM		= %Exp:cPlan%
			AND Z35_CORTE		= %Exp:cCorte%
			AND Z35.R_E_C_N_O_	= %Exp:nRecno%
			AND Z35.%notDel%
		EndSql
		
		nSay3+=1
		If TRB1->(!Eof())
			aAdd(aDadosOk,{StrTran(aTexto[NPOSRECNO],"'",""),aTexto[NPOSQTDCOM]})
		Else
			aAdd(aDadosEr,{nLinha,"Registro não encontrado no planejamento "+cPlan+"-"+cCorte+"."})
			nSay5+=1
		EndIf
							
		FT_FSKIP()
	EndDo		
	
Return()

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que cria interface de erros
caso exista.
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASTELAERR()

	Local oDlg		:= Nil
	Local oFolder	:= Nil
	Local oFwLayer	:= Nil
	Local oPanel0A	:= Nil
	Local oPanel0B	:= Nil
	Local oPanel0C	:= Nil
	Local oSay1	:= Nil
	Local oSay2	:= Nil
	Local oSay3	:= Nil
	Local oSay4	:= Nil
	Local oSay5	:= Nil
	Local oGetDad	:= Nil
	Local oFont1	:= TFont():New("Tahoma",,20,,.T.,,,,.F.,.F.)
	Local oFont2	:= TFont():New("Tahoma",,15,,.T.,,,,.F.,.F.)
	
	Local nOpcA	:= 0
	Local nX		:= 0

	Local aHeader	:= ASCRIAHED()
	Local aCols	:= ASCRIACOL()
	
	Local aButtons	:= {}
	Local aSize	:= MsAdvSize()
	
	Local nFreeze		:= 0
	Local nMax			:= 999
	Local cLinOk		:= "AllwaysTrue"
	Local cTudoOk		:= ""
	Local cIniCpos		:= ""
	Local cFieldOk		:= "AllwaysTrue"
	Local cSuperDel 	:= ""
	Local cDelOk		:= "AllwaysTrue"
	Local aAlterGda 	:= {}	
	
	Define MsDialog oDlg From 000,000 To aSize[6],aSize[5] Title "Asics Brasil - "+DTOC(DDATABASE) Pixel Of oDlg
		
		oDlg:lMaximized:= .T.
		
		oFolder 		:= TFolder():New(0,0,{"Histórico de validação"},,oDlg,,,,.T.,.F.,oDlg:nClientWidth/2,oDlg:nClientHeight/2+10)
		oFolder:Align	:= CONTROL_ALIGN_ALLCLIENT
			
		oFwLayer := FWLayer():New()
		oFwLayer:Init(oFolder:aDialogs[1],.T.)
		    
		oFwLayer:addLine("Lin00",015,.T.)
		oFwLayer:addCollumn("Col00",100,.T.,"Lin00")
		oFwLayer:addWindow("Col00","Win00","Descritivo",100,.F.,.T.,,"Lin00")
			
		oFwLayer:addLine("Lin01",055,.T.)
		oFwLayer:addCollumn("Col00",100,.T.,"Lin01")
		oFwLayer:addWindow("Col00","Win00","Erro(s)",100,.F.,.T.,,"Lin01")
		
		oFwLayer:addLine("Lin02",015,.T.)
		oFwLayer:addCollumn("Col00",100,.T.,"Lin02")
		oFwLayer:addWindow("Col00","Win00","Total",100,.F.,.T.,,"Lin02")		
			
		oPanel0A:= oFwLayer:GetWinPanel("Col00","Win00","Lin00")
		oPanel0B:= oFwLayer:GetWinPanel("Col00","Win00","Lin01")
		oPanel0C:= oFwLayer:GetWinPanel("Col00","Win00","Lin02")
					
		oSay1	:= TSay():New(003,005,{|| "Abaixo será listado o Log de erros no processamento do arquivo importado."},oPanel0A,,oFont1,,,,.T.,,,999,999)
			
		oGetDad := MsNewGetDados():New(0,0,0,0,2,cLinOk,cTudoOk,cIniCpos,aAlterGda,nFreeze,Len(aCols),cFieldOk,cSuperDel,cDelOk,oPanel0B,aHeader,aCols)
		oGetDad:oBrowse:Align			:= CONTROL_ALIGN_ALLCLIENT
		oGetDad:oBrowse:bChange		:= {|| NATGETDAD := oGetDad:oBrowse:nAt, oGetDad:oBrowse:Refresh()}
		oGetDad:oBrowse:SetBlkBackColor({||ASMUDACOR(1,oGetDad)})
		oGetDad:oBrowse:SetBlkColor({||ASMUDACOR(2,oGetDad)})
		
		oSay2	:= TSay():New(003,005,{|| "Total de linhas no arquivo:" 	},oPanel0C,					,oFont2,,,,.T.,,,200,15)
		oSay3	:= TSay():New(003,100,{|| nSay3 					},oPanel0C,PESQPICT("SD2","D2_QUANT")	,oFont2,,,,.T.,CLR_HRED,,100,15)
		
		oSay4:= TSay():New(003,200,{|| "Total de linhas com erro:" 	},oPanel0C,					,oFont2,,,,.T.,,,200,15)
		oSay5	:= TSay():New(003,295,{|| nSay5 					},oPanel0C,PESQPICT("SD2","D2_QUANT")	,oFont2,,,,.T.,CLR_HRED,,100,15)
		
		Aadd(aButtons,{"budget",{|| ASEXCEL(aHeader,aCols)}, "Exporta Excel","Exporta Excel"})	
	
	Activate MsDialog oDlg On Init EnchoiceBar(oDlg,{|| nOpcA:= 1,oDlg:End()},{|| oDlg:End()},,aButtons) CENTERED	
		
Return()

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que cria cabecalho das
colunas da interface de erros. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASCRIAHED()
	
	Local aRet	:= {}
	
	aAdd(aRet,{"Status Erro"		,"TRB_BMP"		,"@BMP"		,10	,0,"","","C","",""})
	Aadd(aRet,{"Linha"			,"TRB_LINHA"	,""			,06	,0,"","","C","",""})
	Aadd(aRet,{"Descrição"		,"TRB_DESC"	,""			,30	,0,"","","C","",""})
	
Return(aRet)

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que cria itens da interface
de erros. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASCRIACOL()
	
	Local aRet	:= {}
	
	For nX:= 1 To Len(aDadosEr)
		aAdd(aRet,{	"BR_CANCEL"		,;//Legenda
					aDadosEr[nX][1]		,;//Linha do arquivo
					aDadosEr[nX][2]		,;//Descricao
					.F.				})
	Next nX	

Return(aRet)

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que efetua a mudanca da
cor da linha que esta sendo navegada pelo
usuario. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASMUDACOR(nTipo,oGet1)

	Local nRet  	:= Iif(nTipo == 1,CLR_WHITE,CLR_BLACK)
	Local nLinha	:= oGet1:NAT
	
	Default nTipo	:= 0
	Default oGet1	:= Nil
	
	If nTipo == 1
		If nLinha == NATGETDAD
			nRet := CLR_BLACK
		EndIf
	Else
		If nLinha == NATGETDAD
			nRet := CLR_WHITE
		EndIf
	EndIf

Return(nRet)

/*--------------------------------------------
@Autor: Caio Pereira
@Data: 17/11/2015
@Hora: 18:24:52
@Versão: 
@Uso: Asics Brasil
@Descrição: Funcao que efetua a atualizacao
da qtde comprava da necessidade de compras
de acordo com o arquivo anexado. 
---------------------------------------------
Change: 383
--------------------------------------------*/
Static Function ASPROCESS()
	
	Local nX	:= 0
	
	For nX:=1 To Len(aDadosOk)
		
		If Val(aDadosOk[nX][2]) > 0
			Z35->(DbGoTo(Val(aDadosOk[nX][1])))
			RecLock("Z35",.F.)
				Z35->Z35_COMPRA := Val(aDadosOk[nX][2])
			Z35->(MsUnLock())
		EndIf
	
	Next nX
	
	If Select("TRB1") > 0
		TRB1->(DbCloseArea())
	EndIf

	BeginSql Alias "TRB1"
		SELECT *
		FROM %Table:Z35% Z35
		WHERE
		Z35_FILIAL			= %xfilial:Z35%
		AND Z35_NUM		= %Exp:Z34->Z34_NUM%
		AND Z35_CORTE		= %Exp:Z34->Z34_CORTE%
		AND Z35_COMPRA 		> 0
		AND Z35.%notDel%
	EndSql
	
	//Parcialmente atendida
	If TRB1->(!Eof())
		Z34->(DbSetOrder(1))
		If Z34->(DbSeek(xFilial("Z34") + Z34->Z34_NUM + Z34->Z34_CORTE))
			RecLock("Z34",.F.)
				Z34->Z34_STATUS := "2"
			Z34->(MsUnLock())
		EndIf
	EndIf
	
	If Select("TRB1") > 0
		TRB1->(DbCloseArea())
	EndIf

	BeginSql Alias "TRB1"
		SELECT *
		FROM %Table:Z35% Z35
		WHERE
		Z35_FILIAL			= %xfilial:Z35%
		AND Z35_NUM		= %Exp:Z34->Z34_NUM%
		AND Z35_CORTE		= %Exp:Z34->Z34_CORTE%
		AND Z35_COMPRA 		< Z35_QTDNEC
		AND Z35.%notDel%
	EndSql
	
	//Totalmente atendida
	If TRB1->(Eof())
		Z34->(DbSetOrder(1))
		If Z34->(DbSeek(xFilial("Z34") + Z34->Z34_NUM + Z34->Z34_CORTE))
			RecLock("Z34",.F.)
				Z34->Z34_STATUS := "3"
			Z34->(MsUnLock())
		EndIf
	EndIf
	
Return()
