#Include "Protheus.Ch"
#Include "Fileio.ch"
#Include "TopConn.CH"
#Include "TbiConn.CH"
/*
Programa   : TK271END ()
Objetivo   : Envio de email apos a confirmacao 
Retorno    : 
Autor      : <PERSON><PERSON><PERSON>
Data/Hora  : 13/08/2014 - 10:00
*/
User Function TK271END()//executar ao alterar 
Private cCodOcorrencia	:= ""
Private cEmailTo   		:= ""
Private cCopia1	  		:= ""
Private cCopia2	 		:= "" 
Private lResp	 		:= .F. 
If (Inclui .Or. Altera) .And. M->UC_XEMAIL != "2"  .And.EnvEMail()
	U_Mail_Call()
	GravaSUCQI2()
End If

Return



/*
Programa   : GravaSUCQI2()
Objetivo   : Gravar dados na SUC para QI2 os campos customizados
Retorno    : 
Autor      : Fabi<PERSON>
Data/Hora  : 13/08/2014 - 10:00
*/
Static Function GravaSUCQI2(cCodigo)
Local aOrd 		:= SaveOrd({"SUD","SUC","SUQ"})
Local nPosProd 	:= aScan(aHeader,{|x| AllTrim(x[2])=="UD_PRODUTO"}) 
Local nPosDesc 	:= aScan(aHeader,{|x| AllTrim(x[2])=="UD_DESC"	}) 
Local nPosOcor 	:= aScan(aHeader,{|x| AllTrim(x[2])=="UD_OCORREN"}) 

Begin Transaction//Restaura todos os dados caso seja interrompido por um erro

QI2->(dbSetOrder(9))//FILIAL+ATENDIMENTO - ITEM/ASSUNTO: 000015 - 02/000004    
If QI2->(dbSeek(xFilial("QI2")+"ATENDIMENTO - ITEM/ASSUNTO: "+SUC->UC_CODIGO ) ) .And. "TMK" $ QI2->QI2_ORIGEM//validar

	RecLock("QI2",.F.)
		QI2->QI2_XMODEL	:= SUC->UC_XMODELO
		QI2->QI2_XNUMER	:= SUC->UC_XNUMERO
		QI2->QI2_XDEFEI	:= SUC->UC_XDEFEIT
		QI2->QI2_XTEMPO	:= SUC->UC_XTEMPO
		QI2->QI2_XLOJA	:= SUC->UC_XLOJA
		QI2->QI2_XCITLO	:= SUC->UC_XCITLOJ
		QI2->QI2_XNF	:= SUC->UC_XNF
		QI2->QI2_XDTCOM	:= SUC->UC_XDTCOMP
		QI2->QI2_XVLPAG	:= SUC->UC_XVLPAG
		QI2->QI2_XFORMA	:= SUC->UC_XFORMA
		QI2->QI2_XFREQ	:= SUC->UC_XFREQ
		QI2->QI2_XSPORT	:= SUC->UC_XSPORT
		QI2->QI2_XLIMP	:= SUC->UC_XLIMP
	QI2->(MsUnLock())
	
End If
RestOrd(aOrd)
End Transaction

Return


/*
Programa   : EnvEMail ()
Objetivo   : Envio de email apos a confirmacao da  alteracao e ao clicar em acoes relacionadas em 'Reenvio de e-mail' 
Retorno    : 
Autor      : Fabio Satoru Yamamoto
Data/Hora  : 13/08/2014 - 10:00
*/
Static Function EnvEMail()
Local lRet 		:= .F.
Local cAux
Local aOrd 		:= SaveOrd({"SUQ"})
Local i
Local cCodAcao 	:= ""
Local nPosOcor := aScan(aHeader,{|x| AllTrim(x[2])=="UD_SOLUCAO" })

For i:=1 To Len(aCols)
	cCodAcao := aCols[i][nPosOcor]
	cAux := Posicione("SUQ",1,xFilial("SUQ")+AvKey(cCodAcao,"UQ_SOLUCAO"),"UQ_GERFNC")
		                        
	If cAux $ "1" .And. "3" != SUC->UC_STATUS//"3" != M->UC_STATUS
		cCodOcorrencia	:= cCodAcao
		cEmailTo		:= Posicione("SUQ",1,xFilial("SUQ")+AvKey(cCodAcao,"UQ_SOLUCAO"),"UQ_EMAIL")
		cCopia1	  		:= Posicione("SUQ",1,xFilial("SUQ")+AvKey(cCodAcao,"UQ_SOLUCAO"),"UQ_XCMAIL1")
		cCopia2	 		:= Posicione("SUQ",1,xFilial("SUQ")+AvKey(cCodAcao,"UQ_SOLUCAO"),"UQ_XCMAIL2")
		lRet := .T.
	End If
	
Next

RestOrd(aOrd)

Return lRet