#Include "rwmake.ch"

/*
Este PE trabalha em conjunto com o PE F460VAL, A460COL, F460SE1
*/

User Function F460CTB
Local nPosEmit := aScan(aHeader,{|x| AllTrim(x[2])=="E1_EMITCHQ"})
Local nPosHist := aScan(aHeader,{|x| AllTrim(x[2])=="E1_HIST"})

SE1->E1_PREFIXO	:= aCols[nCntFor,1]
SE1->E1_NUMLIQ	 	:= cLiquid					// Nro. da Liquidacao
SE1->E1_NUM	   	:= aCols[nCntFor,6]		// nro. do cheque
SE1->E1_TIPO   	:= aCols[nCntFor,2]		// Tipo
SE1->E1_BCOCHQ 	:= aCols[nCntFor,3]		// banco
SE1->E1_AGECHQ 	:= aCols[nCntFor,4]		// agencia
SE1->E1_CTACHQ 	:= aCols[nCntFor,5]		// conta

SE1->E1_PARCELA	:= cParc460
SE1->E1_CLIENTE	:= cCliente
SE1->E1_LOJA		:= cLoja

For nCntFor := 1 To Len(aCols)
	If (aCols[nCntFor,nUsado2+1])
		Loop
	Endif
//	aCols[nCntFor,1] + aCols[nCntFor,6] + 
	
Next nCntFor 
