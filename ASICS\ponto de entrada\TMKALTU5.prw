#include "protheus.ch"

/*
Programa   : TMKALTU5()
Objetivo   : Ponto de entrada para gravar o contato como prospect automaticamente e vincular com a AC8
Retorno    : aBtnLat
Autor      : <PERSON><PERSON><PERSON>
Data/Hora  : 22/07/2014 - 10:00
*/
User Function TMKALTU5()

	Local cContato	:= ""
	Local cProspect	:= ""
	Local cLjProspec:= ""

	If ALTERA
		//Contato para prospect e Prospect X Contato
		//SU5 para SUS e AC8

		Begin Transaction

			cContato := SU5->U5_CODCONT

			DbSelectArea("AC8")
			DbSetOrder(1)

			If AC8->(DbSeek(xFilial("AC8")+cContato))

				DbSelectArea("SUS")
				DbSetOrder(1)

				If SUS->(DbSeek(xFilial("SUS")+AC8->AC8_CODENT))

					RecLock("SUS",.F.)

					SUS->US_NOME	:= SU5->U5_CONTAT
					SUS->US_NREDUZ	:= SU5->U5_CONTAT
					SUS->US_TIPO	:= "F"
					SUS->US_END		:= SU5->U5_END
					SUS->US_MUN		:= SU5->U5_MUN
					SUS->US_BAIRRO 	:= SU5->U5_BAIRRO
					SUS->US_CEP 	:= SU5->U5_CEP
					SUS->US_EST 	:= SU5->U5_EST
					SUS->US_DDI	 	:= SU5->U5_CODPAIS
					SUS->US_DDD 	:= SU5->U5_DDD
					SUS->US_TEL 	:= SU5->U5_FONE
					SUS->US_FAX 	:= SU5->U5_FAX
					SUS->US_EMAIL 	:= SU5->U5_EMAIL
					SUS->US_URL 	:= SU5->U5_URL
					SUS->US_PAIS 	:= SU5->U5_PAIS
					SUS->US_CGC 	:= SU5->U5_CPF

					SUS->(MsUnLock())

				EndIf

			EndIf

		End Transaction
	End If

Return