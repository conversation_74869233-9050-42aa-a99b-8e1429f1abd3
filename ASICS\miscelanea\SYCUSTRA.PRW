#include "Protheus.ch"
#include "Topconn.ch"
#include "TbiConn.ch"
#include "TbiCode.ch"
#include "Jpeg.ch"
#include "MsmGadd.ch"

User Function SYCUSTRA()

Local cQry      := ""
Local nPosProd  := aScan(aHeader,{ |x| Upper(AllTrim(x[2])) == 'C6_PRODUTO'})
Local cProduto  := acols[n,nPosProd]
Local nPosLoc   := aScan(aHeader,{ |x| Upper(AllTrim(x[2])) == 'C6_LOCAL'  })
Local cLocPad   := acols[n,nPosLoc]
Local nCustMed  := 0
Local nCusto    := 0 
Local cEstado	:= ""
Local xEst07    := AllTrim(GetMV('MV_XEST07'))
Local xEst12    := AllTrim(GetMV('MV_XEST12'))
Local xEst18    := AllTrim(GetMV('MV_XEST18'))
Local aMascara	:= Separa(GetMv('MV_MASCGRD',,'09,04,02,02'),',')
Local nTamPai	:= Val(aMascara[1])
Local cProd	    := Left(cProduto,nTamPai)  
Local cFilLoja  := GetMv("MV_SYFILLOJ",,"")
 
cQry+="	SELECT TOP 1 B2_CM1 FROM "+RETSQLNAME("SB2")+" SB2"+CRLF
cQry+="	WHERE B2_FILIAL = '"+xFilial("SB2")+"'"+CRLF
cQry+="	AND LEFT(B2_COD,"+Alltrim(cValToChar(nTamPai))+") = '"+cProd+"'"+CRLF
cQry+="	AND B2_LOCAL = '"+cLocPad+"'"+CRLF
cQry+="	AND B2_CM1 > 0"+CRLF
cQry+="	AND SB2.D_E_L_E_T_ = ''"+CRLF
cQry+="	ORDER BY B2_CM1 DESC"+CRLF

If Select("TRB1") > 0
	TRB1->(DbCloseArea())
EndIf

TcQuery cQry New Alias "TRB1"

nCustMed  :=TRB1->B2_CM1

//If CFILANT $ Alltrim(cFilLoja)
	If Empty(M->C5_TABELA) .And. M->C5_01TPOP == "2" .And. !Empty(M->C5_01OST)
		
		If M->C5_TIPO == 'N'
			cEstado  := Posicione('SA1',1,xFilial('SA1')+M->C5_CLIENTE+M->C5_LOJACLI,'A1_EST')
		Else
			cEstado  := Posicione('SA2',1,xFilial('SA2')+M->C5_CLIENTE+M->C5_LOJACLI,'A2_EST')
		EndIf
		
		nDescPerc := U_AfRetIcm(M->C5_CLIENTE,M->C5_LOJACLI,cProduto,0,.T.)
		//U_SYDESICMS(cProduto,M->C5_CLIENTE,M->C5_LOJACLI,1)
		
		If nDescPerc == 0
			If cEstado $ xEst07
				nCusto := Round(nCustMed / 0.93,2)
			ElseIf cEstado $ xEst12
				nCusto := Round(nCustMed / 0.88,2)
			ElseIf cEstado $ xEst18
				nCusto := Round(nCustMed / 0.82,2)
			EndIf
		Else
			nCusto := Round(nCustMed / nDescPerc,2)		
		EndIf	
		
	Endif
//Endif	
Return(nCusto)