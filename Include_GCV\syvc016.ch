#ifdef SPANISH
	#define STR0001"Espere"
	#define STR0002"Efectuando consulta..."
	#define STR0003"Historial de metas"
	#define STR0004"Periodo seleccionado: De"
	#define STR0005" a"
	#define STR0006"Ano"
	#define STR0007"Semana"
	#define STR0008"Meta(C)"
	#define STR0009"Historia(C)"
	#define	STR0010"Real(C)"
	#define	STR0011"% Real/Meta(C)"
	#define	STR0012"% Real/Hist(C)"
	#define	STR0013"Meta($)"
	#define	STR0014"Historia($)"
	#define	STR0015"Real($)"
	#define	STR0016"% Real/Meta($)"
	#define	STR0017"% Real/Hist($)"
	#define	STR0018"No fue posible borrar la tabla temporal."
#else
	#ifdef ENGLISH
		#define STR0001"Wait"
		#define STR0002"Running inquiry..."
		#define STR0003"Target History"
		#define STR0004"Selected Period: From "
		#define STR0005" to "
		#define STR0006"Year"
		#define STR0007"Week"
		#define STR0008"Target(Q)"
		#define STR0009"History(Q)"
		#define STR0010"Real(Q)"
		#define STR0011"% Real/Target(Q)"
		#define STR0012"% Real/Hist(Q)"
		#define STR0013"Target($)"
		#define STR0014"History($)"
		#define STR0015"Real($)"
		#define STR0016"% Real/Target($)"
		#define STR0017"% Real/Hist($)"
		#define STR0018"It was not possible to delete the temporary table."
	#else
		#define STR0001"Aguarde"
		#define STR0002"Efetuando consulta..."
		#define STR0003"Historico de Metas"
		#define STR0004"Periodo Selecionado: De"
		#define STR0005" ate"
		#define STR0006"Ano"
		#define STR0007"Semana"
		#define STR0008"Meta(Q)"
		#define STR0009"Historia(Q)"
		#define STR0010"Real(Q)"
		#define STR0011"% Real/Meta(Q)"
		#define STR0012"% Real/Hist(Q)"
		#define STR0013"Meta($)"
		#define STR0014"Historia($)"
		#define STR0015"Real($)"
		#define STR0016"% Real/Meta($)"
		#define STR0017"% Real/Hist($)"
		#define STR0018"Nao foi possivel a exclusao da tabela temporaria."
	#endif
#endif