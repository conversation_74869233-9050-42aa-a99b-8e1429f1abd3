#Include "Totvs.ch"

/*==========================================================================
 Funcao...........:	MT100AGR
 Descricao........:	Ponto de Entrada para gravar data de pagamento
 					Nos titulos de NF
 Autor............:	Amedeo D. Paoli 	
 Data.............:	16/05/2015
 Parametros.......:	Nil
 Retorno..........:	Nil
==========================================================================*/
User Function MT100AGR()
	Local aAreaAT	:= GetArea()
	Local aAreaE2	:= SE2->( GetArea() )
	Local cNFiscal	:= SF1->F1_DOC
	Local cSerie	:= SF1->F1_SERIE
	Local cFornece	:= SF1->F1_FORNECE
	Local cLoja		:= SF1->F1_LOJA
	
	DbSelectarea("SE2")
	SE2->( DbSetorder(6) )	//E2_FILIAL+E2_FORNECE+E2_LOJA+E2_PREFIXO+E2_NUM+E2_PARCELA+E2_TIPO
	If SE2->( DbSeek(xFilial("SE2") + cFornece + cLoja + cSerie + cNFiscal) )
		While !SE2->( Eof() ) .And. SE2->E2_FILIAL == xFilial("SE2") .And.;
									SE2->E2_FORNECE == cFornece .And.;
									SE2->E2_LOJA == cLoja .And.;
									SE2->E2_PREFIXO == cSerie .And.;
									SE2->E2_NUM == cNFiscal
			
			RecLock("SE2",.F.)
				SE2->E2_XDTPAG	:= SE2->E2_VENCREA
			MsUnlock()
			
			SE2->( DbSkip() )
		End
	EndIf
	
	RestArea( aAreaAT )
	RestArea( aAreaE2 )
	
Return Nil
