<EASYLINK>
	<SERVICE>
		<ID>003</ID>
		<DATA_SELECTION>
			<FIN_NUM>If(EECFlags('TIT_PARCELAS').And.!Empty(EEC->EEC_TITCAM), EEC->EEC_TITCAM, EEC<PERSON>etFinN("SE1"))</FIN_NUM>
			<FIN_SEND>
				<FIN_IT>
					<FIN_ELE1>'E1_NUM'</FIN_ELE1>
					<E1_NUM>#TAG FIN_NUM#</E1_NUM>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_PREFIXO'</FIN_ELE1>
					<E1_PREFIXO>cModulo</E1_PREFIXO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_PARCELA'</FIN_ELE1>
					<E1_PARCELA>Av<PERSON><PERSON>(EECGetFinParc(EEQ->EEQ_PARC),"E1_PARCELA")</E1_PARCELA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_TIPO'</FIN_ELE1>
					<E1_TIPO>'NF'</E1_TIPO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_CLIENTE'</FIN_ELE1>
					<E1_CLIENTE>EEQ->EEQ_IMPORT</E1_CLIENTE>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_LOJA'</FIN_ELE1>
					<E1_LOJA>EEQ->EEQ_IMLOJA</E1_LOJA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_EMISSAO'</FIN_ELE1>
					<E1_EMISSAO>If(FindFunction('AF200DtEmissa'),AF200DtEmissa(),EEC->EEC_DTEMBA)</E1_EMISSAO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_VENCTO'</FIN_ELE1>
					<E1_VENCTO>If(FindFunction('AF200DtVec'),AF200DtVec(),If(Empty(EEQ->EEQ_VCT),EEQ->EEQ_PGT,EEQ->EEQ_VCT))</E1_VENCTO>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_VENCREA'</FIN_ELE1>
					<E1_VENCREA>DataValida(If(FindFunction('AF200DtVec'),AF200DtVec(),If(Empty(EEQ->EEQ_VCT),EEQ->EEQ_PGT,EEQ->EEQ_VCT)),.T.)</E1_VENCREA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_VENCORI'</FIN_ELE1>
					<E1_VENCORI>If(FindFunction('AF200DtVec'),AF200DtVec(),If(Empty(EEQ->EEQ_VCT),EEQ->EEQ_PGT,EEQ->EEQ_VCT))</E1_VENCORI>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_VALOR'</FIN_ELE1>
					<E1_VALOR>EEQ->EEQ_VL - If(If(!GetMv('MV_EEC0056',.T.), GetMv('MV_AVG0214',,.F.), GetMv('MV_EEC0056',,.F.)), 0, EEQ->EEQ_CGRAFI)</E1_VALOR>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_DECRESC'</FIN_ELE1>
					<E1_DECRESC>0</E1_DECRESC>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_EMIS1'</FIN_ELE1>
					<E1_EMIS1>If(FindFunction('AF200DtEmissa'),AF200DtEmissa(),EEC->EEC_DTEMBA)</E1_EMIS1>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_TXMOEDA'</FIN_ELE1>
					<E1_TXMOEDA>If(FindFunction('AF200DtEmissa'),BuscaTaxa(EEQ->EEQ_MOEDA,AF200DtEmissa()),BuscaTaxa(EEQ->EEQ_MOEDA,dDataBase))</E1_TXMOEDA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_VLCRUZ'</FIN_ELE1>
					<E1_VLCRUZ>(EEQ->EEQ_VL - If(If(!GetMv('MV_EEC0056',.T.), GetMv('MV_AVG0214',,.F.), GetMv('MV_EEC0056',,.F.)), 0, EEQ->EEQ_CGRAFI))*(BuscaTaxa(EEQ->EEQ_MOEDA,If(FindFunction('AF200DtEmissa'),AF200DtEmissa(),dDataBase)))</E1_VLCRUZ>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_MOEDA'</FIN_ELE1>
					<E1_MOEDA>Posicione('SYF', 1, xFilial('SYF')+EEQ->EEQ_MOEDA, 'YF_MOEFAT')</E1_MOEDA>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_HIST'</FIN_ELE1>
					<E1_HIST>If(FindFunction('AF200HisEmb'),AF200HisEmb(),'Emb.:' + EEC->EEC_PREEMB)</E1_HIST>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_BCOCLI'</FIN_ELE1>
					<E1_BCOCLI>If(FindFunction('AF200Banco'),AF200Banco('1'),EEQ->EEQ_BANC)</E1_BCOCLI>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_NATUREZ'</FIN_ELE1>
					<!-- RRC - 22/08/2013 - Criado Parâmetro para o código da natureza do cliente no SIGAESS-->					
					<E1_NATUREZ>If(!Empty(Posicione("SA1", 1, xFilial("SA1")+EEQ->(EEQ_IMPORT+EEQ_IMLOJA), "A1_NATUREZ")), Posicione("SA1", 1, xFilial("SA1")+EEQ->(EEQ_IMPORT+EEQ_IMLOJA), "A1_NATUREZ"), If(cModulo=="ESS",GetMv("MV_ESS0018",,"EASY"),GetMv("MV_AVG0178",, "AVG")))</E1_NATUREZ>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
				<FIN_IT>
					<FIN_ELE1>'E1_ORIGEM'</FIN_ELE1>
					<E1_ORIGEM>INT101RetMod()</E1_ORIGEM>
					<FIN_ELE3>''</FIN_ELE3>
				</FIN_IT>
		        <FIN_IT>
			       <FIN_ELE1>'E1_ACRESC'</FIN_ELE1>
			       <E1_ACRESC>If( AVFLAGS("ACR_DEC_DES_MUL_JUROS_CAMBIO_EXP"), EEQ->EEQ_ACRESC, 0)</E1_ACRESC>
			       <FIN_ELE3>''</FIN_ELE3>
		        </FIN_IT>
		        <FIN_IT>
			       <FIN_ELE1>'E1_DECRESC'</FIN_ELE1>
			       <E1_DECRESC>If( AVFLAGS("ACR_DEC_DES_MUL_JUROS_CAMBIO_EXP"), EEQ->EEQ_DECRES, 0)</E1_DECRESC>
			       <FIN_ELE3>''</FIN_ELE3>
	            </FIN_IT>			
			</FIN_SEND>
		</DATA_SELECTION>
		<DATA_SEND>
			<SEND>EECINFIN(#TAG FIN_SEND#, 'SE1', 'INCLUIR',,,#TAG FIN_NUM#)</SEND>
		</DATA_SEND>
		<DATA_RECEIVE>
			<SRV_STATUS>.T.</SRV_STATUS>
		</DATA_RECEIVE>
	</SERVICE>
</EASYLINK>
