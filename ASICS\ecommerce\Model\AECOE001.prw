#include 'Protheus.ch'
#include 'parmtype.ch'
#include 'fwmvcdef.ch'

/*/{Protheus.doc} myFunction
(long_description)
@type    Function
<AUTHOR>
@since   27/08/2018
@version version
/*/
User Function AECOE001(cProdSku)

	Local aAreaAtu	:= GetArea()
	Local oBrowse

    Default cProdSku := ""

	oBrowse := FWMBrowse():New()
	oBrowse:SetAlias("Z72")
	oBrowse:SetDescription("Produtos Ecommerce")

	oBrowse:Activate()

	RestArea(aAreaAtu)

Return nil

/*/{Protheus.doc} myFunction
(long_description)
@type    Function
<AUTHOR>
@since   27/08/2018
@version version
/*/
Static Function MenuDef()

	Local aRotina	:= {}

	ADD OPTION aRotina TITLE "Pesquisar"	ACTION "PesqBrw"			OPERATION 1 ACCESS 0
	ADD OPTION aRotina TITLE "Visualizar"	ACTION "VIEWDEF.AECOE001"	OPERATION 1 ACCESS 0

Return aRotina

/*/{Protheus.doc} myFunction
(long_description)
@type    Function
<AUTHOR>
@since   22/08/2018TI   
@version version
/*/
Static Function ViewDef()

	Local oView			:= Nil
	Local oModel		:= FWLoadModel('AECOE001')

	Local oStructZ72	:= FWFormStruct(2, 'Z72')

	oView := FWFormView():New()

	oView:SetModel(oModel)
	oView:AddField( 'FIELDZ72', oStructZ72, 'FIELDZ72')

	oView:CreateHorizontalBox('BOXTOP',100)

	oView:SetOwnerView('FIELDZ72','BOXTOP')

	oView:EnableTitleView('FIELDZ72', 'Produto Ecommerce' )

	oView:SetCloseOnOk({||.T.})

Return oView

/*/{Protheus.doc} myFunction
(long_description)
@type    Function
<AUTHOR> Oliveira
@since   27/08/2018
@version version
/*/
Static Function ModelDef()

	Local oModel		:= Nil
	Local oStructZ72	:= FWFormStruct(1, 'Z72')

	oModel := MPFormModel():New('PRODECOMM')
	oModel:SetDescription('Produtos E-Commerce')

	oModel:addFields('FIELDZ72',,oStructZ72)

	oModel:SetPrimaryKey({})

	oModel:getModel('FIELDZ72'):SetDescription('Produto E-Commerce')
Return oModel