#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE "TBICONN.CH"

#DEFINE CRLF Chr(13) + Chr(10)

/*
������������������������������������������������������������������������������
������������������������������������������������������������������������������
��������������������������������������������������������������������������ͻ��
���Programa  � AFATM098  �Autor  �SYMM Consultoria    � Data �  22/04/2017 ���
��������������������������������������������������������������������������͹��
���Desc.     �Realiza a geracao do arquivo texto (.CSV) de clientes para   ���
���Desc.     �integracao com catalogo eletronico.						   ���
��������������������������������������������������������������������������͹��
���Uso       � ASICS - Brasil                                              ���
��������������������������������������������������������������������������ͼ��
������������������������������������������������������������������������������
������������������������������������������������������������������������������
*/
User Function AFATM098(cEmpTrab,cFilTrab,cHorario)

	Local cArquivo 	 	:= 'customers'
	Local cPathFTP 		:= ""
	Local cPathOut		:= ""
	Local cPathBkp		:= ""
	Local cEndFtp  		:= ""
	Local cUsuFtp  		:= ""
	Local cSenFtp  		:= ""
	Local aLogErros		:= {}
	Local aLogOk   		:= {}
	Local lExec			:= Nil

	Private cDirImp		:= "\debug\"
	Private cARQLOG		:= cDirImp + "AFATM098_CUSTOMERS" + cEmpTrab + "_" + cFilTrab + ".LOG"
	Private aRecno		:= {}


	//��������������������Ŀ
	//� Nao comer licenca. �
	//����������������������
	RPCSetType(3)

	PREPARE ENVIRONMENT EMPRESA cEmpTrab FILIAL cFilTrab

	cPathFTP 	:= GETMV("AS_FAT098A",,"/catalogo_eletronico/")
	cPathOut	:= GETMV("AS_FAT098B",,"/edi_catalogo/")
	cPathBkp	:= GETMV("AS_FAT098C",,"/edi_catalogo/Bkp/")
	cEndFtp  	:= GETMV("AS_FAT098D",,"ftp.asicsbrasil.com.br")
	cUsuFtp  	:= GETMV("AS_FAT098E",,"abr_catalogoasics")
	cSenFtp 	:= GETMV("AS_FAT098F",,"Abr@catalogo968")
	lExec		:= DTOS(GetMv("AS_FAT098G",,DTOS(DDATABASE))) <> DTOS(DDATABASE)


	MakeDir(cPathOut)
	MakeDir(cPathBkp)

	If Left(Time(),2) == Left(cHorario,2) .And. lExec

		PutMv("AS_FAT098G",DDATABASE)

		CONOUT("")
		LogExec(Replicate("-",80))
		LogExec("INICIADO ROTINA DE INTEGRACAO COM CATALOGO ELETRONICO - CUSTOMERS: AFATM098 - DATA/HORA: " + DTOC(DATE()) + " AS " + TIME())

		//Gera o arquivo em formato .CSV.
		cArquivo+= ".CSV"
		cPath 	:= cPathOut
		nHandle	:= FCreate(cPath + cArquivo)

		SyGeraArq()

		//Fecha o arquivo
		FClose(nHandle)

		//Envia arquivo para FTP
		FATM098A(cPath,{{cPathFTP}},cArquivo,.T.,cEndFtp,cUsuFtp,cSenFtp,@aLogErros,@aLogOk)

		LogExec("FINALIZADO ROTINA DE INTEGRACAO COM CATALOGO ELETRONICO - CUSTOMERS: AFATM098 - DATA/HORA: " + DTOC(DATE()) + " AS " + TIME())
		LogExec(Replicate("-",80))
		CONOUT("")

	EndIf


	RESET ENVIRONMENT

Return Nil

/*
������������������������������������������������������������������������������
������������������������������������������������������������������������������
��������������������������������������������������������������������������ͻ��
���Programa  �SyGeraArq  �Autor  �SYMM Consultoria    � Data �  22/04/2017 ���
��������������������������������������������������������������������������͹��
���Desc.     �      					 								   ���
��������������������������������������������������������������������������ͼ��
������������������������������������������������������������������������������
������������������������������������������������������������������������������
*/
Static Function SyGeraArq()

	Local aAreaAtu := GetArea()

	// Local cColNot	:= GetMv("AS_POLNOT",,"F18")
	// Local cColecAtu	:= U_SYRETCOL() //Retorna a cole��o atual.

	// /*------------------------------------------------------------------+
	// | @Autor: Lucas Oliveira | @Data: 08/2020 | @Hora: 16:10:08			|
	// | @Change: CHG0038137												|
	// | @Descri��o: Implementa��o de par�metro de filial principal		|
	// +------------------------------------------------------------------*/
	// Local cFilMain	:= AllTrim(U_MyNewSX6('AS_FILMAIN','05','C','C�digo da filial principal utilizada por padr�o',;
		// 															'C�digo da filial principal utilizada por padr�o',;
		// 															'C�digo da filial principal utilizada por padr�o',.F. ))

	cAlias := SyQryVendas()

	If Select(cAlias) > 0

		//cBuffer	:= "CODCLI;RAZAO_SOCIAL;NOME_FANTASIA;CNPJ;CODVEN;VENDEDOR;CODREP;REPRESENTANTE;CANAL;EMAIL_CLIENTE;EMAIL_REPRESENTANTE;MARKUP;MkpNGUA;MkpIGUA;MkpNNOR;MkpINOR"
		/*
			cBuffer +=  ALLTRIM((cAlias)->BLQ)+";"
			cBuffer +=  ALLTRIM((cAlias)->MOTBLQ)+";"
			cBuffer +=  ALLTRIM((cAlias)->DESCBLQ)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_END)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_COMPLEM)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_BAIRRO)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_EST)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_XGRUPOC)+";"
			cBuffer +=  ALLTRIM((cAlias)->Z01_DESCRI)
		*/
		cBuffer	:= "CODCLI;RAZAO_SOCIAL;NOME_FANTASIA;CNPJ;CODVEN;VENDEDOR;CODREP;REPRESENTANTE;CANAL;EMAIL_CLIENTE;EMAIL_REPRESENTANTE;CONDICAO_PAGAMENTO;MKP_NACIONAL_PRE;MKP_IMPORT_PRE;MKP_NACIONAL_PE;MKP_IMPORT_PE;BLQ;MOTBLQ;DESCBLQ;END;COMPLEMEM;BAIRRO;EST;GRUPOC;DESC_GRUPO;CANAL2"
		FWrite(nHandle, cBuffer + CRLF)

		ProcRegua((cAlias)->(RecCount()) )
		(cAlias)->( DbGotop() )
		While (cAlias)->( !Eof() )

			IncProc()

			cBuffer	:= ""
			cBuffer +=  (cAlias)->CODIGO+";"
			cBuffer +=  ArrumaChar((cAlias)->RAZAO_SOCIAL)+";"
			cBuffer +=  ArrumaChar((cAlias)->NOME_FANTASIA)+";"
			cBuffer +=  ALLTRIM((cAlias)->CNPJ)	+";"
			cBuffer +=  ALLTRIM((cAlias)->CODVEN)+";"
			cBuffer +=  ArrumaChar(ALLTRIM(UPPER((cAlias)->VENDEDOR)))+";"
			cBuffer +=  ALLTRIM((cAlias)->CODREP)+";"
			cBuffer +=  ArrumaChar(ALLTRIM(UPPER((cAlias)->REPRESENTANTE)))+";"
			cBuffer +=  ALLTRIM((cAlias)->CANAL)+";"
			cBuffer +=  ArrumaChar((cAlias)->EMAIL_CLI)+";"
			cBuffer += 	ArrumaChar((cAlias)->EMAIL_REP)+";"

			// If AllTrim(cColecAtu) $ AllTrim(cColNot)
			// 	cBuffer +=  STRTRAN(ALLTRIM((cAlias)->MARKUP),";",",")+";"

			// 	cBuffer+=STRTRAN(AllTrim(STR(0)),".",",")+";"
			// 	cBuffer+=STRTRAN(AllTrim(STR(0)),".",",")+";"

			// 	cBuffer+=STRTRAN(AllTrim(STR(0)),".",",")+";"
			// 	cBuffer+=STRTRAN(AllTrim(STR(0)),".",",")

			// Else

			// 	cBuffer +=  "0;"

			// 	//-------------------------------------------------------
			// 	// Andre Lanzieri 28/11/2018
			// 	// Retorna Mkp Guarulhos
			// 	//-------------------------------------------------------

			// 	cBuffer+=STRTRAN(AllTrim(STR(U_VA033MKP(cFilMain, cColecAtu, (cAlias)->A1_COD, (cAlias)->A1_LOJA, "", "PE", "0"))),".",",")+";"
			// 	cBuffer+=STRTRAN(AllTrim(STR(U_VA033MKP(cFilMain, cColecAtu, (cAlias)->A1_COD, (cAlias)->A1_LOJA, "", "PE", "1"))),".",",")+";"

			// EndIf
			//CHG0041093 - Ajuste de Markup
			cBuffer += ALLTRIM((cAlias)->COND_PGTO)+";"
			cBuffer += STRTRAN(AllTrim(STR((cAlias)->MKPPRENAC)),".",",")+";"
			cBuffer += STRTRAN(AllTrim(STR((cAlias)->MKPPREIMP)),".",",")+";"
			cBuffer += STRTRAN(AllTrim(STR((cAlias)->MKPPENACIO)),".",",")+";"
			cBuffer += STRTRAN(AllTrim(STR((cAlias)->MKPPEIMPOR)),".",",")+";"
			//----------------------------------------------
			// Leonardo Espinosa - 22/08/2022
			// CHG0041542 - Adi��o de novas colunas
			//----------------------------------------------
			cBuffer +=  ALLTRIM((cAlias)->BLQ)+";"
			cBuffer +=  ALLTRIM((cAlias)->MOTBLQ)+";"
			cBuffer +=  ArrumaChar((cAlias)->DESCBLQ)+";"
			cBuffer +=  ArrumaChar((cAlias)->A1_END)+";"//ALLTRIM((cAlias)->A1_END)+";"
			cBuffer +=  ArrumaChar((cAlias)->A1_COMPLEM)+";"//ALLTRIM((cAlias)->A1_COMPLEM)+";"
			cBuffer +=  ArrumaChar((cAlias)->A1_BAIRRO)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_EST)+";"
			cBuffer +=  ALLTRIM((cAlias)->A1_XGRUPOC)+";"
			cBuffer +=  ArrumaChar((cAlias)->Z01_DESCRI)+";"
			cBuffer +=  ALLTRIM((cAlias)->CANAL2)

			//-- Fim CHG0041542

			FWrite(nHandle, cBuffer + CRLF)
			(cAlias)->( dbSkip() )
		EndDo

	Endif
	(cAlias)->( dbCloseArea() )

	restArea(aAreaAtu)

Return Nil

/*
������������������������������������������������������������������������������
������������������������������������������������������������������������������
��������������������������������������������������������������������������ͻ��
���Programa  �SyQryVend  �Autor  �SYMM Consultoria    � Data �  23/06/2014 ���
��������������������������������������������������������������������������͹��
���Desc.     �															   ���
��������������������������������������������������������������������������ͼ��
������������������������������������������������������������������������������
������������������������������������������������������������������������������
*/
Static Function SyQryVendas()

	Local cQuery	:= ""
	Local cAlias	:= CriaTrab(,.F.)
	Local cQryPolit := ""

	//Local nMkpPad	:= SuperGetMv("AS_TMMKPAD", ,1.9)
	Local nMkpNac := U_MyNewSX6("AS_PMKPNAC","1.85","N","Valor de markup padr�o para clientes sem politica para produtos Nacionais",;
		"Valor de markup padr�o para clientes sem politica para produtos Nacionais",;
		"Valor de markup padr�o para clientes sem politica para produtos Nacionais", .F.)
		
	Local nMkpImp := U_MyNewSX6("AS_PMKPIMP","1.8","N","Valor de markup padr�o para clientes sem politica para produtos Importados",;
		"Valor de markup padr�o para clientes sem politica para produtos Importados",;
		"Valor de markup padr�o para clientes sem politica para produtos Importados", .F.)

	cQryPolit := " FROM "+RetSqlName("Z87")+" Z87 (NOLOCK) "+CRLF
	cQryPolit += " INNER JOIN "+RetSqlName("AYH")+" AYH (NOLOCK) ON "+CRLF
	cQryPolit += " AYH.AYH_FILIAL	 	= '"+xFilial("AYH")+"' "+CRLF
	cQryPolit += " AND AYH.AYH_CODIGO 	= Z87.Z87_COLECA  "+CRLF
	cQryPolit += " AND AYH.D_E_L_E_T_ 	= '' "+CRLF
	cQryPolit += " WHERE Z87.Z87_FILIAL = '"+xFilial("AYH")+"' "+CRLF
	cQryPolit += " AND Z87.Z87_FILCAR 	= '"+xFilial("SA3")+"' "+CRLF
	cQryPolit += " AND Z87.Z87_CLIENT 	= SA1.A1_COD "+CRLF
	cQryPolit += " AND Z87.Z87_LOJA 	= SA1.A1_LOJA "+CRLF
	cQryPolit += " AND Z87.D_E_L_E_T_ 	= ' ' "+CRLF
	cQryPolit += " ORDER BY AYH.AYH_DTINI DESC "+CRLF

	//���������������Ŀ
	//�Query consulta �
	//�����������������
	cQuery += "	SELECT	A.* FROM ( "+ CRLF
	cQuery += "	SELECT	A1_COD+A1_LOJA CODIGO, "+ CRLF
	cQuery += "			A1_NOME RAZAO_SOCIAL, "+ CRLF
	cQuery += "			A1_COD, "+ CRLF
	cQuery += "			A1_LOJA, "+ CRLF
	cQuery += "			A1_NREDUZ NOME_FANTASIA, "+ CRLF
	cQuery += "			A1_CGC CNPJ, "+ CRLF
	//chg0041797 - Pacote C - Cria��o de regras de vendedor
	// cQuery += "			A1_XSFVEND CODVEN, "+ CRLF
	// cQuery += "			SA3.A3_NOME VENDEDOR, "+ CRLF
	// cQuery += "			(CASE WHEN SA3.A3_SUPER<>'' THEN SA3.A3_SUPER ELSE A1_XSFVEND END) CODREP, "+ CRLF
	// cQuery += "			(CASE WHEN SA3.A3_SUPER<>'' THEN SA3A.A3_NOME ELSE SA3.A3_NOME END) REPRESENTANTE, "+ CRLF
	cQuery += "			SA1.A1_SUP2 CODVEN, "+ CRLF
	cQuery += "			SA3.A3_NOME VENDEDOR, "+ CRLF
	cQuery += "			SA1.A1_SUP1 CODREP, "+ CRLF
	cQuery += "			SA3A.A3_NOME REPRESENTANTE, "+ CRLF

	cQuery += "			A1_XCANAL1 CANAL, "+ CRLF
	cQuery += "			A1_XCANAL2 CANAL2, "+ CRLF
	cQuery += "			A1_EMAIL EMAIL_CLI, "+ CRLF
	//cQuery += "			A1_XREFER MARKUP, "+ CRLF
	cQuery += "			(CASE WHEN SA3.A3_EMAIL<>'' THEN SA3.A3_EMAIL ELSE SA3A.A3_EMAIL END) EMAIL_REP "+ CRLF
	//cQuery += "			,A1_COND COND_PGTO "+ CRLF //CHG0041668
	cQuery += "			,ISNULL(SE4.E4_CODIGO,'') +' - '+ ISNULL(E4_DESCRI,'') AS COND_PGTO "+CRLF
	cQuery += "			,ISNULL((SELECT TOP 1 Z87_MKPPRN "+ CRLF
	cQuery += cQryPolit
	cQuery += "			),"+AllTrim(STR(nMkpNac))+") MKPPRENAC "+ CRLF //MKP_NACIONAL_PRE
	cQuery += "			,ISNULL((SELECT TOP 1 Z87_MKPPEN "+ CRLF
	cQuery += cQryPolit
	cQuery += "			),"+AllTrim(STR(nMkpNac))+") MKPPENACIO "+ CRLF //MKP_NACIONAL_PE
	cQuery += "			,ISNULL((SELECT TOP 1 Z87_MKPPRI "+ CRLF
	cQuery += cQryPolit
	cQuery += "			),"+AllTrim(STR(nMkpImp))+") MKPPREIMP "+ CRLF //MKP_IMPORT_PRE
	cQuery += "			,ISNULL((SELECT TOP 1 Z87_MKPPEI "+ CRLF
	cQuery += cQryPolit
	cQuery += "			),"+AllTrim(STR(nMkpImp))+") MKPPEIMPOR "+ CRLF //MKP_IMPORT_PE
	//----------------------------------------------
	// Leonardo Espinosa - 22/08/2022
	// CHG0041542 Envio de informacoes adicionais
	//----------------------------------------------
	cQuery += "			,CASE WHEN A1_MSBLQL = '1' THEN 'S' ELSE 'N' END BLQ "+ CRLF
	cQuery += "			,A1_XMOTBLQ MOTBLQ "+ CRLF
	cQuery += "			,COALESCE(SX5.X5_DESCRI,'') DESCBLQ "+ CRLF
	cQuery += "			,A1_END"+ CRLF
	cQuery += "			,A1_COMPLEM "+ CRLF
	cQuery += "			,A1_BAIRRO "+ CRLF
	cQuery += "			,A1_EST "+ CRLF
	cQuery += "			,A1_XGRUPOC "+ CRLF
	cQuery += "			,COALESCE(Z01.Z01_DESCRI,'') Z01_DESCRI  "+ CRLF
	cQuery += "	FROM "+RetSqlName("SA1")+" SA1 (NOLOCK) "+ CRLF

	//chg0041797 - Pacote C - Cria��o de regras de vendedor
	// cQuery += "	INNER JOIN "+RetSqlName("SA3")+" SA3  (NOLOCK)  "+ CRLF
	// cQuery += "		ON A3_FILIAL      = '"+xFilial("SA3")+"' "+ CRLF
	// cQuery += "		AND A3_COD=A1_XSFVEND "+ CRLF
	// cQuery += "		AND SA3.D_E_L_E_T_ = ' ' "+ CRLF
	// cQuery += "	LEFT  JOIN "+RetSqlName("SA3")+" SA3A (NOLOCK) "+ CRLF
	// cQuery += "		ON SA3A.A3_FILIAL = '"+xFilial("SA3")+"' "+ CRLF
	// cQuery += "		AND SA3A.A3_COD=SA3.A3_SUPER  "+ CRLF
	// cQuery += "		AND SA3A.D_E_L_E_T_ = ' ' "+ CRLF
	cQuery += "	INNER JOIN "+RetSqlName("SA3")+" SA3  (NOLOCK)  "+ CRLF
	CQUERY += "		ON A3_FILIAL = '"+XFILIAL("SA3")+"' "+ CRLF
	CQUERY += "		AND A3_COD = A1_SUP2 "+ CRLF
	CQUERY += "		AND SA3.D_E_L_E_T_ = ' ' "+ CRLF
	CQUERY += "	INNER JOIN "+RETSQLNAME("SA3")+" SA3A (NOLOCK) "+ CRLF
	CQUERY += "		ON SA3A.A3_FILIAL = '"+XFILIAL("SA3")+"' "+ CRLF
	CQUERY += "		AND SA3A.A3_COD= SA1.A1_SUP1  "+ CRLF
	CQUERY += "		AND SA3A.D_E_L_E_T_ = ' ' "+ CRLF

	cQuery += "	LEFT  JOIN "+RetSqlName("SX5")+" SX5 (NOLOCK) "+ CRLF
	cQuery += "		ON SX5.X5_FILIAL = '"+xFilial("SX5")+"' "+ CRLF
	cQuery += "		AND SX5.X5_TABELA = 'Z6' "+ CRLF
	cQuery += "		AND SX5.X5_CHAVE = A1_XMOTBLQ  "+ CRLF
	cQuery += "		AND SX5.D_E_L_E_T_ = ' ' "+ CRLF
	cQuery += "	LEFT  JOIN "+RetSqlName("Z01")+" Z01 (NOLOCK) "+ CRLF
	cQuery += "		ON Z01.Z01_FILIAL = '"+xFilial("Z01")+"' "+ CRLF
	cQuery += "		AND Z01.Z01_GRUPO = A1_XGRUPOC  "+ CRLF
	cQuery += "		AND Z01.D_E_L_E_T_ = ' ' "+ CRLF
	cQuery += "		LEFT JOIN "+ RetSqlName("SE4") +" SE4 (NOLOCK) "+CRLF
	cQuery += "		ON SE4.E4_FILIAL = '"+ xFilial("SE4") +"' "+CRLF
	cQuery += "		AND SE4.E4_CODIGO = A1_COND "+CRLF
	cQuery += "		AND SE4.D_E_L_E_T_ = ' ' "+CRLF

	cQuery += "	WHERE "+ CRLF
	cQuery += "				A1_FILIAL		=  '"+xFilial("SA1")+"' "+ CRLF
	//chg0041797 - Pacote C - Cria��o de regras de vendedor
	//cQuery += "			AND A1_XSFVEND		<> ' ' "+ CRLF
	cQuery += "			AND A1_SUP2		<> ' ' "+ CRLF

	//----------------------------------------------
	// Leonardo Espinosa - 22/08/2022
	// CHG0041542 - Envio de atualiza��es dos clientes
	// Bloqueados
	//----------------------------------------------
	///cQuery += "			AND A1_MSBLQL		<> '1' "+ CRLF
	//--
	cQuery += "			AND SA1.D_E_L_E_T_	=  ' ' "+ CRLF
	cQuery += "			AND (SA1.A1_CGC <> ' ' OR SA1.A1_EST = 'EX') "+ CRLF
	// cQuery += "	ORDER BY A1_COD,A1_LOJA "+ CRLF
	cQuery += "	)A ORDER by BLQ DESC, CODIGO ASC "+ CRLF

	//dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlias,.T.,.T.)
	MpSysOpenQuery(cQuery, cAlias)

	If (cAlias)->( Eof() )
		LogExec("Nao existem dados a serem gerados")
		(cAlias)->( dbCloseArea() )
		Return(cAlias)
	EndIf

Return cAlias

/*���������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Programa  �LogExec	�Autor  � SYMM Consultoria   � Data �  11/07/13   ���
�������������������������������������������������������������������������͹��
���Desc.     �Gera log de execucao da rotina.                             ���
���          �                                                            ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
���������������������������������������������������������������������������*/
Static Function LogExec(cMsg)

CONOUT(cMsg)
LjWriteLog(cARQLOG,cMsg)
  	
Return .T.

/*/
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Programa  �ALJM07UP  � Autor �                    � Data �  09/11/15   ���
�������������������������������������������������������������������������͹��
���Descricao � Realiza o UpLoad de um ou mais arquivos no servidor FTP    ���
�������������������������������������������������������������������������͹��
���Uso       � ASICS                                                      ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
/*/

Static Function FATM098A(cPath,aDFtpEnv,cArqTransf,lBinario,cEndFtp,cUsuFtp,cSenFtp,aLogErros,aLogOk)

Local aDirectory := {}
Local nFile      := 0
Local nI         := 0
Local nVezes     := 0
Local cDFtpEnv   := ""
Local lOk        := .F.
Local cNomeTxtOk := ""
Local cPathBkp   := cPath+'\Bkp\'
Local nX         := 0

DEFAULT cArqTransf := ""

//���������������������������Ŀ
//�Conecta no servidor de FTP.�
//�����������������������������
IF !SyFTPConnect(cEndFtp,cUsuFtp,cSenFtp,"CONEXAO",lBinario,@aLogErros)
	Return(.F.)
EndIf

//���������������������������������Ŀ
//�Seta o diretorio de envio no FTP.�
//�����������������������������������
For nI := 1 To Len(aDFtpEnv)
	cDFtpEnv := ""
	For nX := 1 To Len(aDFtpEnv[nI])
		cDFtpEnv += Lower(aDFtpEnv[nI][nX])
		cDFtpEnv := StrTran(cDFtpEnv,"//","/")
		lOk := FTPDirChange(cDFtpEnv)
		InKey(5)
	Next nX
Next nI

If !lOk
	Aadd(aLogErros,Dtoc(Date())+" "+Time()+" Erro ao acessar o diretorio de envio. Diretorio: "+cDFtpEnv)
	ConOut( aLogErros[ Len(aLogErros) ] )
	Return(.F.)
Else
	lOk := .T.
Endif

//������������������������������������������������������������������Ŀ
//�Envia para o FTP todos os arquivos .MZP que estiverem no dretorio.�
//��������������������������������������������������������������������
IF Empty(cArqTransf)
	aDirectory := Directory(cPath + "*.CSV")
Else
	aDirectory := Directory(cPath + cArqTransf)
EndIF

ProcRegua(Len(aDirectory))
For nFile := 1 To Len(aDirectory)
	
	IncProc("Enviando Arquivo " + aDirectory[nFile,1])
	
	If File(cPath + aDirectory[nFile,1])
		
		nVezes := 1
		
		While nVezes <= 3
			
			ConOut(Dtoc(Date())+" "+Time()+" Executando UPLOAD do arquivo "+aDirectory[nFile,1]+" - Tentativa No."+AllTrim(Str(nVezes)))
			
			lOk := FTPUpLoad(cPath+aDirectory[nFile,1], aDirectory[nFile,1] )
			
			If lOk
				//�����������������������������������������������������������������������Ŀ
				//�Se conseguiu enviar, apaga o arquivo do diretorio de envio do Protheus.�
				//�������������������������������������������������������������������������
				
				cCopia  	:= cPath+aDirectory[nFile,1]
				cCopiax 	:= cPathBkp + 'BKP_' + AllTrim(aDirectory[nFile,1])
				__COPYFILE(cCopia,cCopiax)
				
				FErase(cPath + aDirectory[nFile,1])
				ConOut(Dtoc(Date())+" "+Time()+" Arquivo "+aDirectory[nFile,1]+" enviado com sucesso.")
				aAdd(aLogOk,Dtoc(Date())+" "+Time()+" Arquivo " + aDirectory[nFile,1] + " enviado com sucesso !")
				Exit
			Else
				Aadd(aLogErros,Dtoc(Date())+" "+Time()+" Erro ao enviar o arquivo "+aDirectory[nFile,1]+" - Tentativa No."+AllTrim(Str(nVezes)))
				ConOut( aLogErros[ Len(aLogErros) ] )
				nVezes++
				Inkey(5)
			EndIf
			
		EndDo
		
	EndIf
	
Next nFile

If (Len(aDirectory) == 0)
	Aadd(aLogErros,Dtoc(Date())+" "+Time()+" Nao existem arquivos para enviar.")
	lOk:= .F.
EndIf

//�����������������������������������������������������������������������Ŀ
//� Renomeio os arquivos para liberacao de download.                      �
//�������������������������������������������������������������������������
IF Empty(cArqTransf)
	
	nFile := 0
	For nFile := 1 To Len(aDirectory)
		
		nVezes := 1
		
		While nVezes <= 3
			
			cNomeTxtOk := Substr( aDirectory[nFile,1] ,	1 , Len(Alltrim(aDirectory[nFile,1]))-4 ) + ".MZP"
			
			If FTPRenameFile(aDirectory[nFile,1], cNomeTxtOk )
				ConOut(Dtoc(Date())+" "+Time()+" Arquivo renomeado com sucesso "+aDirectory[nFile,1]+" - Tentativa No."+AllTrim(Str(nVezes)))
				Exit
			Else
				Aadd(aLogErros,Dtoc(Date())+" "+Time()+" N�o foi possivel renomear o arquivo "+aDirectory[nFile,1]+" - Tentativa No."+AllTrim(Str(nVezes)))
				ConOut( aLogErros[ Len(aLogErros) ] )
				nVezes++
				Inkey(5)
			EndIF
			
		EndDo
		
	Next nFile
	
EndIF

//��������������������������������Ŀ
//�Seta o diretorio inicial do FTP.�
//����������������������������������
FTPDirChange('..')

//������������������Ŀ
//�Desconecta do FTP.�
//��������������������
SyFTPConnect(cEndFtp,cUsuFtp,cSenFtp,"DESCONEXAO",lBinario,@aLogErros)

Return(lOk)

/*/
�������������������������������������������������������������������������������
�������������������������������������������������������������������������������
���������������������������������������������������������������������������ͻ��
���Programa  �SyFTPConnect� Autor �                    � Data �  09/11/15   ���
���������������������������������������������������������������������������͹��
���Descricao � Funcoes para tratamento de FTP                               ���
���������������������������������������������������������������������������͹��
���Uso       � ASICS                                                        ���
���������������������������������������������������������������������������ͼ��
�������������������������������������������������������������������������������
�������������������������������������������������������������������������������
/*/

Static Function SyFTPConnect(cEndFtp,cUsuFtp,cSenFtp,cTipo,lBinario,aLogErros)

Local lRet 	 := .F.
Local nVezes := 0

DEFAULT lBinario := .T.

While nVezes <= 3
	
	IF cTipo == "CONEXAO"
		FTPDisconnect()
		lRet := FTPConnect(cEndFtp,21,cUsuFtp,cSenFtp)
	Else
		lRet := FTPDisconnect()
	EndIF
	
	IF !lRet
		Inkey(5)
		nVezes++
		IF cTipo == "CONEXAO"
			Aadd(aLogErros,Dtoc(Date())+" "+Time()+" Erro de conexao ao FTP. Servidor: " + cEndFtp + " Usuario: " + cUsuFtp + " Senha: " + cSenFtp + " - Tentativas: "+Alltrim(Str(nVezes)))
			ConOut( aLogErros[ Len(aLogErros) ] )
		Else
			Aadd(aLogErros,Dtoc(Date())+" "+Time()+" Erro ao desconectar do FTP. Servidor: " + cEndFtp + " Usuario: " + cUsuFtp + " Senha: " + cSenFtp + " - Tentativas: "+Alltrim(Str(nVezes)))
			ConOut( aLogErros[ Len(aLogErros) ] )
		EndIF
	Else
		IF cTipo == "CONEXAO"
			ConOut(Dtoc(Date())+" "+Time()+" Conexao ao FTP efetuada com sucesso.")
			
			// Determina que sera trasferido um arquivo binario
			IF lBinario
				FtpSetType(1)
			EndIF
		Else
			ConOut(Dtoc(Date())+" "+Time()+" Desconexao do FTP efetuada com sucesso.")
		EndIF
		Exit
	EndIF
	
EndDo

Return(lRet)

/*/{Protheus.doc} VA98ADebug
Fun��o para valida��o de altera��es de relat�rio
@type    Function
<AUTHOR> Oliveira
@since   05/10/2022
@version version
/*/
User Function VA98ADebug()
															
Return U_AFATM098("01","22",left(time(),2))

/*/{Protheus.doc} FM098sche
(long_description)
@type    Function
<AUTHOR> Oliveira
@since   14/10/2022
@version version
/*/
User Function FM098sche(aDados)

	Local lExec     := .F.
	Local cArquivo  := 'customers'
	Local cPath     := ""

	Local cPathFTP  := ""
	Local cPathOut  := ""
	Local cPathBkp  := ""
	Local cEndFtp   := ""
	Local cUsuFtp   := ""
	Local cSenFtp   := ""
	Local aLogErros := {}
	Local aLogOk    := {}

	Private cDirImp := "\debug\"
	Private cARQLOG := ""
	Private aRecno  := {}

	Default aData:={"01","22"}

	RPCSetType(3)
	RpcSetEnv(aData[1],aData[2])

	//CHG0041797 - Ajustes campo de email, convers�o para execu��o via Schedule
	lExec := U_MyNewSX6("AS_FAT098H",".T.","L","Se (.T.) ira executaro envio do cadastro de cliente para o Asics.ne",;
		"Se (.T.) ira executar o envio do cadastro de cliente para o Asics.net",;
		"Se (.T.) ira executar o envio do cadastro de cliente para o Asics.ne", .F.)
	
	cPathFTP 	:= GETMV("AS_FAT098A",,"/catalogo_eletronico/")
	cPathOut	:= GETMV("AS_FAT098B",,"/edi_catalogo/")
	cPathBkp	:= GETMV("AS_FAT098C",,"/edi_catalogo/Bkp/")
	cEndFtp  	:= GETMV("AS_FAT098D",,"ftp.asicsbrasil.com.br")
	cUsuFtp  	:= GETMV("AS_FAT098E",,"abr_catalogoasics")
	cSenFtp 	:= GETMV("AS_FAT098F",,"Abr@catalogo968")

	MakeDir(cPathOut)
	MakeDir(cPathBkp)

	If lExec

		cARQLOG	:= cDirImp + "AFATM098_CUSTOMERS" + cEmpAnt + "_" + cFilAnt + ".LOG"
	
		If LockByName("FM098sche"+cEmpAnt+cFilAnt,.F.,.F.)

			PutMv("AS_FAT098G",DDATABASE)

			CONOUT("")
			LogExec(Replicate("-",80))
			LogExec("INICIADO ROTINA DE INTEGRACAO COM CATALOGO ELETRONICO - CUSTOMERS: AFATM098 - DATA/HORA: " + DTOC(DATE()) + " AS " + TIME())

			//Gera o arquivo em formato .CSV.
			cArquivo+= ".CSV"
			cPath 	:= cPathOut
			nHandle	:= FCreate(cPath + cArquivo)

			SyGeraArq()

			//Fecha o arquivo
			FClose(nHandle)

			//Envia arquivo para FTP
			FATM098A(cPath,{{cPathFTP}},cArquivo,.T.,cEndFtp,cUsuFtp,cSenFtp,@aLogErros,@aLogOk)

			LogExec("FINALIZADO ROTINA DE INTEGRACAO COM CATALOGO ELETRONICO - CUSTOMERS: AFATM098 - DATA/HORA: " + DTOC(DATE()) + " AS " + TIME())
			LogExec(Replicate("-",80))
			CONOUT("")

			UnLockByName("FM098sche"+cEmpAnt+cFilAnt,.F.,.F.)
		Else
			Conout("FM098sche - N�o foi poss�vel iniciar a rotina, pois a mesma j� est� em execu��o")
		EndIf
	EndIf

return


/*/{Protheus.doc} ArrumaChar
Ajusta a string, removendo caracteres invalidos que possam quebrar a geração do CSV
@type function
@version 12.1.2210
<AUTHOR> Espinosa
@since 6/15/2023
/*/
Static Function ArrumaChar(cText)
	
	Local 	cRet 	:= ""

	cRet 	:= StrTran(cText,";","|")
	cRet 	:= StrTran(cRet	,'"',' ')
	cRet 	:= StrTran(cRet	,"'",' ')
	cRet 	:= StrTran(cRet	,",",' ')

Return AllTrim(cRet)
