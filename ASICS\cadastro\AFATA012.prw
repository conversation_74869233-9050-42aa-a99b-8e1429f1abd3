#Include 'Protheus.ch'

/*/{Protheus.doc} AFATA012
Efetua o cadastro do log na tabela SZL
<AUTHOR>
@since 10/05/2016
@version 1.0
@param aDados, array, Dados a serem cadastrados
/*/
User Function AFATA012(aDados)
	Local aBkpArea:=	GetArea()
	Local nX		:= 0

	DbSelectArea("Z40")
	RecLock('Z40',.T.)
	For nX := 1 To Len(aDados)
		cCampo	 := 'Z40->' + aDados[nX][1]
		&cCampo := aDados[nX][2]
	Next
	MsUnlock()

	RestArea(aBkpArea)
Return