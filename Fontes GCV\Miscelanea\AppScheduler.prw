#Include "Protheus.ch"

User Function AppScheduler(cRotina, cEmpTrab, cFilTrab, cIntervalo)

	Local aFilTrab := Separa(cFilTrab,",",.F.)
	Local nStep
	Local nCount
	Local nX
	
	Private aParam

	DEFAULT cIntervalo  := '60000' // 60000 milisegundos = 1 minuto
	
	nIntervalo := Val(cIntervalo)
	
	For nX:=1 To Len(aFilTrab)
		
		If KillApp()
			Return
		EndIf
		
		aParam := {cEmpTrab, aFilTrab[nX]}
		
		&(cRotina + "(aParam)")
	Next nX
	
	nStep  := 1
	nCount := nIntervalo/1000
	While !KillApp() .AND. nStep <= nCount
		Sleep(1000) //Sleep de 1 segundo
		nStep++
	EndDo
	
Return